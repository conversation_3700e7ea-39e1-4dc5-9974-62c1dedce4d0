Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-25T19:14:22.007878300+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-25T19:14:22.013480300+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-25T19:14:22.013480300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-25T19:14:22.013987400+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-25T19:14:22.017592800+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-07-25T19:14:22.018099300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-25T19:14:22.214102400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118688"
time="2025-07-25T19:14:22.219085700+08:00" level=info msg="Initial configuration complete, total time: 208ms"
time="2025-07-25T19:14:22.258239500+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-25T19:14:22.798681800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-25T19:14:23.025123200+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-25T19:14:23.026958800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-25T19:14:23.026958800+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:23.032826100+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-25T19:14:23.034893600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-25T19:14:23.034893600+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:23.078115300+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:24.458134800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:24.655489000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:24.655489000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:24.655489000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-25T19:14:24.655489000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-25T19:14:24.776495500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:24.893974000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:25.145203600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:25.145203600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-25T19:14:25.512514200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-25T19:14:25.512514200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-25T19:14:25.576987700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:25.576987700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:25.697487300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-25T19:14:25.829842400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:25.833465200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:27.088978300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-25T19:14:28.027210800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-25T19:14:28.027210800+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-25T19:14:28.033114700+08:00" level=error msg="PKM2 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:28.033114700+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:28.033114700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:28.033114700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:28.078343600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:28.078871600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:28.078871600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:28.078871600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:28.254978700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:28.398035500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:28.521728400+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-25T19:14:28.914552400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:28.914552400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-25T19:14:29.140351600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:29.144505000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:29.796982900+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:61520(msedgewebview2.exe) --> github.com:443 error: EOF"
time="2025-07-25T19:14:30.418832700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:61149(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-25T19:14:32.209523700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:61311(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-25T19:14:32.933959400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3232336463333361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:32.933959400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:33.459698100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:33.474591300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:33.483868500+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:61520(msedgewebview2.exe) --> github.com:443 error: failed to dial WebSocket: unexpected status: 503 Service Unavailable"
time="2025-07-25T19:14:33.928933500+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:61502(clash-verge.exe) --> github.com:443 error: context deadline exceeded"
time="2025-07-25T19:14:33.964991100+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:61520(msedgewebview2.exe) --> github.com:443 error: context deadline exceeded"
time="2025-07-25T19:14:34.163847800+08:00" level=warning msg="because 🇺🇸 美国 failed multiple times, active health check"
time="2025-07-25T19:14:34.163847800+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:61634(clash-verge.exe) --> github.com:443 error: failed to dial WebSocket: unexpected status: 503 Service Unavailable"
time="2025-07-25T19:14:35.270370800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:35.275188000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:35.484856900+08:00" level=error msg="Free-2 | 🇯🇵JP_1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-25T19:14:35.484856900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:36.020620700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:36.020620700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-25T19:14:37.210554700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:61826(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-25T19:14:37.216494500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:61831(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-25T19:14:37.316478700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:37.319111000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-25T19:14:37.616000400+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:62005(msedgewebview2.exe) --> github.com:443 error: failed to dial WebSocket: unexpected status: 503 Service Unavailable"
time="2025-07-25T19:14:38.112697100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:61937(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-25T19:14:39.639034200+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本樱花04丨直连【2x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:39.639034200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:41.784971100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6534636139383261 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:41.784971100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:41.944413400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:62565(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:41.945430500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:62566(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:42.069075500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:62590(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:42.069075500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:62589(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:43.022729000+08:00" level=error msg="Free-6 | ❓_🇦🇺_💻_github.com/Ruk1ng001_6339643963623836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:43.022729000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:43.027337700+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-25T19:14:43.118729400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62430(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-25T19:14:46.450497600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-25T19:14:46.450497600+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:46.451001700+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-25T19:14:46.455240400+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-25T19:14:46.455809000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-25T19:14:46.455809000+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:46.495145800+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-25T19:14:46.899268200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:62564(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-25T19:14:47.502138700+08:00" level=error msg="Free-2 | 🇯🇵JP_1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62716->*************:21829: use of closed network connection"
time="2025-07-25T19:14:47.502138700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:47.968988000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:47.968988000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T19:14:47.968988000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-25T19:14:47.968988000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-25T19:14:49.046682900+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-25T19:14:49.046682900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:49.244498000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-25T19:14:49.244498000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-25T19:14:49.415264600+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-25T19:14:49.624690200+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62933->*************:21829: use of closed network connection"
time="2025-07-25T19:14:49.624690200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:49.690955700+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62935->*************:21829: use of closed network connection"
time="2025-07-25T19:14:49.690955700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:49.734911700+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62934->*************:21829: use of closed network connection"
time="2025-07-25T19:14:49.734911700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:49.755448500+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:63063->*************:21829: use of closed network connection"
time="2025-07-25T19:14:49.755448500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:50.077989200+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62916->*************:21829: use of closed network connection"
time="2025-07-25T19:14:50.077989200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:50.713855800+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62908->*************:21829: use of closed network connection"
time="2025-07-25T19:14:50.713855800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:51.113301200+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6435396165323635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:51.113301200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:51.455278900+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:51.455278900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-25T19:14:51.455278900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:51.455278900+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-25T19:14:51.495742500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:51.495742500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:51.495742500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:51.495742500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-25T19:14:51.617489600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6535353732663333 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:51.617489600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:51.934377200+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-25T19:14:53.525497000+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-25T19:14:55.245899800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3939336438346637 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:55.245899800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:14:55.678541000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3232336463333361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:14:55.678541000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:15:00.332848100+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3136376638393361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:15:00.332848100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:15:01.081667300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-25T19:15:03.027805400+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-25T19:15:05.233003200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3137383833356432 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:15:05.233003200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:15:05.964117900+08:00" level=error msg="Free-6 | ❓_🇦🇺_💻_github.com/Ruk1ng001_6339643963623836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:15:05.964117900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T19:15:06.279888300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-25T19:15:06.279888300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-25T19:15:06.450616200+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-25T19:15:08.924646500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-25T19:15:09.042632300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-25T19:15:10.233484800+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6237633464653263 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-25T19:15:10.233484800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
