Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-28T19:23:23.850353200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-28T19:23:23.856538200+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-28T19:23:23.856538200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-28T19:23:23.856538200+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-28T19:23:23.860629300+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-07-28T19:23:23.860629300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-28T19:23:24.067764900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118476"
time="2025-07-28T19:23:24.072318300+08:00" level=info msg="Initial configuration complete, total time: 218ms"
time="2025-07-28T19:23:24.075411900+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-28T19:23:24.075411900+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-28T19:23:24.075411900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:23:24.097233100+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-28T19:23:24.787250300+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.787751400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:23:24.787751400+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.804380800+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.806067300+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.807082800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:23:24.807082800+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.819632600+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-07-28T19:23:24.852272500+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:23:24.859569400+08:00" level=warning msg="[Provider] applications not updated for a long time, force refresh"
time="2025-07-28T19:23:24.869335700+08:00" level=warning msg="[Provider] microsoft not updated for a long time, force refresh"
time="2025-07-28T19:23:24.901390100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-28T19:23:25.212206900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:25.212206900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:25.212206900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: couldn't find ip"
time="2025-07-28T19:23:25.212709200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/cncidr) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-07-28T19:23:25.279855000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:25.279855000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: couldn't find ip"
time="2025-07-28T19:23:25.279855000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:25.375165500+08:00" level=warning msg="[Provider] proxy not updated for a long time, force refresh"
time="2025-07-28T19:23:25.766816700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:23:25.766816700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:23:26.136470400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:23:26.185185100+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:23:26.296913100+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: cnsg.5b4cv6.ccddn4.icu:65400 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.5b4cv6.ccddn4.icu:65401 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.5b4cv6.ccddn4.icu:65123 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.359791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.815542600+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: couldn't find ip"
time="2025-07-28T19:23:26.815542600+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: couldn't find ip"
time="2025-07-28T19:23:26.815542600+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:26.916863100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:23:27.625485500+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:27.625485500+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:27.625485500+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:29.806770500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:29.806770500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:23:29.807782500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:29.807782500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:23:30.001609200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60466(svchost.exe) --> fe2cr.update.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:30.002117800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:30.036667100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:23:30.037173000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:23:30.039201300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:30.039711800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60477(svchost.exe) --> licensing.mp.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:23:30.041231900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:23:30.041739900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:23:30.042764100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60478(svchost.exe) --> v10.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:30.058217000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:23:30.066997600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:30.066997600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60451(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:30.131759300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: dial tcp ***************:65301: operation was canceled"
time="2025-07-28T19:23:30.223021600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:30.224524300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:23:30.224524300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:23:30.224524300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:23:30.251222300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:30.253251600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60466(svchost.exe) --> fe2cr.update.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:30.255105200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:23:30.263776000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:23:30.271720500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:23:30.282329800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60478(svchost.exe) --> v10.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:23:30.283699700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/gfw) mihomo --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:23:30.283699700+08:00" level=error msg="[Provider] applications pull error: Get \"https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt\": EOF"
time="2025-07-28T19:23:30.347436700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:30.351808700+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:30.380733800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> fastly.jsdelivr.net:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-28T19:23:30.380733800+08:00" level=error msg="[Provider] proxy pull error: Get \"https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt\": EOF"
time="2025-07-28T19:23:30.432613600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60606(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:30.465495600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60614(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:30.553270400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:23:30.560223600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:23:30.649788000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60606(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:23:30.673954800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60614(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:23:30.759202300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:30.892263900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60614(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:23:30.993205300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:31.000869800+08:00" level=error msg="MLY 免流云 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:23:31.000869800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:23:31.133640100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60614(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:31.201852900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:31.924029900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:23:31.924029900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-28T19:23:31.925084600+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: dial tcp ***************:65523: i/o timeout"
time="2025-07-28T19:23:31.925595800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65101 connect error: dial tcp ***************:65101: operation was canceled"
time="2025-07-28T19:23:31.926135400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: dial tcp ***************:65022: i/o timeout"
time="2025-07-28T19:23:31.926135400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-07-28T19:23:32.118949800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:23:32.228308700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:32.232449300+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: dial tcp ***************:65023: operation was canceled"
time="2025-07-28T19:23:32.232449300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:23:32.328488300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:23:32.422475000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:32.423496000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:32.428202100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60686(MicrosoftEdgeUpdate.exe) --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:32.524637300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:23:32.531411500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60682(svchost.exe) --> kv601.prod.do.dsp.mp.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:23:32.626599500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:23:32.631397000+08:00" level=error msg="[GEO] update GEO database error: can't download GeoSite database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat\": EOF"
time="2025-07-28T19:23:32.631397000+08:00" level=error msg="[GEO] Failed to update GEO database: can't download GeoSite database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat\": EOF"
time="2025-07-28T19:23:32.647379100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60686(MicrosoftEdgeUpdate.exe) --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:23:32.729012700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:23:32.745728500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60682(svchost.exe) --> kv601.prod.do.dsp.mp.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:23:32.821195800+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:23:32.821195800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:23:32.882793000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:23:32.971123900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60682(svchost.exe) --> kv601.prod.do.dsp.mp.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:23:32.972651700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:23:33.088857500+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:23:33.105340100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:23:33.193949100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60682(svchost.exe) --> kv601.prod.do.dsp.mp.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:23:33.469603200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: dial tcp ***************:65023: i/o timeout"
time="2025-07-28T19:23:33.469603200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: i/o timeout"
time="2025-07-28T19:23:33.471140300+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: dial tcp ***************:65523: i/o timeout"
time="2025-07-28T19:23:33.471140300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: dial tcp ***************:65501: i/o timeout"
time="2025-07-28T19:23:33.724345200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60478(svchost.exe) --> v10.events.data.microsoft.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-28T19:23:34.187666700+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:23:35.241079100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60614(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:23:35.780607300+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:37.328752400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60670(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-28T19:23:37.896675300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:23:37.896675300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:23:38.198475800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198475800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198475800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198475800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198976700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198976700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:38.198976700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:23:40.484260600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-28T19:23:41.840683800+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:23:50.935038700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:23:52.036286300+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:23:52.036286300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:24:06.521934400+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:24:10.224795800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:24:10.224795800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:24:10.224795800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:24:10.224795800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:24:10.384529400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6337663534343361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:24:10.384529400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:24:11.297477300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:24:11.297477300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:24:19.370419900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:24:24.053154700+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:24:41.002680800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:25:01.633073500+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6637383539383662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:25:01.633073500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:25:06.173113300+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:25:06.173113300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:25:26.867168600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:25:30.224956400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:25:30.224956400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:25:30.224956400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:25:30.224956400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:25:31.334467900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3232336463333361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:25:31.334467900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:25:31.742741300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:25:34.874671700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:25:46.629909500+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:25:58.076105000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:26:22.919840300+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:26:22.919840300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:26:48.243915500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3130323265623563 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:26:48.243915500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:26:51.064839000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:26:59.462038500+08:00" level=error msg="CT | 🇭🇰 专线香港-4 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:26:59.462038500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:03.783705500+08:00" level=error msg="PKM1 | 🇬🇧【欧洲】英国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:03.783705500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:04.512163600+08:00" level=error msg="PKM1 | 🇩🇪【欧洲】德国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:04.512163600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:06.586598200+08:00" level=error msg="PKM1 | 🇷🇺【欧洲】俄罗斯【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:06.586598200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:10.051445200+08:00" level=error msg="CC | 上海联通转香港HKC4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:10.051445200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:18.666869900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:27:26.667028500+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:26.667028500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:27.436559500+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:27.436559500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:30.547461400+08:00" level=error msg="PKM1 | 🇩🇪【欧洲】德国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:30.547461400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:30.824363800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:30.824363800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:27:35.144600500+08:00" level=error msg="PKM1 | 🇮🇸【欧洲】冰岛【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:27:35.144600500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:28:07.397642300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:28:10.225429300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:28:10.225429300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:28:10.225429300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:28:10.225429300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:28:12.305125300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:28:16.743361700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:28:16.743361700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:28:25.427439000+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:29.268276900+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:33.428666500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:28:35.664650800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:28:46.742141400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:28:46.742141400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:29:27.433831300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:29:50.375323300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:30:07.423294600+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:30:07.423294600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:30:29.700342700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3234623938643265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:30:29.700342700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:30:37.962334700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:30:51.296266000+08:00" level=error msg="PKM1 | 🇮🇹 【欧洲】意大利丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:30:51.296266000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:31:05.525784300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:31:13.525906700+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:31:13.525906700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:31:15.857790500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:31:15.857790500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:31:50.476798400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6561353930393261 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:31:50.476798400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:31:59.086387300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:32:21.283539100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:32:59.252432700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6561353930393261 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:32:59.252432700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:08.414835300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:33:19.772392900+08:00" level=error msg="PKM1 | 🇬🇧【欧洲】英国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:19.772392900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:22.803242200+08:00" level=error msg="PKM1 | 🇧🇬【欧洲】保加利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:22.803242200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:22.884466500+08:00" level=error msg="PKM1 | 🇮🇸【欧洲】冰岛【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:22.884466500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:25.452399000+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:27.842117300+08:00" level=error msg="PKM1 | 🇸🇦【中东】沙特阿拉伯【4x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:27.842117300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:27.892437100+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:27.892437100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:27.986816700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:33:30.225570900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:33:30.225570900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:33:30.225570900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:33:30.225570900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:33:30.650373600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650373600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650373600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650373600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650905600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650905600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:30.650905600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:33:31.405171500+08:00" level=error msg="CC | 广东移动转香港HKT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:31.405171500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:35.990607700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:33:37.139777600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:33:49.377341300+08:00" level=error msg="PKM1 | 🇷🇺【欧洲】俄罗斯【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:49.377341300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:49.792125100+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:49.792125100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:33:57.135202900+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:33:57.135202900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:34:09.268945200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-28T19:34:24.796128700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:34:47.463358200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:35:00.532901800+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:00.532901800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:01.896098200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:56767(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:01.896098200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:56768(clash-verge.exe) --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:01.898714300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:35:01.900250700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:35:01.961800300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-07-28T19:35:02.018865600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:35:02.021935100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:35:03.376947600+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:03.376947600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:06.039339900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:35:06.086567600+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-28T19:35:06.647122200+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6637383539383662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:06.647122200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:06.875065200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:35:06.876081200+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:35:06.879807000+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:35:06.885454100+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:35:06.893204600+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:35:06.893204600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:35:06.931638700+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:35:07.884283300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:35:08.041582200+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:35:08.124857000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:35:08.126144200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:35:08.126144200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:35:08.126144200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:35:08.378555700+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:35:08.471711100+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:35:08.592461100+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:35:08.603090800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: context canceled"
time="2025-07-28T19:35:08.603604900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-28T19:35:08.603604900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:35:08.699862900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:35:08.705608000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:35:08.706118300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:35:08.822306500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-28T19:35:08.826508900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-28T19:35:08.943151200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) **********:56193(verge-mihomo.exe) --> 192.227.152.86:61001 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-28T19:35:08.950907600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg7.cc.tjcct.xyz, not dl.sg7.baidu.com"
time="2025-07-28T19:35:09.057177900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:57427(msedge.exe) --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-28T19:35:09.057177900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-28T19:35:09.110999000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) **********:60600(verge-mihomo.exe) --> 107.167.18.102:8443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-28T19:35:09.114034400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) **********:56553(verge-mihomo.exe) --> 107.167.18.102:8443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-28T19:35:09.137293900+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: context canceled"
time="2025-07-28T19:35:09.137293900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-28T19:35:09.159325200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-28T19:35:09.185581200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.5b4cv6.ccddn4.icu:65400 connect error: dial tcp 163.177.223.137:65400: operation was canceled"
time="2025-07-28T19:35:09.374921200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: dial tcp ***************:65023: operation was canceled"
time="2025-07-28T19:35:09.374921200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:09.375428700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.5b4cv6.ccddn4.icu:65401 connect error: dial tcp 163.177.223.137:65401: operation was canceled"
time="2025-07-28T19:35:09.409882400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.5b4cv6.ccddn4.icu:65400 connect error: dial tcp 163.177.223.137:65400: operation was canceled"
time="2025-07-28T19:35:09.616125000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> 104.16.249.249:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:09.616125000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-28T19:35:09.696140300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-28T19:35:11.715738100+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> 104.16.249.249:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:11.715738100+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: dial tcp ***************:65023: operation was canceled"
time="2025-07-28T19:35:11.886104100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:11.886104100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:35:11.893622700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:11.893622700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:35:12.230870900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:12.233759800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:57427(msedge.exe) --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-28T19:35:12.390247300+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💼_github.com/Ruk1ng001_3662346364343063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:12.390247300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:12.913237900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:13.923975400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) **********:50811(verge-mihomo.exe) --> **************:8888 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:35:14.121408300+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:16.523828400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:57674(msedge.exe) --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-28T19:35:16.825404000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:16.825404000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:16.825404000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:35:16.825404000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:35:19.285195700+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: context canceled"
time="2025-07-28T19:35:19.438538200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:19.438538200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:19.450948200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:19.450948200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:19.450948200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:19.450948200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:19.450948200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:35:20.903703000+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:20.964815200+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) **********:56285(verge-mihomo.exe) --> *************:36600 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:35:21.197096600+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:21.525044200+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:64131(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-28T19:35:21.696272600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:57985(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-28T19:35:23.006803400+08:00" level=warning msg="[UDP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: dial tcp ***************:65401: operation was canceled"
time="2025-07-28T19:35:23.300281800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6636346264353037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:23.300281800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:26.931874200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-28T19:35:29.519866100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:35:32.821073300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:32.821073300+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:35:32.821073300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:32.829199400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:32.898540600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:32.899559500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:35.745906300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6436613836333164 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:35.745906300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:38.428558100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:38.428558100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:38.428558100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:35:38.430639600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:35:38.431149500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:38.431149500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:40.266813900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3130323265623563 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:35:40.266813900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:35:41.059984600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:35:42.866456600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:35:46.932258800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-28T19:35:48.930643300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:35:49.179250200+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:35:53.901376800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:53.901376800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:53.903397000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:53.904415600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:35:56.825496200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:56.825496200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:35:56.825496200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:35:56.825496200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:36:01.463421900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:36:01.463421900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:36:02.902443900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:36:02.902443900+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:36:06.805659700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:36:14.610582400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.611107400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.613199200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.613711100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.704044800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.706622200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.805903400+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:36:14.805903400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:36:14.856483700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.856993500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.859054500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.860073200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.907867600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:14.908917900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:36:25.740348800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:36:25.740348800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:36:26.562313600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:36:28.994710700+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:36:47.756606000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3439626230313037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:36:47.756606000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:00.568992100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:37:09.276780500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:37:12.063974200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6139646430646237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:12.063974200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:14.179455300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:37:14.179455300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:37:16.825620000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:37:16.825620000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:37:16.825620000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:37:16.825620000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:37:19.219183800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:37:19.219183800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:37:28.352627300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:37:37.468670100+08:00" level=error msg="MLY | JP-日本 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:37.469191200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:39.527710300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:39.681049800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:40.274018100+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:40.274018100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:40.666226900+08:00" level=error msg="CC | 上海电信转香港HKC4[M][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:40.666226900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:41.269697000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:41.370303700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:44.363930200+08:00" level=error msg="PKM1 | 🇷🇺【欧洲】俄罗斯【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:44.363930200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:44.977037900+08:00" level=error msg="PKM1 | 🇧🇬【欧洲】保加利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:44.977037900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:45.239241500+08:00" level=error msg="PKM1 | 🇨🇭【欧洲】瑞士【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:45.239241500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:45.383379700+08:00" level=error msg="PKM1 | 🇮🇸【欧洲】冰岛【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:45.383379700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:49.886605200+08:00" level=error msg="CC | 广州移动转香港HKC[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:49.886605200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:52.447598500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:52.452313600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:37:54.424306200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:37:54.424306200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:37:54.726252100+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:54.726252100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:37:55.163201400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:37:55.163201400+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:37:56.251903500+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:37:56.251903500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:38:05.063058300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:38:05.064088300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:05.065106000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:38:05.066124200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:38:05.067648500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:10.386273900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:10.386798500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:10.390974900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:10.390974900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:38:19.061444400+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:38:19.061444400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:38:23.295064100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:38:27.326791600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3462313237373137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:38:27.326791600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:38:45.029896500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:38:48.925213800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:38:48.925213800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:39:12.591063600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:39:12.591063600+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:39:32.293752200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:39:32.580951500+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:39:32.580951500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:39:37.681618400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:39:37.681618400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:39:37.683664100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:39:37.683664100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:39:38.247077900+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💼_github.com/Ruk1ng001_3662346364343063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:39:38.247077900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:39:49.735472400+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:39:54.659269800+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:39:56.826115200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:39:56.826115200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:39:56.826115200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:39:56.826115200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:40:01.186328700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3338626463343632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:40:01.186328700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:40:02.682674800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:40:02.682674800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:40:04.295079800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:40:07.746868200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:09.882435400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:12.295517500+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:40:12.295517500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:40:12.738400300+08:00" level=error msg="Free-6 FreeSub failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:40:12.738400300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:40:14.211391100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:40:14.211391100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:40:15.896631400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:15.896631400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:15.896631400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:15.897132900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:15.897132900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:15.897132900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:40:34.683805400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:40:34.683805400+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:40:40.288220300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:40:40.288220300+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:40:48.816779300+08:00" level=error msg="CC | 广东移动转日本NTT4[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:40:48.816779300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:40:54.574228100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:40:54.574228100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:40:54.576365800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:40:54.576365800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:40:55.630263300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-28T19:41:01.441622400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:41:01.442125700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
