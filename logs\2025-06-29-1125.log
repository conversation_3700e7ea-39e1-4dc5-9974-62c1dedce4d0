2025-06-29 11:25:15 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-06-29 11:25:15 INFO - [Setup] 初始化资源...
2025-06-29 11:25:15 INFO - [Setup] 初始化完成，继续执行
2025-06-29 11:25:15 INFO - [System] 应用就绪或恢复
2025-06-29 11:25:15 INFO - [Config] 生成运行时配置成功
2025-06-29 11:25:15 INFO - [Config] 开始验证配置
2025-06-29 11:25:15 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 11:25:15 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 11:25:15 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 11:25:15 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 11:25:16 INFO - [Config] -------- 验证结果 --------
2025-06-29 11:25:16 INFO - [Config] 验证成功
2025-06-29 11:25:16 INFO - [Config] -------- 验证结束 --------
2025-06-29 11:25:16 INFO - [Config] 配置验证成功
2025-06-29 11:25:16 INFO - [Setup] 清理冗余的Profile文件...
2025-06-29 11:25:16 INFO - Profile 文件清理完成: 总文件数=78, 删除文件数=0, 失败数=0
2025-06-29 11:25:16 INFO - [Setup] 启动时Profile文件清理完成
2025-06-29 11:25:16 INFO - [Service] 开始检查服务是否正在运行
2025-06-29 11:25:16 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-29 11:25:16 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:16 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:16 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-29 11:25:16 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-29 11:25:16 INFO - [Service] 服务正在运行
2025-06-29 11:25:16 INFO - [Core] 服务当前可用或看似可用，尝试通过服务模式启动/重装
2025-06-29 11:25:16 INFO - [Service] 开始检查服务是否需要重装
2025-06-29 11:25:16 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-29 11:25:16 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:16 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:16 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-29 11:25:16 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-29 11:25:16 INFO - 服务版本检测：当前=1.1.0, 要求=1.1.0
2025-06-29 11:25:16 INFO - 服务版本匹配，无需重装
2025-06-29 11:25:16 INFO - 正在尝试通过服务启动核心
2025-06-29 11:25:16 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-29 11:25:16 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:16 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:16 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-29 11:25:16 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-29 11:25:16 INFO - 检测到服务版本: 1.1.0, 要求版本: 1.1.0
2025-06-29 11:25:16 INFO - 服务版本匹配
2025-06-29 11:25:16 INFO - [Service] 开始检查服务是否正在运行
2025-06-29 11:25:16 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-29 11:25:16 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:16 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:16 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-29 11:25:16 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-29 11:25:16 INFO - [Service] 服务正在运行
2025-06-29 11:25:16 INFO - 服务已在运行且版本匹配，尝试使用
2025-06-29 11:25:16 INFO - 尝试使用现有服务启动核心 (IPC)
2025-06-29 11:25:16 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:16 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:18 INFO - [Service] IPC请求完成: 命令=StartClash, 成功=true
2025-06-29 11:25:18 INFO - [Service] 服务成功启动核心
2025-06-29 11:25:18 INFO - [Core] 服务模式成功启动核心
2025-06-29 11:25:18 INFO - [Tray] 创建系统托盘...
2025-06-29 11:25:18 INFO - 正在从AppHandle创建系统托盘
2025-06-29 11:25:18 INFO - 系统托盘创建成功
2025-06-29 11:25:18 INFO - [Tray] 系统托盘创建成功
2025-06-29 11:25:18 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-06-29 11:25:18 INFO - [Window] UI准备阶段更新: NotStarted, 耗时: 0ms
2025-06-29 11:25:18 INFO - [Timer] Initializing timer...
2025-06-29 11:25:18 INFO - [Timer] Refreshing 6 timer tasks
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=Rcvw1SCKmdqL, id=4, interval=1440min
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=RKzTK4d5rjIF, id=5, interval=1440min
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=RFHm8frTGpzz, id=6, interval=1440min
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=RqUQUTUdf5hK, id=1, interval=1440min
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=RqkaNnsWj6RG, id=2, interval=1440min
2025-06-29 11:25:18 INFO - [Timer] Adding task: uid=Rx9LL6kjRU3B, id=3, interval=360min
2025-06-29 11:25:18 INFO - [Timer] 已注册的定时任务数量: 6
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=RFHm8frTGpzz, interval=1440min, task_id=6
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=Rx9LL6kjRU3B, interval=360min, task_id=3
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=RKzTK4d5rjIF, interval=1440min, task_id=5
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=RqkaNnsWj6RG, interval=1440min, task_id=2
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=Rcvw1SCKmdqL, interval=1440min, task_id=4
2025-06-29 11:25:18 INFO - [Timer] 注册了定时任务 - uid=RqUQUTUdf5hK, interval=1440min, task_id=1
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置: uid=Rcvw1SCKmdqL
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置: uid=RqUQUTUdf5hK
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置: uid=RqkaNnsWj6RG
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置: uid=Rx9LL6kjRU3B
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置: uid=RFHm8frTGpzz
2025-06-29 11:25:18 INFO - [Timer] 需要立即更新的配置数量: 5
2025-06-29 11:25:18 INFO - [Timer] 立即执行任务: uid=Rcvw1SCKmdqL
2025-06-29 11:25:18 INFO - [Timer] 立即执行任务: uid=RqUQUTUdf5hK
2025-06-29 11:25:18 INFO - [Timer] 立即执行任务: uid=RqkaNnsWj6RG
2025-06-29 11:25:18 INFO - [Timer] 立即执行任务: uid=Rx9LL6kjRU3B
2025-06-29 11:25:18 INFO - [Timer] 立即执行任务: uid=RFHm8frTGpzz
2025-06-29 11:25:18 INFO - [Timer] Timer initialization completed
2025-06-29 11:25:18 INFO - [Timer] Running timer task for profile: RqUQUTUdf5hK
2025-06-29 11:25:18 INFO - [Timer] Running timer task for profile: RqkaNnsWj6RG
2025-06-29 11:25:18 INFO - [Timer] Running timer task for profile: Rcvw1SCKmdqL
2025-06-29 11:25:18 INFO - [Timer] 配置 Rcvw1SCKmdqL 是否为当前激活配置: false
2025-06-29 11:25:18 INFO - [Timer] Running timer task for profile: RFHm8frTGpzz
2025-06-29 11:25:18 INFO - [Timer] 配置 RFHm8frTGpzz 是否为当前激活配置: false
2025-06-29 11:25:18 INFO - [Config] [订阅更新] 开始更新订阅 RFHm8frTGpzz
2025-06-29 11:25:18 INFO - [Timer] Running timer task for profile: Rx9LL6kjRU3B
2025-06-29 11:25:18 INFO - [Config] [订阅更新] 开始更新订阅 Rcvw1SCKmdqL
2025-06-29 11:25:18 INFO - [Timer] 配置 RqUQUTUdf5hK 是否为当前激活配置: false
2025-06-29 11:25:18 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-06-29 11:25:18 INFO - [订阅更新] RFHm8frTGpzz 是远程订阅，URL: http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae
2025-06-29 11:25:18 INFO - [Timer] 配置 RqkaNnsWj6RG 是否为当前激活配置: false
2025-06-29 11:25:18 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-06-29 11:25:18 INFO - [Timer] 配置 Rx9LL6kjRU3B 是否为当前激活配置: true
2025-06-29 11:25:18 INFO - [订阅更新] Rcvw1SCKmdqL 是远程订阅，URL: https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9
2025-06-29 11:25:18 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:25:18 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-06-29 11:25:18 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:25:18 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-06-29 11:25:18 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:25:18 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-06-29 11:25:18 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:25:18 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-06-29 11:25:18 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:25:18 INFO - [Frontend] 启动过程中发现错误，加入消息队列: config_validate::success - 
2025-06-29 11:25:18 INFO - [Setup] 异步设置任务完成，耗时: 3.4310649s
2025-06-29 11:25:18 INFO - [Setup] 应用设置成功完成
2025-06-29 11:25:18 INFO - [Frontend] 发送1条启动时累积的错误消息
2025-06-29 11:25:18 INFO - [Window] 窗口已立即显示
2025-06-29 11:25:18 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-06-29 11:25:18 INFO - [Window] 窗口显示流程完成
2025-06-29 11:25:18 INFO - [订阅更新] 更新订阅配置成功
2025-06-29 11:25:18 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-29 11:25:18 INFO - [Timer] Timer task completed successfully for uid: Rcvw1SCKmdqL (took 516ms)
2025-06-29 11:25:18 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:25:19 INFO - UI加载阶段更新: Loading
2025-06-29 11:25:19 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-29 11:25:19 ERROR - error sending request
2025-06-29 11:25:19 ERROR - error sending request
2025-06-29 11:25:19 INFO - UI加载阶段更新: Loading
2025-06-29 11:25:19 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-29 11:25:19 INFO - UI加载阶段更新: DomReady
2025-06-29 11:25:19 INFO - [Window] UI准备阶段更新: DomReady, 耗时: 0ms
2025-06-29 11:25:19 INFO - [Service] 开始检查服务是否正在运行
2025-06-29 11:25:19 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-29 11:25:19 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 11:25:19 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 11:25:19 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-29 11:25:19 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-29 11:25:19 INFO - [Service] 服务正在运行
2025-06-29 11:25:19 INFO - UI加载阶段更新: ResourcesLoaded
2025-06-29 11:25:19 INFO - [Window] UI准备阶段更新: ResourcesLoaded, 耗时: 0ms
2025-06-29 11:25:19 INFO - 前端UI已准备就绪
2025-06-29 11:25:19 INFO - [Window] UI已标记为完全就绪
2025-06-29 11:25:19 INFO - [Window] UI已完全加载就绪
2025-06-29 11:25:19 INFO - [订阅更新] 更新订阅配置成功
2025-06-29 11:25:19 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-29 11:25:19 INFO - [Timer] Timer task completed successfully for uid: RqkaNnsWj6RG (took 1038ms)
2025-06-29 11:25:19 INFO - [订阅更新] 更新订阅配置成功
2025-06-29 11:25:19 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-29 11:25:19 INFO - [Timer] Timer task completed successfully for uid: RqUQUTUdf5hK (took 1229ms)
2025-06-29 11:25:21 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:25:21 ERROR - error sending request
2025-06-29 11:25:21 ERROR - error sending request
2025-06-29 11:25:21 ERROR - error sending request
2025-06-29 11:25:22 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-06-29 11:25:22 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-06-29 11:25:22 ERROR - [[Timer]] Failed to update profile uid Rx9LL6kjRU3B: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-06-29 11:25:24 ERROR - error sending request
2025-06-29 11:25:24 ERROR - error sending request
2025-06-29 11:25:24 ERROR - error sending request
2025-06-29 11:25:27 INFO - [订阅更新] 更新订阅配置成功
2025-06-29 11:25:27 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-29 11:25:27 INFO - [Timer] Timer task completed successfully for uid: RFHm8frTGpzz (took 8619ms)
2025-06-29 11:25:29 ERROR - error sending request
2025-06-29 11:25:29 ERROR - error sending request
2025-06-29 11:25:34 ERROR - error sending request
2025-06-29 11:25:34 ERROR - error sending request
2025-06-29 11:25:40 ERROR - error sending request
2025-06-29 11:25:40 ERROR - error sending request
2025-06-29 11:25:45 ERROR - error sending request
2025-06-29 11:25:45 ERROR - error sending request
2025-06-29 11:25:50 ERROR - error sending request
2025-06-29 11:25:50 ERROR - error sending request
2025-06-29 11:26:11 ERROR - error sending request
2025-06-29 11:26:11 ERROR - error sending request
2025-06-29 11:26:15 ERROR - error sending request
2025-06-29 11:26:15 ERROR - error sending request
2025-06-29 11:26:21 ERROR - error sending request
2025-06-29 11:26:21 ERROR - error sending request
2025-06-29 11:26:26 ERROR - error sending request
2025-06-29 11:26:26 ERROR - error sending request
2025-06-29 11:26:50 INFO - [Window] 开始创建/显示主窗口, is_show=false
2025-06-29 11:26:50 INFO - [Window] 静默模式启动时不创建窗口
2025-06-29 11:30:22 ERROR - error sending request
2025-06-29 11:30:22 ERROR - error sending request
2025-06-29 11:30:28 ERROR - error sending request
2025-06-29 11:30:28 ERROR - error sending request
2025-06-29 11:30:33 ERROR - error sending request
2025-06-29 11:30:33 ERROR - error sending request
2025-06-29 11:30:38 ERROR - error sending request
2025-06-29 11:30:38 ERROR - error sending request
2025-06-29 11:30:43 ERROR - error sending request
2025-06-29 11:30:43 ERROR - error sending request
2025-06-29 11:30:43 ERROR - error sending request
2025-06-29 11:30:48 ERROR - error sending request
2025-06-29 11:30:48 ERROR - error sending request
2025-06-29 11:30:53 ERROR - error sending request
2025-06-29 11:30:53 ERROR - error sending request
2025-06-29 11:30:55 ERROR - error sending request
2025-06-29 11:30:58 ERROR - error sending request
2025-06-29 11:31:04 ERROR - error sending request
2025-06-29 11:31:04 ERROR - error sending request
2025-06-29 11:31:09 ERROR - error sending request
2025-06-29 11:31:10 ERROR - error sending request
2025-06-29 11:31:14 ERROR - error sending request
2025-06-29 11:31:14 ERROR - error sending request
2025-06-29 11:31:14 ERROR - error sending request
2025-06-29 11:31:19 ERROR - error sending request
2025-06-29 11:31:19 ERROR - error sending request
2025-06-29 11:31:19 ERROR - error sending request
2025-06-29 11:31:24 ERROR - error sending request
2025-06-29 11:31:24 ERROR - error sending request
2025-06-29 11:31:29 ERROR - error sending request
2025-06-29 11:31:29 ERROR - error sending request
2025-06-29 11:31:34 ERROR - error sending request
2025-06-29 11:31:55 ERROR - error sending request
2025-06-29 11:31:55 ERROR - error sending request
2025-06-29 11:35:21 ERROR - error sending request
2025-06-29 11:35:21 ERROR - error sending request
2025-06-29 11:35:26 ERROR - error sending request
2025-06-29 11:35:26 ERROR - error sending request
2025-06-29 11:35:31 ERROR - error sending request
2025-06-29 11:35:31 ERROR - error sending request
2025-06-29 11:35:36 ERROR - error sending request
2025-06-29 11:35:36 ERROR - error sending request
2025-06-29 11:35:41 ERROR - error sending request
2025-06-29 11:35:41 ERROR - error sending request
2025-06-29 11:35:46 ERROR - error sending request
2025-06-29 11:35:46 ERROR - error sending request
2025-06-29 11:35:51 ERROR - error sending request
2025-06-29 11:35:51 ERROR - error sending request
2025-06-29 11:35:57 ERROR - error sending request
2025-06-29 11:35:57 ERROR - error sending request
2025-06-29 11:36:02 ERROR - error sending request
2025-06-29 11:36:02 ERROR - error sending request
2025-06-29 11:36:07 ERROR - error sending request
2025-06-29 11:36:07 ERROR - error sending request
2025-06-29 11:36:12 ERROR - error sending request
2025-06-29 11:36:12 ERROR - error sending request
2025-06-29 11:36:17 ERROR - error sending request
2025-06-29 11:36:17 ERROR - error sending request
2025-06-29 11:36:23 ERROR - error sending request
2025-06-29 11:36:23 ERROR - error sending request
2025-06-29 11:36:28 ERROR - error sending request
2025-06-29 11:36:28 ERROR - error sending request
2025-06-29 11:36:33 ERROR - error sending request
2025-06-29 11:36:33 ERROR - error sending request
2025-06-29 11:36:38 ERROR - error sending request
2025-06-29 11:36:38 ERROR - error sending request
2025-06-29 11:36:43 ERROR - error sending request
2025-06-29 11:36:43 ERROR - error sending request
2025-06-29 11:36:48 ERROR - error sending request
2025-06-29 11:36:48 ERROR - error sending request
2025-06-29 11:36:53 ERROR - error sending request
2025-06-29 11:36:53 ERROR - error sending request
2025-06-29 11:37:04 ERROR - error sending request
2025-06-29 11:37:04 ERROR - error sending request
2025-06-29 11:37:09 ERROR - error sending request
2025-06-29 11:37:09 ERROR - error sending request
2025-06-29 11:37:14 ERROR - error sending request
2025-06-29 11:37:14 ERROR - error sending request
2025-06-29 11:40:24 ERROR - error sending request
2025-06-29 11:40:24 ERROR - error sending request
2025-06-29 11:40:29 ERROR - error sending request
2025-06-29 11:40:30 ERROR - error sending request
2025-06-29 11:40:35 ERROR - error sending request
2025-06-29 11:40:35 ERROR - error sending request
2025-06-29 11:40:40 ERROR - error sending request
2025-06-29 11:40:40 ERROR - error sending request
2025-06-29 11:40:45 ERROR - error sending request
2025-06-29 11:40:45 ERROR - error sending request
2025-06-29 11:40:50 ERROR - error sending request
2025-06-29 11:40:50 ERROR - error sending request
2025-06-29 11:40:55 ERROR - error sending request
2025-06-29 11:40:55 ERROR - error sending request
2025-06-29 11:41:01 ERROR - error sending request
2025-06-29 11:41:01 ERROR - error sending request
2025-06-29 11:41:06 ERROR - error sending request
2025-06-29 11:41:06 ERROR - error sending request
2025-06-29 11:41:11 ERROR - error sending request
2025-06-29 11:41:11 ERROR - error sending request
2025-06-29 11:41:16 ERROR - error sending request
2025-06-29 11:41:16 ERROR - error sending request
2025-06-29 11:41:21 ERROR - error sending request
2025-06-29 11:41:21 ERROR - error sending request
2025-06-29 11:41:26 ERROR - error sending request
2025-06-29 11:41:26 ERROR - error sending request
2025-06-29 11:41:31 ERROR - error sending request
2025-06-29 11:41:31 ERROR - error sending request
2025-06-29 11:45:23 ERROR - error sending request
2025-06-29 11:45:23 ERROR - error sending request
2025-06-29 11:45:29 ERROR - error sending request
2025-06-29 11:45:29 ERROR - error sending request
2025-06-29 11:45:34 ERROR - error sending request
2025-06-29 11:45:34 ERROR - error sending request
2025-06-29 11:45:39 ERROR - error sending request
2025-06-29 11:45:39 ERROR - error sending request
2025-06-29 11:45:44 ERROR - error sending request
2025-06-29 11:45:44 ERROR - error sending request
2025-06-29 11:45:49 ERROR - error sending request
2025-06-29 11:45:49 ERROR - error sending request
2025-06-29 11:45:55 ERROR - error sending request
2025-06-29 11:45:55 ERROR - error sending request
2025-06-29 11:46:00 ERROR - error sending request
2025-06-29 11:46:00 ERROR - error sending request
2025-06-29 11:46:05 ERROR - error sending request
2025-06-29 11:46:05 ERROR - error sending request
2025-06-29 11:46:10 ERROR - error sending request
2025-06-29 11:46:10 ERROR - error sending request
2025-06-29 11:46:15 ERROR - error sending request
2025-06-29 11:46:15 ERROR - error sending request
2025-06-29 11:46:21 ERROR - error sending request
2025-06-29 11:46:21 ERROR - error sending request
2025-06-29 11:46:26 ERROR - error sending request
2025-06-29 11:46:26 ERROR - error sending request
2025-06-29 11:46:31 ERROR - error sending request
2025-06-29 11:46:31 ERROR - error sending request
2025-06-29 11:47:22 INFO - Tray点击事件: 显示主窗口
2025-06-29 11:47:22 INFO - [Window] 开始智能显示主窗口
2025-06-29 11:47:22 INFO - [Window] 开始激活窗口
2025-06-29 11:47:22 INFO - [Window] 窗口已最小化，正在取消最小化
2025-06-29 11:47:22 INFO - [Window] 窗口激活成功
2025-06-29 11:47:22 INFO - 窗口显示结果: Shown
2025-06-29 11:47:24 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:47:42 INFO - Tray点击事件: 显示主窗口
2025-06-29 11:47:42 INFO - [Window] 开始智能显示主窗口
2025-06-29 11:47:42 INFO - [Window] 开始激活窗口
2025-06-29 11:47:42 INFO - [Window] 窗口激活成功
2025-06-29 11:47:42 INFO - 窗口显示结果: Shown
2025-06-29 11:47:44 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:47:44 ERROR - error sending request
2025-06-29 11:47:44 ERROR - error sending request
2025-06-29 11:48:25 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:48:26 INFO - [Config] 开始更新配置
2025-06-29 11:48:26 INFO - [Config] 生成新的配置内容
2025-06-29 11:48:27 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 11:48:27 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 11:48:27 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 11:48:27 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 11:48:28 INFO - [Config] -------- 验证结果 --------
2025-06-29 11:48:28 INFO - [Config] 验证成功
2025-06-29 11:48:28 INFO - [Config] -------- 验证结束 --------
2025-06-29 11:48:28 INFO - [Config] 配置验证通过
2025-06-29 11:48:28 INFO - [Config] 生成运行时配置
2025-06-29 11:48:28 INFO - [Config] [订阅更新] 开始更新订阅 R1vf8GRLk2tM
2025-06-29 11:48:28 INFO - [订阅更新] R1vf8GRLk2tM 是远程订阅，URL: https://www.ccsub.org/link/0bq2vG3xxpKOWhUy?clash=1
2025-06-29 11:48:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 11:48:28 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-06-29 11:48:28 INFO - [Network] 正在重置所有HTTP客户端
2025-06-29 11:48:29 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-06-29 11:48:29 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-06-29 11:48:33 INFO - [Core] Configuration updated successfully
2025-06-29 11:48:33 INFO - Profile 文件清理完成: 总文件数=72, 删除文件数=0, 失败数=0
2025-06-29 11:48:33 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:48:37 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 11:49:20 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 11:49:20 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 12:00:05 INFO - Tray点击事件: 显示主窗口
2025-06-29 12:00:05 INFO - [Window] 开始智能显示主窗口
2025-06-29 12:00:05 INFO - [Window] 开始激活窗口
2025-06-29 12:00:05 INFO - [Window] 窗口激活成功
2025-06-29 12:00:05 INFO - 窗口显示结果: Shown
2025-06-29 12:00:43 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 12:00:44 INFO - [Config] 开始更新配置
2025-06-29 12:00:44 INFO - [Config] 生成新的配置内容
2025-06-29 12:00:44 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 12:00:44 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 12:00:44 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 12:00:44 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 12:00:45 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 12:00:45 INFO - [Config] -------- 验证结果 --------
2025-06-29 12:00:45 INFO - [Config] 验证成功
2025-06-29 12:00:45 INFO - [Config] -------- 验证结束 --------
2025-06-29 12:00:45 INFO - [Config] 配置验证通过
2025-06-29 12:00:45 INFO - [Config] 生成运行时配置
2025-06-29 12:00:46 ERROR - error sending request
2025-06-29 12:00:46 INFO - [Core] Configuration updated successfully
2025-06-29 12:11:24 ERROR - error sending request
2025-06-29 12:28:17 ERROR - error sending request
2025-06-29 12:47:06 ERROR - error sending request
2025-06-29 12:47:11 ERROR - error sending request
2025-06-29 12:47:11 ERROR - error sending request
2025-06-29 14:00:53 INFO - [Config] 开始更新配置
2025-06-29 14:00:53 INFO - [Config] 生成新的配置内容
2025-06-29 14:00:53 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 14:00:53 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 14:00:53 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 14:00:53 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 14:00:54 INFO - [Config] -------- 验证结果 --------
2025-06-29 14:00:54 INFO - [Config] 验证成功
2025-06-29 14:00:54 INFO - [Config] -------- 验证结束 --------
2025-06-29 14:00:54 INFO - [Config] 配置验证通过
2025-06-29 14:00:54 INFO - [Config] 生成运行时配置
2025-06-29 14:00:55 INFO - [Core] Configuration updated successfully
2025-06-29 14:03:20 INFO - [Config] 开始更新配置
2025-06-29 14:03:20 INFO - [Config] 生成新的配置内容
2025-06-29 14:03:20 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 14:03:20 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 14:03:20 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 14:03:20 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 14:03:21 INFO - [Config] -------- 验证结果 --------
2025-06-29 14:03:21 INFO - [Config] 验证成功
2025-06-29 14:03:21 INFO - [Config] -------- 验证结束 --------
2025-06-29 14:03:21 INFO - [Config] 配置验证通过
2025-06-29 14:03:21 INFO - [Config] 生成运行时配置
2025-06-29 14:03:23 INFO - [Core] Configuration updated successfully
2025-06-29 15:06:29 INFO - Tray点击事件: 显示主窗口
2025-06-29 15:06:29 INFO - [Window] 开始智能显示主窗口
2025-06-29 15:06:29 INFO - [Window] 开始激活窗口
2025-06-29 15:06:29 INFO - [Window] 窗口激活成功
2025-06-29 15:06:29 INFO - 窗口显示结果: Shown
2025-06-29 15:06:30 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 15:06:31 INFO - [Service] 开始检查服务是否正在运行
2025-06-29 15:06:31 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-29 15:06:31 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 15:06:31 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 15:06:31 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-29 15:06:31 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-29 15:06:31 INFO - [Service] 服务正在运行
2025-06-29 15:07:20 INFO - closing window...
2025-06-29 20:47:42 INFO - 窗口已隐藏
2025-06-29 20:47:42 INFO - [System] 开始异步清理资源
2025-06-29 20:47:42 INFO - [System] 开始执行异步清理操作...
2025-06-29 20:47:42 INFO - [Service] 通过服务停止核心 (IPC)
2025-06-29 20:47:42 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 20:47:42 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 20:47:42 INFO - 系统代理已重置
2025-06-29 20:47:42 INFO - TUN模式已禁用
2025-06-29 20:47:42 INFO - [Service] IPC请求完成: 命令=StopClash, 成功=true
2025-06-29 20:47:42 INFO - 核心服务已停止
2025-06-29 20:47:42 INFO - [System] 异步清理操作完成 - TUN: true, 代理: true, 核心: true, DNS: true, 总体: true
2025-06-29 20:47:42 INFO - [System] 资源清理完成，退出代码: 0
