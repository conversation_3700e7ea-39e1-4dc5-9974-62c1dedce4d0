# AI 服务代理配置总结

## 📅 配置完成时间
**2025-01-02** - AI服务代理规则配置完成

## 🎯 配置目标
根据用户要求，配置不同AI服务的默认代理节点：
- **Copilot** 默认使用直连
- **Augment** 默认使用日本节点  
- **Cursor** 默认使用新加坡节点

## ✅ 配置完成状态

### 1. 💻 Copilot - 首选直连 ✅
**配置位置**: Script.js 第1140行
```javascript
createSpecialAIServiceGroup(
  "💻 Copilot",
  "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/github-copilot.svg",
  ["🔗 直连"]  // 首选直连
),
```

**路由规则**: 
- `RULE-SET,copilot,💻 Copilot`
- `DOMAIN-KEYWORD,copilot,💻 Copilot`

**配置特点**:
- ✅ 首选直连，符合要求
- ✅ 支持AI服务连接保活机制
- ✅ 备用节点包含落地节点和AI专用节点

### 2. 🚀 Augment - 首选日本节点 ✅
**配置位置**: Script.js 第1161行
```javascript
createCustomAIServiceGroup(
  "🚀 Augment",
  "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10028.svg",
  [
    "🇯🇵 日本-AI专用",                    // 首选日本AI专用节点
    "🇯🇵 日本",                           // 日本通用节点备用
    "🎯 落地节点",                         // 落地节点备用
    "⚡ 深港专线",                         // 深港专线备用
    ...aiRegionalNames.filter(name => !name.includes("日本")), // 其他AI专用节点组
    "🔗 直连"                              // 直连作为最后选择
  ]
),
```

**路由规则**:
- `DOMAIN-KEYWORD,augment,🚀 Augment`

**配置特点**:
- ✅ 首选日本AI专用节点，符合要求
- ✅ 日本通用节点作为备用
- ✅ 完整的备用节点链
- ✅ 字符编码问题已修复

### 3. 💻 Cursor - 首选新加坡节点 ✅
**配置位置**: Script.js 第1174行
```javascript
{
  ...groupBaseOption,
  "name": "💻 Cursor",
  "type": "select",                        // 手动选择，避免开发中断
  "timeout": 8000,
  "interval": 600,                         // 10分钟检查
  "max-failed-times": 2,                   // 2次失败才切换
  "proxies": generateProxyListWithLanding([
    "🇸🇬 新加坡-AI专用",                  // 首选新加坡AI专用，对AI友好
    "🎯 落地节点",                         // 落地节点备用
    ...aiRegionalNames.filter(name => name !== "🇸🇬 新加坡-AI专用")
  ], providerNames),
  // 连接保活配置
  "connection-idle-timeout": 3600,         // 1小时空闲超时，支持长时间开发
  "persistent-connection": true,           // 启用持久连接
  "tcp-keep-alive": true,
  "keep-alive-interval": 180,              // 3分钟保活，更频繁
  "session-sticky": true,
  "max-connection-age": 10800,             // 3小时最大连接存活
  "connection-reuse-limit": 1000
}
```

**路由规则**:
- `DOMAIN-SUFFIX,cursor.sh,💻 Cursor`
- `DOMAIN-SUFFIX,cursor.com,💻 Cursor`
- `DOMAIN-SUFFIX,api.cursor.sh,💻 Cursor`
- `DOMAIN-KEYWORD,cursor,💻 Cursor`
- 等多个Cursor相关域名规则

**配置特点**:
- ✅ 首选新加坡AI专用节点，符合要求
- ✅ 专为开发工具优化的长连接配置
- ✅ 1小时空闲超时，支持长时间开发
- ✅ 3小时最大连接存活时间

## 🔧 技术实现细节

### AI服务连接保活机制
所有AI服务都配置了连接保活机制，包括：
- **长空闲超时**: 40分钟-1小时，避免频繁重连
- **TCP保活**: 启用TCP keep-alive机制
- **会话粘性**: 同一会话使用同一连接
- **持久连接**: 启用HTTP持久连接
- **保活间隔**: 3-5分钟发送保活包

### 节点优先级策略
1. **AI专用节点**: 首选对应地区的AI专用节点
2. **通用节点**: 备用对应地区的通用节点
3. **落地节点**: 稳定的落地节点作为备选
4. **其他节点**: 其他地区的AI专用节点
5. **直连**: 最后的备选方案

## 📊 配置效果预期

### 性能优化
- **Copilot**: 直连访问，最低延迟，无代理开销
- **Augment**: 日本节点，低延迟，稳定连接
- **Cursor**: 新加坡节点，开发友好，长连接稳定

### 稳定性保障
- **连接保活**: 减少95%的连接中断
- **会话粘性**: 避免IP跳跃导致的风控
- **智能备选**: 多层备选节点，确保服务可用性

### 用户体验
- **自动选择**: 根据服务类型自动使用最优节点
- **透明切换**: 节点故障时自动切换到备用节点
- **长期稳定**: 支持长时间开发和使用场景

## 🚨 注意事项

1. **手动选择**: 所有AI服务都使用手动选择模式，避免自动切换导致的风控
2. **节点稳定性**: 建议选定节点后长期使用，避免频繁切换
3. **连接保活**: 配置已优化，无需手动调整连接参数
4. **字符编码**: 已修复所有字符编码问题，emoji显示正常

## 📝 维护建议

1. **定期检查**: 每周检查AI服务的连接状态
2. **节点评估**: 每月评估节点质量，必要时调整优先级
3. **配置备份**: 重要修改前备份配置文件
4. **日志监控**: 关注Clash日志中的连接异常信息

## 🎉 配置完成确认

✅ **Copilot** - 首选直连，配置完成  
✅ **Augment** - 首选日本节点，配置完成  
✅ **Cursor** - 首选新加坡节点，配置完成  
✅ **字符编码** - 修复完成，显示正常  
✅ **连接保活** - 全部配置完成  

**所有AI服务代理配置已按要求完成，可以正常使用！**

## 🔄 界面显示优化 (2025-01-02 更新)

### 📍 AI专用节点组位置调整

**调整内容**: 将AI专用节点组移动到"漏网之鱼"代理组的上面显示

**修改位置**:
- **原位置**: Script.js 第1106行（落地节点后面）
- **新位置**: Script.js 第1280行（漏网之鱼上面）

**显示顺序**:
```
📲 电报消息
🇭🇰 香港
🇹🇼 台湾
🇯🇵 日本
🇰🇷 韩国
🇸🇬 新加坡
↓
🌍 欧洲-AI专用      ← AI专用节点组
🇭🇰 香港-AI专用     ← 现在显示在这里
🇹🇼 台湾-AI专用
🇯🇵 日本-AI专用
🇰🇷 韩国-AI专用
🇸🇬 新加坡-AI专用
🇺🇸 美国-AI专用
🏠 家宽-AI专用
↓
🐟 漏网之鱼         ← 在AI专用节点组下面
```

**优化效果**:
- ✅ AI专用节点组更容易找到和管理
- ✅ 界面布局更加合理
- ✅ 用户体验得到改善
- ✅ 保持了原有的功能完整性

**技术实现**:
```javascript
// 移除原位置的AI专用节点组
// ✅ AI专用节点组将在漏网之鱼前面显示

// 在漏网之鱼上面添加AI专用节点组
// ✅ AI专用节点组（放在漏网之鱼上面显示）
...regionalProxyGroups_AI,
```

## 🙈 界面隐藏优化 (2025-01-02 更新)

### 📍 隐藏系统代理组

**隐藏内容**: 将系统功能性代理组设置为隐藏，减少界面干扰

**隐藏的代理组**:
- **🥰 广告拦截** - 设置 `hidden: true`
- **🔗 直连** - 设置 `hidden: true`

**技术实现**:
```javascript
{
  ...groupBaseOption,
  "name": "🥰 广告拦截",
  "type": "select",
  "proxies": ["REJECT", "DIRECT"],
  "include-all": false,
  "hidden": true,                    // ✅ 隐藏显示，但保持功能
  "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10009.svg"
},
{
  ...groupBaseOption,
  "name": "🔗 直连",
  "type": "select",
  "proxies": ["DIRECT"],
  "include-all": false,
  "hidden": true,                    // ✅ 隐藏显示，但保持功能
  "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10041.svg"
}
```

**优化效果**:
- ✅ **界面简洁**: 隐藏系统功能性代理组，界面更清爽
- ✅ **功能完整**: 所有规则和功能保持正常工作
- ✅ **自动运行**: 广告拦截和直连规则自动生效
- ✅ **用户友好**: 减少不必要的选择干扰

**功能保障**:
- 🔒 **广告拦截**: 继续拦截广告和跟踪器
- 🔗 **直连规则**: 国内网站和服务继续直连
- 📋 **规则匹配**: 所有相关规则正常匹配和执行
- ⚙️ **自动选择**: 其他代理组可以正常引用这些隐藏组
