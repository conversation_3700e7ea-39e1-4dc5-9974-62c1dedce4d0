Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-23T19:13:50.306806600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-23T19:13:50.312962200+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-23T19:13:50.312962200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-23T19:13:50.312962200+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-23T19:13:50.473250700+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 19263"
time="2025-07-23T19:13:50.473250700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-23T19:13:50.675923100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119019"
time="2025-07-23T19:13:50.680048300+08:00" level=info msg="Initial configuration complete, total time: 370ms"
time="2025-07-23T19:13:50.703611200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-23T19:13:51.323805200+08:00" level=warning msg="[Provider] free-2 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.325422500+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.347035900+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.349589800+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.352136200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-23T19:13:51.352136200+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.360640500+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-23T19:13:51.360640500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-23T19:13:51.551885600+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-23T19:13:52.587234000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:52.587749900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:53.607105200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:53.607105200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T19:13:53.607105200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:54.258434600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:54.258434600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:54.482350800+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T19:13:54.870807500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:54.871308500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:55.585460000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:55.585460000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:55.980853600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.019990500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T19:13:56.095609000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.095609000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.231732200+08:00" level=error msg="Free-2 | 🇸🇬SG_2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-23T19:13:56.231732200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🚄 Landing-Fast-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🔗 Landing-HK-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.321691700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.321691700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.322239900+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.322239900+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.322239900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.322239900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.323757900+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.323757900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.323757900+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.323757900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.325784500+08:00" level=error msg="FL | 香港 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.325784500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.327303300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.349709300+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T19:13:56.349709300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:56.350724700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-23T19:13:56.350724700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T19:13:56.352750800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-23T19:13:56.352750800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T19:13:56.459460200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.467239700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.480536700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.612622700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.612622700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.612622700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.751667600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.753727300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.756719700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.878631800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.896726600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:56.975922000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.028583700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.046588600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.169134800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.223994000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.267257500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.333910200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_63333630656431 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:64097->************:44123: use of closed network connection"
time="2025-07-23T19:13:57.333910200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:13:57.546028100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T19:13:57.571222200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.572792700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:57.901991300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:58.180602400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:58.297202500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:58.301676100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:58.754354600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:59.236835000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:63942(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:59.416847700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:59.496120400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:59.769079600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:13:59.958009000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.099774100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.117284200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T19:14:00.117284200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T19:14:00.117284200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T19:14:00.117284200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T19:14:00.201927200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.232904200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.239486400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.313230000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.338366700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.347185800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.404022100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.439093000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.458572900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.472820800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:64440->************:3596: use of closed network connection"
time="2025-07-23T19:14:00.472820800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T19:14:00.477987400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.496270300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T19:14:00.514361500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.576738900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.579279800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.610974000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.634095400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.701451300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.714262400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.758415100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.784404500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.841622900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.892199000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:00.898903400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.018449900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.102637600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.106906600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.286697200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.288198100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.351254600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T19:14:01.364015300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.460925600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.789453700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.939795500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:01.990580000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.056138100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.122638700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.195499400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.264885500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.339080800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.473014400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.493974500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.791048600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64491(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.808533700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:02.826631300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.137609900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.252426600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.381422700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64614(OneDrive.exe) --> storage.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.381422700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.510768200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.572571100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.617247400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64568(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.649564500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.814634600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:03.912647200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64597(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.079700600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.177905200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.205577900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.287268200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.407529400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.546599200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.721125700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.811791200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:04.865974500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:05.136132200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64747(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:05.182304500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:05.487978800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:05.591866500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:64638(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-23T19:14:05.622311900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:05.819646600+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-23T19:14:05.835547400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:06.734987000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:64968(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:06.934964200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65073(svchost.exe) --> login.live.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:07.843712200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:07.952653400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.064097800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.186769500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.312337100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.532610900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.673539700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:08.844601400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:09.530051200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:09.879387400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65282(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:10.720963400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65386(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:10.836721200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65386(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:10.957971400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65386(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:10.987261200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65399(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:11.090569400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65386(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:11.105453300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65399(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:11.223346300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65399(msedge.exe) --> substrate.office.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T19:14:11.236281400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65386(CrossDeviceService.exe) --> default.exp-tas.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
