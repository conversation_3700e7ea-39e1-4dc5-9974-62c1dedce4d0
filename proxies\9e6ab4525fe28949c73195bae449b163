port: 7890
socks-port: 7891
mixed-port: 7892
lan-allowed-ips:
    - 0.0.0.0/0
    - ::/0
allow-lan: true
bind-address: '*'
mode: rule
unified-delay: true
log-level: warning
ipv6: true
external-controller: 0.0.0.0:9090
external-controller-cors:
    allow-origins:
        - '*'
    allow-private-network: true
external-ui: /root/.config/mihomo/ui
external-ui-url: https://github.com/MetaCubeX/metacubexd/archive/refs/heads/gh-pages.zip
external-ui-name: xd
geo-update-interval: 24
geodata-mode: true
geodata-loader: memconservative
tcp-concurrent: true
find-process-mode: strict
global-client-fingerprint: chrome
global-ua: clash.meta/1.10.0
etag-support: true
rule-providers:
    ai_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/ai_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/ai.txt
    apple_cdn:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_cdn.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/apple_cdn.txt
    apple_cn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_cn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/apple_cn.txt
    apple_services:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/apple_services.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/apple_services.txt
    cdn_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/cdn_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/cdn.txt
    cdn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/cdn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/cdn.txt
    china_ip:
        behavior: ipcidr
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/china_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/china_ip.txt
    china_ip_ipv6:
        behavior: ipcidr
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/china_ipv6.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/china_ip_ipv6.txt
    direct_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/direct_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/direct.txt
    domestic_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/domestic_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/domestic.txt
    domestic_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/domestic_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/domestic.txt
    download_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/download_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/download.txt
    download_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/download_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/download.txt
    global_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/global_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/global.txt
    lan_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/lan_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/lan.txt
    lan_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/lan_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/lan.txt
    microsoft_cdn_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/microsoft_cdn_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/microsoft_cdn.txt
    microsoft_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/microsoft_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/microsoft.txt
    neteasemusic_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/neteasemusic_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/neteasemusic.txt
    neteasemusic_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/neteasemusic_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/neteasemusic.txt
    reject_domainset:
        behavior: domain
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_domainset.txt
        type: http
        url: https://ruleset.skk.moe/Clash/domainset/reject.txt
    reject_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/reject.txt
    reject_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject.txt
    reject_non_ip_drop:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip_drop.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject-drop.txt
    reject_non_ip_no_drop:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/reject_non_ip_no_drop.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/reject-no-drop.txt
    sogouinput:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/sogouinput.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/sogouinput.txt
    stream_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/stream_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/stream.txt
    stream_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/stream_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/stream.txt
    telegram_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/telegram_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/ip/telegram.txt
    telegram_non_ip:
        behavior: classical
        format: text
        interval: 43200
        path: ./sukkaw_ruleset/telegram_non_ip.txt
        type: http
        url: https://ruleset.skk.moe/Clash/non_ip/telegram.txt
proxies:
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
      network: ws
      port: 8880
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3666373466643734
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      type: hysteria2
      udp: true
    - http-opts:
        headers:
            Host:
                - www.speedtest.net
        method: GET
        path:
            - /
      name: github.com/Ruk1ng001_6564303835626136
      network: http
      port: 11662
      server: finland.a1mshop2.xyz
      servername: www.speedtest.net
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 85ad8b72-7a9e-4603-abd2-1c5a553f9b4a
    - name: "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
      network: ws
      port: 8880
      server: *************
      servername: kjgx.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: /Telegram@WangCai2/?ed=2560
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
      network: ws
      port: 8880
      server: *************
      servername: kjgx.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: /Telegram@WangCai2/?ed=2560
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
      network: ws
      port: 8880
      server: ***************
      type: vmess
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_6665613065346164
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 923e41fe-1893-489b-b827-71364438e293
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6339646565373938
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
      network: ws
      port: 80
      server: ***************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - name: github.com/Ruk1ng001_6439643631343738
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e5987910-655a-4d1d-aab3-a6fc0ae31304
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - name: "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
      obfs: salamander
      obfs-password: Mzc5NTA3MjVlZDMyYTYyMA==
      password: d84be3ca-d0b6-4bd0-9b61-db86d7c65969
      port: 443
      server: *************
      skip-cert-verify: true
      sni: aparat.com
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6633616161383765
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
      network: ws
      password: fa050497-fc2a-45ee-89c0-96670c4ecb65
      port: 443
      server: *************
      skip-cert-verify: false
      sni: Rrr4.8906004.xYZ
      type: trojan
      ws-opts:
        headers:
            Host: Rrr4.8906004.xYZ
        path: /DZxb5QZyWgQPuXTwt
    - client-fingerprint: firefox
      name: "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: ************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      udp: true
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - name: github.com/Ruk1ng001_3436326136353835
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20957
      server: nexiateq.688997.xyz
      skip-cert-verify: true
      sni: nexiateq.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3463623035343238
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20357
      server: nexiahg.688997.xyz
      skip-cert-verify: true
      sni: nexiahg.688997.xyz
      tls: false
      type: hysteria2
    - cipher: aes-256-cfb
      name: "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      password: f8f7aCzcPKbsF8p3
      port: 989
      server: *************
      type: ss
      udp: true
    - name: github.com/Ruk1ng001_3139343834313837
      password: test.+
      port: 30445
      server: ctusa.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
      network: tcp
      port: 666
      server: *************
      tls: false
      type: vless
      udp: true
      uuid: 7445df1e-b5ec-46c3-ba7e-9f315b274c3e
    - name: github.com/Ruk1ng001_6435306164666165
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 4b6f7c4d-809d-4d89-9b6f-68f95fbd140d
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6437303763303333
      password: test.+
      port: 30443
      server: cuhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_65353737353831
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: a3321309-41bb-4d53-8028-d6d5ede426a8
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_3565383232353338
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: github.com/Ruk1ng001_3839643063653164
      network: ws
      port: 2096
      server: ipw.gfdv54cvghhgfhgj-njhgj64.info
      servername: 638890562127852879.amsterdam-prx-dnraaal.info
      tls: true
      type: vless
      udp: true
      uuid: 3056458b-913a-4193-b428-cdf22675b3b5
      ws-opts:
        headers:
            Host: 638890562127852879.amsterdam-prx-dnraaal.info
        path: /ydgmopws
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3536663336653435
      network: ws
      port: 30805
      server: v5.heduian.link
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: ocbc.com
        path: /oooo
    - client-fingerprint: chrome
      name: "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6434316432313361
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e283d4e4-cbee-45e1-ab19-8eb0c9927891
      port: 35310
      server: flyhg.flylink.cyou
      skip-cert-verify: true
      sni: flyhg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6136636466633939
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1d6feed2-6d2e-4509-a304-210de50a41d3
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3333373138336563
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e561798e-c51f-45c1-b0f6-036db4540205
      port: 35270
      server: flyels.flylink.cyou
      skip-cert-verify: true
      sni: flyels.flylink.cyou
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
      network: ws
      port: 8880
      server: ***************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_3839633563323161
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 7e2040cc-abbc-4f4c-8f7f-62fdd0f18aa1
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3835623631666464
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_3235333066353034
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 923e41fe-1893-489b-b827-71364438e293
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - alpn:
        - http/1.1
      name: github.com/Ruk1ng001_3464386330303961
      network: ws
      password: 0f7070cd-c91d-4532-a51f-56da4f0e94be
      port: 443
      server: iiiiop0.444752.xyz
      skip-cert-verify: false
      sni: iiiiop0.444752.xyz
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: iiiiop0.444752.xyz
        path: /ctHoQlqeZn8pbEUSLppj7jCmY
    - name: github.com/Ruk1ng001_3561353731633539
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3331373033323837
      password: test.+
      port: 30445
      server: cmusb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_6564633162393539
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_37323030643661
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 033932e3-7f3b-4c5c-96d1-49f525991abf
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      tls: false
      type: vless
      udp: true
      uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
      ws-opts:
        headers:
            Host: cf.d3z.net
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3533323030353139
      network: ws
      port: 80
      server: zoomgov.vipren.biz.id
      tls: false
      type: vless
      udp: true
      uuid: 00830f61-5f66-4157-9efd-c054a470ea58
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /*************=2053
    - name: "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3162613432376163
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 8007932c-b77f-4f02-b8c5-d63c19e4d124
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 64
      cipher: auto
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
      port: 50002
      server: **************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: github.com/Ruk1ng001_3738303331643362
      password: test.+
      port: 30443
      server: cthkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3332653730623238
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: f39a270d-c46e-4a39-a998-b33660291e7e
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
      network: ws
      port: 8880
      server: ************
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      name: "\U0001F534_\U0001F1E9\U0001F1EA_\U0001F4BC_github.com/Ruk1ng001_6564386238383439"
      port: 35879
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: deff1354-92b9-42cc-8ee6-46dead4e1c5d
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
      network: ws
      port: 443
      server: *************
      servername: EDfrT.frEEvPNatm2025.DPdNS.oRG
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
      ws-opts:
        headers:
            Host: EDfrT.frEEvPNatm2025.DPdNS.oRG
        path: /O9jlBCbIm3xr1D40NK
    - alpn:
        - http/1.1
      client-fingerprint: randomized
      name: github.com/Ruk1ng001_3539616164386266
      network: ws
      port: 443
      server: kifpool.me
      servername: mdrN.paGes.Dev
      tls: true
      type: vless
      udp: true
      uuid: 02efda19-4437-4d67-ad4f-3ca613dd80b1
      ws-opts:
        headers:
            Host: mdrn.pages.dev
        path: /kuF4zJYa4FBeudGb?ed=2560
    - client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: *************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      udp: true
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - name: github.com/Ruk1ng001_6235653130303737
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3736393163636436
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 923e41fe-1893-489b-b827-71364438e293
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6465373134613231
      password: test.+
      port: 40443
      server: cthka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
      port: 50002
      server: *************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - client-fingerprint: chrome
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_3331613266376662
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      type: hysteria2
      udp: true
    - alterId: 0
      cipher: auto
      country: "\U0001F3C1ZZ"
      h2-opts: {}
      http-opts: {}
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
      network: tcp
      port: 49597
      server: **************
      skip-cert-verify: true
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
      network: ws
      port: 8880
      server: ***********
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560/?JOKERRVPNBIA_CHANEL@JOKERRVPN
    - name: github.com/Ruk1ng001_3738653764333838
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e21fcd4e-1d46-4b16-b8f4-4c9c51d90879
      port: 25782
      server: hkhub.xg-hub.icu
      skip-cert-verify: true
      sni: hkhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6461656664646666
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 7dfb149a-47ee-4c98-8da4-26176d4ae5db
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3563646362656235
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      country: "\U0001F1E8\U0001F1F4CO"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_3531356165366663
      network: ws
      port: 443
      server: JJJjjjjjmMMmM.4444926.XYZ
      servername: jjjjjjjjmmmmm.4444926.xyz
      skip-cert-verify: true
      tls: true
      type: vmess
      uuid: dc50eb1d-244d-4711-b168-a101a5e6fb1b
      ws-opts:
        headers:
            HOST: jjjjjjjjmmmmm.4444926.xyz
        path: /awmqq79B17rfnpXiNaWb
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
      network: ws
      port: 8880
      server: ***************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_3634303766363039
      password: <password>
      port: 443
      server: 46977950-sxc9s0-t1bnjq-1krtb.hkt2.cdnhuawei.com
      tls: true
      type: socks5
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6133313834333934
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      type: hysteria2
      udp: true
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
      network: ws
      port: 8880
      server: ************
      servername: VngSuPpLY.IP-DdnS.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560
    - client-fingerprint: chrome
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
      network: ws
      password: f108e0e2-5f12-42b6-9e67-1b2f073ffb2b
      port: 443
      server: **************
      skip-cert-verify: false
      sni: CCcvfgt6.852224.dpdns.org
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: CCcvfgt6.852224.dpdns.org
        path: /CA5bMmr2JMum8sDKRwvFCJq
    - name: github.com/Ruk1ng001_6634356239386330
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6664623833366434
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_6332613833396439
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 139fe171-d1a8-4a08-abd5-81660cd9459b
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM
    - name: github.com/Ruk1ng001_3136363866356365
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 343bdaa2-2f89-4bee-80d4-2a797d8d13fe
      port: 35240
      server: flyyg.flylink.cyou
      skip-cert-verify: true
      sni: flyyg.flylink.cyou
      tls: false
      type: hysteria2
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
      network: ws
      port: 443
      server: ***********
      servername: ddDDdDDdDDF.777198.XyZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: c226ac5d-65e9-4379-95c3-fb542bc242d8
      ws-opts:
        headers:
            Host: ddDDdDDdDDF.777198.XyZ
        path: /OjdW89Bpg4ykd4O
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
      network: ws
      port: 443
      server: **************
      servername: eeEEEeR.666470.xYZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: 662e38ba-8427-4955-94aa-76f5347a0ce8
      ws-opts:
        headers:
            Host: eeEEEeR.666470.xYZ
        path: /6DuxYMYmrGrnGKRtF5UvWyyVQu
    - name: github.com/Ruk1ng001_32376632636230
      network: ws
      port: 80
      server: light-presence.oneeat.co
      type: vless
      uuid: 7e74ff43-3a90-48b3-8372-7d92a045c2d4
      ws-opts:
        headers:
            host: light-presence.oneeat.co
        path: /
    - cipher: aes-256-gcm
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
      password: 941cbc4237e0
      plugin: v2ray-plugin
      plugin-opts:
        host: kh36v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /tkzhhylcfuf
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_6632323936386239
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: c19a5f77-460c-4273-aea7-7666fb9a632a
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3435326535353033
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
      password: wl4HYN9PPKs8d8IzGIt4yKqt0
      port: 53348
      server: ************
      skip-cert-verify: true
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3466326438663839
      network: ws
      port: 443
      server: f3.askkf.com
      servername: s2.askkf.com
      tls: true
      type: vless
      uuid: 81fae892-37b3-47d5-d047-b44b3395fa38
      ws-opts:
        headers:
            Host: s2.askkf.com
        path: /
    - name: github.com/Ruk1ng001_6238323762663634
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: d4a98364-7b10-4ebd-b79b-7f045d66ff33
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6634363638653334
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
      password: 23d03aa704c7
      plugin: v2ray-plugin
      plugin-opts:
        host: cmihk3v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /grqhyifio
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - client-fingerprint: chrome
      name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: e204a396-5b17-46df-94e3-4373a088735d
      ws-opts:
        headers:
            Host: www.speedtest.net.tax.gov.ir.aparat.com.a.1.jEy.dPdNs.org.
        path: /Telegram@vpnjeyTelegram@vpnjeyTelegram@vpnjey
    - name: github.com/Ruk1ng001_3263653961396632
      password: test.+
      port: 30443
      server: cuhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3434623763646434
      network: ws
      port: 80
      server: SSSxXcvFtY.444752.Xyz
      servername: sssxxcvfty.444752.xyz
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 512d9674-db12-44ca-a1b5-654244549b65
      ws-opts:
        headers:
            Host: SSSxXcvFtY.444752.Xyz
        path: /uiAxvH6OkVk0VCfa7dX3JIrYk7zm
    - name: github.com/Ruk1ng001_6235616432666637
      obfs: salamander
      obfs-password: Telegram-->@Ln2Ray
      password: Telegram-->@Ln2Ray
      port: 45000
      server: gavazn.lnmarketplace.net
      skip-cert-verify: false
      sni: gavazn.55994421.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3334363461316530
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 87c96676-4887-432e-a1d7-89213be6333f
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6562633765653631
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 971cdb33-90c4-4df4-9df9-172710f22b2e
      port: 25782
      server: hkhub.xg-hub.icu
      skip-cert-verify: true
      sni: hkhub.xg-hub.icu
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
      password: 823e135ed103
      plugin: v2ray-plugin
      plugin-opts:
        host: jp7v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /dchlbvrbbotc
        skip-cert-verify: false
        tls: true
      port: 636
      server: **************
      tfo: false
      type: ss
    - client-fingerprint: chrome
      name: "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
      network: ws
      port: 2096
      server: **************
      servername: www.dollardoon.com
      tls: true
      type: vless
      udp: true
      uuid: 5fe4abb7-92e6-4390-a17c-fd887f2f1c93
      ws-opts:
        headers:
            Host: www.dollardoon.com
        path: /@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7?ed=2048
    - name: github.com/Ruk1ng001_3431316235386230
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3232383363316461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e6c29ae0-f573-4dca-9b02-b4133edd7e38
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6139346637303337
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 338bb7db-e7e0-4279-bf97-ff21b4c3fc67
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_35303137353031
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3330643631376666
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 318f9f6e-deb6-4a90-b370-8c0922d9250f
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 2
      cipher: auto
      country: "\U0001F1ED\U0001F1F0 HK"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_3635646561333861
      network: ws
      port: 80
      server: 228c7ba5-sz8sg0-szi8d6-1ro8s.hk.p5pv.com
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      type: vmess
      uuid: a18ef18e-293a-11ef-b76e-f23c91cfbbc9
      ws-headers:
        HOST: broadcastlv.chat.bilibili.com
      ws-path: /
    - name: github.com/Ruk1ng001_6439373666393161
      password: test.+
      port: 30445
      server: cuusa.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
      network: ws
      port: 8880
      server: ***************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_3264656135663034
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 033932e3-7f3b-4c5c-96d1-49f525991abf
      port: 25781
      server: jphub.xg-hub.icu
      skip-cert-verify: true
      sni: jphub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6335313363343366
      network: ws
      port: 8880
      server: ::ffff:**************
      servername: sg.laoyoutiao.link
      skip-cert-verify: true
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: sg.laoyoutiao.link
        path: /Telegram@WangCai2/?ed=2048
    - name: github.com/Ruk1ng001_3432613333366433
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0e122b6d-67ea-4243-a7fb-641a544c7bf0
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 2
      cipher: auto
      ip-version: dual
      name: github.com/Ruk1ng001_3264323232653631
      network: ws
      port: 30835
      server: v35.heduian.link
      type: vmess
      udp: true
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            host: baidu.com
        path: /oooo
    - name: github.com/Ruk1ng001_3864663936653262
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - alpn:
        - http/1.1
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
      network: ws
      password: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
      port: 443
      server: *************
      skip-cert-verify: false
      sni: rrrRrRRrT.459.pp.ua
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: rrrRrRRrT.459.pp.ua
        path: /znQImc22ijDwVOkZfoq
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_37646339616265
      network: ws
      port: 80
      server: 212f8cd0-t0bog0-t1nwsn-1la2q.hkt.tcpbbr.net
      tls: false
      type: vmess
      udp: true
      uuid: 431026c8-7397-11ed-a8bf-f23c91cfbbc9
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560/?@vpnserverrr_@vpnserverrr_vpnserverrr_@vpnserverrr_@vpnserverrr
    - name: github.com/Ruk1ng001_6462316532396330
      password: test.+
      port: 40443
      server: cuhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3863393430613433
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      type: hysteria2
      udp: true
    - client-fingerprint: chrome
      name: "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
      network: ws
      port: 80
      server: ************
      servername: blaze-can-118.blazecanada.site
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 3ac2de34-47c5-4dd5-afc0-8fb4b05d4077
      ws-opts:
        headers:
            Host: blaze-can-118.blazecanada.site
        path: /?ed=2560
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
      network: ws
      port: 443
      server: **************
      servername: ddDDdDDdDDF.777198.XyZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: c226ac5d-65e9-4379-95c3-fb542bc242d8
      ws-opts:
        headers:
            Host: ddDDdDDdDDF.777198.XyZ
        path: /OjdW89Bpg4ykd4O
    - name: "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
      network: tcp
      port: 443
      server: ************
      servername: cs2.pishima.ir
      tls: true
      type: vless
      uuid: e67025a0-278f-4208-8690-cac092b82306
    - name: github.com/Ruk1ng001_6464323733386635
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3835616365363435
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20357
      server: nexiahg.688997.xyz
      skip-cert-verify: true
      sni: nexiahg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6239323437633439
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: e561798e-c51f-45c1-b0f6-036db4540205
      port: 35260
      server: flyxghy.flylink.cyou
      skip-cert-verify: true
      sni: flyxghy.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6437306131613231
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6636333131363533
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3631303430363134
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3732393764363436
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: firefox
      name: "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
      network: tcp
      port: 34045
      reality-opts:
        public-key: cDaDzPr3PlS3NM8lreHZbdo-Mhqz8vMBzMSkHXhGIUA
        short-id: e8ab71d0
      server: **************
      servername: visit-this-invitation-link-to-join-tg-enkelte-notif.ekt.me
      tls: true
      type: vless
      uuid: d8dd94fd-540e-461d-b5d4-acebef02c22a
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
      password: 203d1d64-3313-11ed-bb74-f23c9164ca5d
      port: 8443
      server: **************
      sni: 0e1462f1-sum4g0-t8ro7t-1ey07.hy2.gotochinatown.net
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3330336130613830
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 591e50da-9904-467e-94e8-397a35c4f4a6
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
      port: 59003
      server: *************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - name: github.com/Ruk1ng001_6664383662343533
      network: ws
      port: 80
      server: vizzpn.freenet.channel.vizzfrag.ir
      tls: false
      type: vless
      udp: true
      uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /**************=443
    - name: github.com/Ruk1ng001_3964393736633237
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 318f9f6e-deb6-4a90-b370-8c0922d9250f
      port: 20757
      server: nexiaels.688997.xyz
      skip-cert-verify: true
      sni: nexiaels.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_31633031356434
      network: ws
      port: 80
      server: famous-experience.seotoolsforyou.co.uk
      tls: false
      type: vless
      udp: true
      uuid: 7f09e7b4-4680-4653-a2da-307d110b6961
      ws-opts:
        headers:
            Host: famous-experience.seotoolsforyou.co.uk
        path: /
    - name: github.com/Ruk1ng001_3562653962643961
      password: test.+
      port: 30443
      server: cmhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
      network: ws
      port: 8880
      server: **************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3466663135313065
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: a65c5cf2-9681-4ebc-904e-11fee411fe6c
      port: 35250
      server: flyhl.flylink.cyou
      skip-cert-verify: true
      sni: flyhl.flylink.cyou
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3961633062323636
      network: ws
      port: 80
      server: c1800c19-t07z40-teqqcv-1rc6n.hkt.tcpbbr.net
      skip-cert-verify: false
      tls: false
      type: vmess
      udp: true
      uuid: 32bd96d6-186b-11f0-9a65-f23c9164ca5d
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_3939313435643833
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_6235353439613934
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 318f9f6e-deb6-4a90-b370-8c0922d9250f
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6434363231303762
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_62363830373766
      network: ws
      port: 80
      server: aio.loophole.biz.id
      tls: false
      type: vless
      udp: true
      uuid: f282b878-8711-45a1-8c69-5564172123c1
      ws-opts:
        headers:
            Host: aio.loophole.biz.id
        path: /aioproxybot/*************-18650
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
      password: 70a0b8103356
      plugin: v2ray-plugin
      plugin-opts:
        host: jp5v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /bezradrqccl
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
      network: ws
      port: 8880
      server: ***************
      servername: VngSuPpLY.IP-DdnS.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /Etb1L6YUdZFZuTOr?ed=2560
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
      network: ws
      port: 80
      server: ***************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - name: github.com/Ruk1ng001_62396364396633
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3162626630326566
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3565393236356239
      password: test.+
      port: 40443
      server: cuhkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
      network: ws
      port: 443
      server: **************
      servername: EDfrT.frEEvPNatm2025.DPdNS.oRG
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
      ws-opts:
        headers:
            Host: EDfrT.frEEvPNatm2025.DPdNS.oRG
        path: /O9jlBCbIm3xr1D40NK
    - name: github.com/Ruk1ng001_3833633361613064
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6464373535616561
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 139fe171-d1a8-4a08-abd5-81660cd9459b
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3763326462356666
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 4565f544-8ba3-40ae-b4b2-8aa67d62987d
      port: 25780
      server: ushub.xg-hub.icu
      skip-cert-verify: true
      sni: ushub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3462373763626437
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20557
      server: nexiadg.688997.xyz
      skip-cert-verify: true
      sni: nexiadg.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3865303030376334
      network: ws
      port: 80
      server: 5019a345-t049s0-t1kmeg-mv7m.hkt.tcpbbr.net
      tls: false
      type: vmess
      udp: true
      uuid: 94d40708-8273-11ea-8fc9-f23c913c8d2b
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - alterId: 0
      cipher: auto
      name: "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
      network: ws
      port: 25808
      server: ***********
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 8fd16171-8c78-475c-80fe-a6f66b310bf3
      ws-opts:
        path: /
    - name: github.com/Ruk1ng001_3865353236366537
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 7dfb149a-47ee-4c98-8da4-26176d4ae5db
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3533643165623038
      password: test.+
      port: 40443
      server: cthkb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - client-fingerprint: chrome
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
      network: ws
      port: 8443
      server: ************
      servername: ovhwuxian.pai50288.uk
      tls: true
      type: vless
      udp: true
      uuid: 57ba2ab1-a283-42eb-82ee-dc3561a805b8
      ws-opts:
        headers:
            Host: ovhwuxian.pai50288.uk
        path: /57ba2ab1
    - alterId: 2
      cipher: auto
      country: "\U0001F1ED\U0001F1F0 HK"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_6263383164363435
      network: ws
      port: 80
      server: 7acf8cf6-szi1s0-tiaexf-ufc0.hk.p5pv.com
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      type: vmess
      uuid: *************-11ea-a22e-f23c91cfbbc9
      ws-headers:
        HOST: broadcastlv.chat.bilibili.com
      ws-path: /
    - name: github.com/Ruk1ng001_6432656364616336
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 204b0f73-7a1a-44f9-8b70-4ae86378b90c
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alpn:
        - http%2F1.1
      client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
      network: ws
      port: 443
      server: **************
      servername: SssXzAw.444652.XYZ
      tls: true
      type: vless
      udp: true
      uuid: 0f7070cd-c91d-4532-a51f-56da4f0e94be
      ws-opts:
        headers:
            Host: SssXzAw.444652.XYZ
        path: /nSABZLQbEUSLppj7jCmY
    - client-fingerprint: chrome
      name: github.com/Ruk1ng001_6439636165306266
      network: ws
      port: 443
      server: ipw.gfdv54cvghhgfhgj-njhgj64.info
      servername: 638892846452855773.hamedan-prx-dnraaao.info
      tls: true
      type: vless
      udp: true
      uuid: eb577431-3b16-4a8f-953b-7bcfb573b025
      ws-opts:
        headers:
            Host: 638892846452855773.hamedan-prx-dnraaao.info
        path: /kskoxmws
    - name: github.com/Ruk1ng001_6237393931363935
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
      password: 823e135ed103
      plugin: v2ray-plugin
      plugin-opts:
        host: jp7v1.lingjfjkm002.com
        mode: websocket
        mux: true
        path: /dchlbvrbbotc
        skip-cert-verify: false
        tls: true
      port: 636
      server: **************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3933383531336336
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1E6\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3661333965626436"
      network: ws
      port: 8880
      server: ************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
      network: ws
      port: 443
      server: ************
      servername: SssXzAw.444652.XYZ
      tls: true
      type: vless
      uuid: 0f7070cd-c91d-4532-a51f-56da4f0e94be
      ws-opts:
        headers:
            Host: SssXzAw.444652.XYZ
        path: /nSABZLQbEUSLppj7jCmY
    - name: github.com/Ruk1ng001_6461313636333037
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20057
      server: nexiaxg.688997.xyz
      skip-cert-verify: true
      sni: nexiaxg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3563636561396535
      password: test.+
      port: 30448
      server: ctsgb.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
      network: ws
      port: 80
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /**************=443
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
      network: ws
      port: 8880
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3332633631356137
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33652
      server: fg.dport.top
      skip-cert-verify: true
      sni: fg.dport.top
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3836613565346433
      network: ws
      port: 443
      server: dddfFFvvBnhJU.931.pP.uA
      tls: true
      type: vmess
      uuid: a4e8ec0a-75d0-4fc5-837a-4973ed3a9d3e
      ws-opts:
        headers:
            Host: dddfffvvbnhju.931.pp.ua
        path: /14Fziqw1hYgCXNutkS5H
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6563376135316539
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33654
      server: bx.dport.top
      skip-cert-verify: true
      sni: bx.dport.top
      type: hysteria2
      udp: true
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_6462613332363431
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      type: hysteria2
      udp: true
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
      network: ws
      port: 443
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: true
      type: vmess
      udp: true
      uuid: de94cc0a-0592-4969-b1fc-97ea8f0ea0b3
      ws-opts:
        headers:
            Host: farid.1.berozha.ir
        path: /us.kkp.me.eu.org/aa
    - name: github.com/Ruk1ng001_6138316533313466
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 762650e2-ce38-4450-a767-2697d18663e7
      port: 35270
      server: flyels.flylink.cyou
      skip-cert-verify: true
      sni: flyels.flylink.cyou
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3232383931303434
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20057
      server: nexiaxg.688997.xyz
      skip-cert-verify: true
      sni: nexiaxg.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
      network: ws
      port: 8880
      server: ************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_3239646232333836
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1d6feed2-6d2e-4509-a304-210de50a41d3
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - cipher: aes-256-cfb
      client-fingerprint: chrome
      name: github.com/Ruk1ng001_3734613164336331
      password: qwerREWQ@@
      port: 15098
      server: p222.panda001.net
      tfo: true
      type: ss
      udp: true
    - name: github.com/Ruk1ng001_633965653638
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 338bb7db-e7e0-4279-bf97-ff21b4c3fc67
      port: 25780
      server: ushub.xg-hub.icu
      skip-cert-verify: true
      sni: ushub.xg-hub.icu
      tls: false
      type: hysteria2
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
      network: ws
      port: 443
      server: ************
      servername: XcdvfGBnHU7.0890604.XyZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: a7c9c017-db10-4d15-b01b-0634db498b57
      ws-opts:
        headers:
            Host: XcdvfGBnHU7.0890604.XyZ
        path: /xZjr7v1DqrYyamxeTh7sLJtI1
    - name: github.com/Ruk1ng001_6539393930303131
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 724a0024-6d29-41b9-a510-7d8bd126a380
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
      network: ws
      port: 80
      server: ***************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - name: github.com/Ruk1ng001_6664363964386637
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 7e2040cc-abbc-4f4c-8f7f-62fdd0f18aa1
      port: 35270
      server: flyels.flylink.cyou
      skip-cert-verify: true
      sni: flyels.flylink.cyou
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
      network: ws
      port: 8880
      server: ************
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3638626336613364
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      country: "\U0001F1FA\U0001F1F8US"
      h2-opts: {}
      http-opts: {}
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
      network: ws
      port: 443
      server: *************
      servername: farid.1.berozha.ir
      skip-cert-verify: true
      tls: true
      type: vmess
      uuid: de94cc0a-0592-4969-b1fc-97ea8f0ea0b3
      ws-opts:
        headers:
            HOST: farid.1.berozha.ir
        path: /us.kkp.me.eu.org/aa
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3563303034333061
      network: ws
      port: 80
      server: b5ca534f-t02f40-t1iztx-1ryup.hk.p5pv.com
      skip-cert-verify: false
      tls: false
      type: vmess
      udp: true
      uuid: 3ee091ca-ab9c-11ef-a791-f23c9164ca5d
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - name: "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3365306661356538
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: /TelegramU0001F1E8U0001F1F3 @WangCai2 /?ed=2560
    - name: github.com/Ruk1ng001_6666386564313730
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e8e34487-b0a6-4992-a4b3-17155b4e8639
      port: 25783
      server: krhub.xg-hub.icu
      skip-cert-verify: true
      sni: krhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6331663466396538
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 63399f11-4b3a-4ccb-9b9c-c9a3145912f9
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3763373361656635
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 971cdb33-90c4-4df4-9df9-172710f22b2e
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3362663337633737
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: af5cd0c6-9df5-4728-984a-0a85ecafb668
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 583ceab3-**************-9bedc625ad4e
      ws-opts:
        headers:
            Host: ip.langmanshanxi.top
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
      ws-opts:
        headers:
            Host: cf.d3z.net
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6235393862366434
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      tls: false
      type: hysteria2
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_6561643733643262
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      type: hysteria2
      udp: true
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_3939363939653332
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33653
      server: dg.dport.top
      skip-cert-verify: true
      sni: dg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3465343033313861
      password: exaxgqkKkd0TAMrCxeonWg==
      port: 1443
      server: twmoon1-cdn-route.couldflare-cdn.com
      skip-cert-verify: false
      sni: twmoon1-cdn-route.couldflare-cdn.com
      tls: true
      type: http
      username: mrwdfNTD8M79LCukCieldrqZWqs=
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
      network: ws
      port: 8880
      server: ***************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: /?ed=2560
    - name: github.com/Ruk1ng001_3562336539356461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3836373438313464
      password: <password>
      port: 443
      server: 48b68b06-sxe4g0-t1bnjq-1krtb.hkt2.cdnhuawei.com
      tls: true
      type: socks5
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3338373762663838
      port: 26144
      server: ppy-boardv2.02ijp4uos1.download
      tls: false
      type: vmess
      uuid: aa15b97a-24b1-3a31-824a-bc52f5a15bac
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_3732366531346165
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
      network: ws
      port: 8880
      server: ***************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3934653330313137
      password: <password>
      port: 8443
      server: dd902adc-sxc9s0-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - alterId: 0
      cipher: auto
      name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
      network: ws
      port: 8880
      server: ***********
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 3e1e3e7f-2683-3f36-83b1-1850790295df
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3963393535633736
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 4b6f7c4d-809d-4d89-9b6f-68f95fbd140d
      port: 25785
      server: ukhub.xg-hub.icu
      skip-cert-verify: true
      sni: ukhub.xg-hub.icu
      tls: false
      type: hysteria2
    - auth: e5b6e551-791f-4bb2-bf14-f383203f0657
      name: github.com/Ruk1ng001_6339373633313431
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e5b6e551-791f-4bb2-bf14-f383203f0657
      port: 33656
      server: hg.dport.top
      skip-cert-verify: true
      sni: hg.dport.top
      type: hysteria2
      udp: true
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3433393632646435
      network: ws
      port: 80
      server: 711e213a-t09ts0-t0grjj-ezjz.hkt.tcpbbr.net
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
      ws-opts:
        headers:
            Host: broadcastlv.chat.bilibili.com
        path: /
    - name: github.com/Ruk1ng001_6136633862646666
      password: <password>
      port: 8443
      server: 9497cfdb-sx4v40-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
      network: ws
      port: 8880
      server: *************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: aes-256-gcm
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
      password: 9bfdde71b4c0
      plugin: v2ray-plugin
      plugin-opts:
        host: newroot2v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /cauejypbltqt
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
      network: ws
      password: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
      port: 443
      server: *************
      skip-cert-verify: false
      sni: eEEfGty6.999836.XYz
      type: trojan
      ws-opts:
        headers:
            Host: eEEfGty6.999836.XYz
        path: /XmTzATQPJv9RO3xr1D40NK
    - alpn:
        - http/1.1
      client-fingerprint: chrome
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
      network: ws
      port: 443
      server: *************
      servername: eeEEEeR.666470.xYZ
      skip-cert-verify: false
      tls: true
      type: vless
      udp: true
      uuid: 662e38ba-8427-4955-94aa-76f5347a0ce8
      ws-opts:
        headers:
            Host: eeEEEeR.666470.xYZ
        path: /6DuxYMYmrGrnGKRtF5UvWyyVQu
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3739386461386566
      network: ws
      port: 80
      server: ed97ae8e-t049s0-t4nxvc-1p1b.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 279b8588-616b-11ed-a8bf-f23c91cfbbc9
      ws-opts:
        headers:
            Host: ed97ae8e-t049s0-t4nxvc-1p1b.hkt.tcpbbr.net
        path: /
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: yd.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6337623839316331
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 771b45aa-d017-4e35-841b-33ec8bf5bad4
      port: 35270
      server: flyels.flylink.cyou
      skip-cert-verify: true
      sni: flyels.flylink.cyou
      tls: false
      type: hysteria2
    - cipher: aes-256-cfb
      client-fingerprint: chrome
      name: github.com/Ruk1ng001_3831353438663762
      password: qwerREWQ@@
      port: 4652
      server: p141.panda001.net
      tfo: true
      type: ss
      udp: true
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM?ed=2560
    - name: github.com/Ruk1ng001_6164333933623864
      network: ws
      port: 443
      server: s.askkf.com
      servername: s2.askkf.com
      tls: true
      type: vless
      uuid: 81fae892-37b3-47d5-d047-b44b3395fa38
      ws-opts:
        headers:
            Host: s2.askkf.com
        path: /
    - name: github.com/Ruk1ng001_3666356461666565
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20957
      server: nexiateq.688997.xyz
      skip-cert-verify: true
      sni: nexiateq.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      country: "\U0001F1ED\U0001F1F0 HK"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_3234623938643265
      network: ws
      port: 80
      server: c212722e-szecg0-t0uprz-1th8j.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      type: vmess
      uuid: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
      ws-headers:
        HOST: broadcastlv.chat.bilibili.com
      ws-path: /
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_3435326431633532
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33657
      server: rb.dport.top
      skip-cert-verify: true
      sni: rb.dport.top
      type: hysteria2
      udp: true
    - client-fingerprint: chrome
      name: "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - alpn:
        - http/1.1
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
      network: ws
      password: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
      port: 443
      server: ************
      skip-cert-verify: false
      sni: rrrRrRRrT.459.pp.ua
      type: trojan
      udp: true
      ws-opts:
        headers:
            Host: rrrRrRRrT.459.pp.ua
        path: /znQImc22ijDwVOkZfoq
    - name: github.com/Ruk1ng001_6363663038353964
      password: 3ee091ca-ab9c-11ef-a791-f23c9164ca5d
      port: 8443
      server: **************
      skip-cert-verify: true
      sni: 3233ac27-swin40-sxh7iw-1ryup.hy2.gotochinatown.net
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6337373661366432
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 4b6f7c4d-809d-4d89-9b6f-68f95fbd140d
      port: 25782
      server: hkhub.xg-hub.icu
      skip-cert-verify: true
      sni: hkhub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_35653739633933
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20457
      server: nexiahl.688997.xyz
      skip-cert-verify: true
      sni: nexiahl.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6665373339653631
      password: <password>
      port: 443
      server: 38f6538e-sx8kg0-t1bnjq-1krtb.hkt2.cdnhuawei.com
      tls: true
      type: socks5
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: github.com/Ruk1ng001_3464353338646333
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0e122b6d-67ea-4243-a7fb-641a544c7bf0
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
      network: ws
      port: 8880
      server: **************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 70df7c1e-12c8-325f-a12a-34aa46949e60
      ws-opts:
        headers:
            Host: TG.WangCai2.s4.cn-db.top
        path: "/dabai&Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6238373730616661
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 318f9f6e-deb6-4a90-b370-8c0922d9250f
      port: 20857
      server: nexiabx.688997.xyz
      skip-cert-verify: true
      sni: nexiabx.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3333326330366538
      password: test.+
      port: 40443
      server: cmhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - alterId: 0
      cipher: auto
      country: "\U0001F1ED\U0001F1F0 HK"
      h2-opts: {}
      http-opts: {}
      name: github.com/Ruk1ng001_3166393234333836
      network: ws
      port: 80
      server: 248f33f5-szi1s0-tiaexf-ufc0.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: true
      type: vmess
      uuid: *************-11ea-a22e-f23c91cfbbc9
      ws-headers:
        HOST: broadcastlv.chat.bilibili.com
      ws-path: /
    - name: github.com/Ruk1ng001_3564306633373131
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2712bd76-3dd2-4cfb-a962-65ae95825432
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3636623563313461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20357
      server: nexiahg.688997.xyz
      skip-cert-verify: true
      sni: nexiahg.688997.xyz
      tls: false
      type: hysteria2
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6332626561616562
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      type: hysteria2
      udp: true
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
      network: ws
      port: 80
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare+@WangCai2+/?ed=2560"
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3137323838373536
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33658
      server: teq.dport.top
      skip-cert-verify: true
      sni: teq.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_6231393332623436
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20257
      server: nexiarb.688997.xyz
      skip-cert-verify: true
      sni: nexiarb.688997.xyz
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
      network: ws
      port: 8880
      server: *************
      skip-cert-verify: true
      tfo: false
      tls: false
      type: vmess
      udp: true
      uuid: 248be52b-35d9-34cb-9b73-e12b78bc1301
      ws-opts:
        headers:
            Host: TG.WangCai2.s2.db-link02.top
        path: /dabai.in
    - name: github.com/Ruk1ng001_3235303735313835
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: c197f9f0-84e1-4ca4-b87e-b290afa95191
      port: 25786
      server: twhub.xg-hub.icu
      skip-cert-verify: true
      sni: twhub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: github.com/Ruk1ng001_3763646137393936
      network: ws
      port: 80
      server: 83da64f6-szi1s0-t0y0nd-1mmbp.hkt.tcpbbr.net
      servername: broadcastlv.chat.bilibili.com
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: 607d365e-7ea1-11ee-95e9-f23c913c8d2b
      ws-opts:
        headers:
            Host: 83da64f6-szi1s0-t0y0nd-1mmbp.hkt.tcpbbr.net
        path: /
    - name: github.com/Ruk1ng001_3662653032626239
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20157
      server: nexiamg.688997.xyz
      skip-cert-verify: true
      sni: nexiamg.688997.xyz
      tls: false
      type: hysteria2
    - alpn:
        - h3
      auth: cc83a02b-90ea-403b-9c76-27e4be95f637
      name: "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
      password: cc83a02b-90ea-403b-9c76-27e4be95f637
      port: 50256
      server: ***************
      skip-cert-verify: true
      sni: www.bing.com
      type: hysteria2
      udp: true
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3530626166313039
      network: ws
      port: 30833
      server: v33.heduian.link
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            host: baidu.com
        path: /oooo
    - name: github.com/Ruk1ng001_6330363833363365
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1749cfb9-d214-4537-9f2f-543378dcf14f
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3937353231343736
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0569c5b3-f400-46c8-b71d-2467382c35b8
      port: 20457
      server: nexiahl.688997.xyz
      skip-cert-verify: true
      sni: nexiahl.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6662376237646330
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 448f52d9-3b9f-44a6-a204-60dcc9428d63
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 2
      cipher: auto
      name: github.com/Ruk1ng001_3334356634346233
      network: ws
      port: 30830
      server: v30.heduian.link
      skip-cert-verify: false
      tls: false
      type: vmess
      uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
      ws-opts:
        headers:
            Host: ocbc.com
        path: /oooo
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
      network: ws
      port: 2082
      server: ************
      tls: false
      type: vless
      udp: true
      uuid: 55445977-b31b-4142-83e0-e54b80c14059
      ws-opts:
        headers:
            Host: mrdnzdtnbkwbdfsqoncu.yaSharTeam.com.
        path: /?ed=2052
    - name: github.com/Ruk1ng001_6264376662373231
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 87c96676-4887-432e-a1d7-89213be6333f
      port: 33659
      server: hl.dport.top
      skip-cert-verify: true
      sni: hl.dport.top
      tls: false
      type: hysteria2
    - client-fingerprint: chrome
      name: "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
      network: ws
      port: 8880
      server: ************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - client-fingerprint: chrome
      name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
      network: ws
      port: 8880
      server: *************
      servername: jp.laoyoutiao.link
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: jp.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
      password: 0b191076d18f
      plugin: v2ray-plugin
      plugin-opts:
        host: hk6v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /ugiibpkbca
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3938613662353534
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: e21fcd4e-1d46-4b16-b8f4-4c9c51d90879
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3462306230646233
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 2456027e-a471-476c-b5d0-4ac80f9aaf8c
      port: 25781
      server: jphub.xg-hub.icu
      skip-cert-verify: true
      sni: jphub.xg-hub.icu
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
      network: ws
      port: 8880
      server: *************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
      network: ws
      port: 8880
      server: *************
      servername: vngsupply.ip-ddns.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: vngsupply.ip-ddns.com
        path: /J5aLQOY1R9ONWYCM
    - name: github.com/Ruk1ng001_6138303631343566
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 7b2e01b1-4f4c-408a-9ee5-ad340fbba0e4
      port: 20457
      server: nexiahl.688997.xyz
      skip-cert-verify: true
      sni: nexiahl.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_3139396234313438
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: d4cf6e04-6614-4e31-bd24-10db576815d9
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - auth: 5CBqBh6MeDq6GajcilBiDg==
      name: github.com/Ruk1ng001_6436613836333164
      password: 5CBqBh6MeDq6GajcilBiDg==
      port: 61001
      server: 192-227-152-86.nip.io
      skip-cert-verify: true
      sni: 192-227-152-86.nip.io
      type: hysteria2
    - name: github.com/Ruk1ng001_6366646333613962
      password: test.+
      port: 30443
      server: cmhka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_6539396539366363
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: cb36d691-0349-4d9a-a040-1e0b3dfb5c12
      port: 35300
      server: flyrb.flylink.cyou
      skip-cert-verify: true
      sni: flyxg.flylink.cyou
      tls: false
      type: hysteria2
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_3338626463343632
      password: test.+
      port: 30445
      server: cmusa.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - cipher: aes-256-gcm
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
      password: 0b191076d18f
      plugin: v2ray-plugin
      plugin-opts:
        host: hk6v1.dsjsapp.com
        mode: websocket
        mux: true
        path: /ugiibpkbca
        skip-cert-verify: false
        tls: true
      port: 636
      server: ***************
      tfo: false
      type: ss
    - name: github.com/Ruk1ng001_3137306535363936
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: ed2621e5-67ad-40e7-a2ef-61278db1a59b
      port: 25784
      server: dehub.xg-hub.icu
      skip-cert-verify: true
      sni: dehub.xg-hub.icu
      tls: false
      type: hysteria2
    - alterId: 0
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
      port: 50007
      server: **************
      servername: **************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - auth: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      name: github.com/Ruk1ng001_3338656239353332
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 605b42da-dfb0-476c-8ecd-307ccb1ea219
      port: 33650
      server: mg.dport.top
      skip-cert-verify: true
      sni: mg.dport.top
      type: hysteria2
      udp: true
    - name: github.com/Ruk1ng001_3439626230313037
      network: ws
      port: 443
      server: nnctwclyx.codesofun.pp.ua
      servername: nnctw.codesofun.pp.ua
      tls: true
      type: vless
      uuid: 8b9c63ff-6789-449d-e5fd-5b83fef2b440
      ws-opts:
        headers:
            Host: nnctw.codesofun.pp.ua
        path: /2261acb5
    - name: "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    - name: "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
      network: ws
      port: 8880
      server: **************
      tls: false
      type: vless
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: us.laoyoutiao.link
        path: "Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    - name: github.com/Ruk1ng001_6331646365333336
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 0004f8b0-df8e-4f3d-866a-e3547122933d
      port: 20657
      server: nexiayg.688997.xyz
      skip-cert-verify: true
      sni: nexiayg.688997.xyz
      tls: false
      type: hysteria2
    - name: github.com/Ruk1ng001_6262323233653834
      obfs: salamander
      obfs-password: ZWYxYjI3YjlmY2I0OGEwYw==
      password: 771b45aa-d017-4e35-841b-33ec8bf5bad4
      port: 35230
      server: flydg.flylink.cyou
      skip-cert-verify: true
      sni: flydg.flylink.cyou
      tls: false
      type: hysteria2
    - alterId: 64
      cipher: auto
      name: "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
      port: 50112
      server: *************
      skip-cert-verify: true
      tls: false
      type: vmess
      uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    - auth: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      name: github.com/Ruk1ng001_6234343230363461
      obfs: salamander
      obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
      password: 1a62e9bb-0b00-40cb-9fed-2c55224eae85
      port: 33651
      server: jnd.dport.top
      skip-cert-verify: true
      sni: jnd.dport.top
      type: hysteria2
      udp: true
    - name: "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
      network: ws
      port: 80
      server: ***********
      tls: false
      type: vless
      udp: true
      uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
      ws-opts:
        headers:
            Host: zoomgov.vipren.biz.id
        path: /**************=443
    - alterId: 0
      cipher: auto
      country: "\U0001F1E8\U0001F1F4CO"
      h2-opts: {}
      http-opts: {}
      name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
      network: ws
      port: 443
      server: ************
      servername: 1a2d514b-37cf-499f-8d08-d017a92ab5bb.asoul-ava.top
      skip-cert-verify: true
      tls: true
      type: vmess
      uuid: 5f726fe3-d82e-4da5-a711-8af0cbb2b682
      ws-opts:
        headers:
            HOST: 1a2d514b-37cf-499f-8d08-d017a92ab5bb.asoul-ava.top
        path: /azumase.ren
    - name: github.com/Ruk1ng001_3164663535313063
      password: test.+
      port: 30448
      server: ctsga.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: github.com/Ruk1ng001_3362626135633261
      password: <password>
      port: 8443
      server: 999598f4-sx8kg0-t1bnjq-1krtb.hkt.cdnhuawei.com
      tls: true
      type: http
      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    - name: github.com/Ruk1ng001_3163666237653361
      password: test.+
      port: 30443
      server: cthka.ssv.vc
      skip-cert-verify: false
      tls: true
      type: http
      username: test
    - name: "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
      network: ws
      port: 8880
      server: *************
      servername: VngSuPpLY.IP-DdnS.com
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
      ws-opts:
        headers:
            Host: VngSuPpLY.IP-DdnS.com
        path: /1ycR2zb3KeELWRha
    - name: "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      network: ws
      port: 8880
      server: **************
      servername: Telegram-channel-WangCai2
      skip-cert-verify: false
      tls: false
      type: vless
      udp: true
      uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
      ws-opts:
        headers:
            Host: kjgx.laoyoutiao.link
        path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
proxy-groups:
    - interval: 300
      name: "\U0001F3AF"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - github.com/Ruk1ng001_6564633162393539
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - github.com/Ruk1ng001_3365306661356538
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F47B"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - github.com/Ruk1ng001_3463623035343238
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6435306164666165
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3162613432376163
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3332653730623238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F534_\U0001F1E9\U0001F1EA_\U0001F4BC_github.com/Ruk1ng001_6564386238383439"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - github.com/Ruk1ng001_6133313834333934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6238323762663634
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3232383363316461
        - github.com/Ruk1ng001_6139346637303337
        - github.com/Ruk1ng001_35303137353031
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - github.com/Ruk1ng001_6439373666393161
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6239323437633439
        - github.com/Ruk1ng001_6437306131613231
        - github.com/Ruk1ng001_6636333131363533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - github.com/Ruk1ng001_3732393764363436
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_3330336130613830
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_6434363231303762
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_6464373535616561
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - github.com/Ruk1ng001_6432656364616336
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1E6\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3661333965626436"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - github.com/Ruk1ng001_6462613332363431
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - github.com/Ruk1ng001_3362663337633737
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_35653739633933
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - github.com/Ruk1ng001_6231393332623436
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3530626166313039
        - github.com/Ruk1ng001_6330363833363365
        - github.com/Ruk1ng001_3937353231343736
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_6264376662373231
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_3938613662353534
        - github.com/Ruk1ng001_3462306230646233
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3137306535363936
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_3439626230313037
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6331646365333336
        - github.com/Ruk1ng001_6262323233653834
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F44B"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - github.com/Ruk1ng001_3463623035343238
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6435306164666165
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3162613432376163
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3332653730623238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F534_\U0001F1E9\U0001F1EA_\U0001F4BC_github.com/Ruk1ng001_6564386238383439"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - github.com/Ruk1ng001_6133313834333934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6238323762663634
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3232383363316461
        - github.com/Ruk1ng001_6139346637303337
        - github.com/Ruk1ng001_35303137353031
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - github.com/Ruk1ng001_6439373666393161
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6239323437633439
        - github.com/Ruk1ng001_6437306131613231
        - github.com/Ruk1ng001_6636333131363533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - github.com/Ruk1ng001_3732393764363436
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_3330336130613830
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_6434363231303762
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_6464373535616561
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - github.com/Ruk1ng001_6432656364616336
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1E6\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3661333965626436"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - github.com/Ruk1ng001_6462613332363431
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - github.com/Ruk1ng001_3362663337633737
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_35653739633933
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - github.com/Ruk1ng001_6231393332623436
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3530626166313039
        - github.com/Ruk1ng001_6330363833363365
        - github.com/Ruk1ng001_3937353231343736
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_6264376662373231
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_3938613662353534
        - github.com/Ruk1ng001_3462306230646233
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3137306535363936
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_3439626230313037
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6331646365333336
        - github.com/Ruk1ng001_6262323233653834
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F680"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_3232336463333361"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - github.com/Ruk1ng001_3463623035343238
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6435306164666165
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_6136636466633939
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - github.com/Ruk1ng001_37323030643661
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3162613432376163
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_3332653730623238
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F534_\U0001F1E9\U0001F1EA_\U0001F4BC_github.com/Ruk1ng001_6564386238383439"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - github.com/Ruk1ng001_6133313834333934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6238323762663634
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3232383363316461
        - github.com/Ruk1ng001_6139346637303337
        - github.com/Ruk1ng001_35303137353031
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - github.com/Ruk1ng001_6439373666393161
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3432613333366433
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6239323437633439
        - github.com/Ruk1ng001_6437306131613231
        - github.com/Ruk1ng001_6636333131363533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - github.com/Ruk1ng001_3732393764363436
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_3330336130613830
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3466663135313065
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_6434363231303762
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_6464373535616561
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - github.com/Ruk1ng001_6432656364616336
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "❓_\U0001F1E6\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3661333965626436"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - github.com/Ruk1ng001_6462613332363431
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_3239646232333836
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - github.com/Ruk1ng001_3362663337633737
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_35653739633933
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - github.com/Ruk1ng001_6231393332623436
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_3530626166313039
        - github.com/Ruk1ng001_6330363833363365
        - github.com/Ruk1ng001_3937353231343736
        - github.com/Ruk1ng001_6662376237646330
        - github.com/Ruk1ng001_3334356634346233
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - github.com/Ruk1ng001_6264376662373231
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_3938613662353534
        - github.com/Ruk1ng001_3462306230646233
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6138303631343566
        - github.com/Ruk1ng001_3139396234313438
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3137306535363936
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_3439626230313037
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6331646365333336
        - github.com/Ruk1ng001_6262323233653834
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      type: load-balance
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0"
      proxies:
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - github.com/Ruk1ng001_3635646561333861
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3865303030376334
        - github.com/Ruk1ng001_6263383164363435
        - github.com/Ruk1ng001_3563303034333061
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3934653330313137
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_6136633862646666
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_3234623938643265
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3763646137393936
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_3362626135633261
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1F3:\U0001F1F9\U0001F1FC"
      proxies:
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_6437303763303333
        - github.com/Ruk1ng001_6465373134613231
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_6439373666393161
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3562653962643961
        - github.com/Ruk1ng001_3565393236356239
        - github.com/Ruk1ng001_3465343033313861
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3163666237653361
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1F2"
      proxies:
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6332626561616562
        - github.com/Ruk1ng001_3530626166313039
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_3338656239353332
        - github.com/Ruk1ng001_3439626230313037
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F7\U0001F1FA"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F3"
      proxies:
        - github.com/Ruk1ng001_3839643063653164
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3338373762663838
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E7\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3666373466643734
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EA\U0001F1F8"
      proxies:
        - github.com/Ruk1ng001_3666373466643734
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6563376135316539
        - github.com/Ruk1ng001_3338373762663838
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E9\U0001F1EA"
      proxies:
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3939363939653332
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EB\U0001F1F7"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_3332633631356137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EF\U0001F1F5"
      proxies:
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3435326431633532
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F9"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EC\U0001F1E7"
      proxies:
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_3839643063653164
        - "\U0001F534_\U0001F1E9\U0001F1EA_\U0001F4BC_github.com/Ruk1ng001_6564386238383439"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F0\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3738303331643362
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_3533643165623038
        - github.com/Ruk1ng001_3563636561396535
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3338626463343632
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E8\U0001F1E6"
      proxies:
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_6234343230363461
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F3\U0001F1F1"
      proxies:
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3539616164386266
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_6133313834333934
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_35303137353031
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6664383662343533
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_6462613332363431
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FF\U0001F1E6"
      proxies:
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1EE\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_6564303835626136
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F9\U0001F1F7"
      proxies:
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_3137323838373536
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F5\U0001F1F1"
      proxies:
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_6439636165306266
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F3\U0001F1F4"
      proxies:
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1E6"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F8\U0001F1EE"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1E6\U0001F1F7"
      proxies:
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3539616164386266
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FA\U0001F1FE"
      proxies:
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1FB\U0001F1EA"
      proxies:
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F8\U0001F1F0"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_3533323030353139
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - github.com/Ruk1ng001_3831353438663762
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F1F1\U0001F1FA"
      proxies:
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_6439636165306266
      test-timeout: 5
      type: select
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F916"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - github.com/Ruk1ng001_6564633162393539
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - github.com/Ruk1ng001_3365306661356538
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F4FA"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_33626633386536"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6636333131363533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_3734613164336331
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - github.com/Ruk1ng001_6136633862646666
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6362343931636662"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F3AC"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6435306164666165
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - github.com/Ruk1ng001_6634363638653334
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6664383662343533
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3939363939653332
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3235303735313835
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - github.com/Ruk1ng001_3938613662353534
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: "\U0001F419"
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - github.com/Ruk1ng001_3835623631666464
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3235333066353034
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BC_github.com/Ruk1ng001_6537373831343962"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_3264323232653631
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - "\U0001F7E1_\U0001F1EE\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3139383863633739"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6636333131363533
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - "❓_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6466343131636431"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_6336353561363333"
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3461666437343131"
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4F6_github.com/Ruk1ng001_6337336132343539"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - github.com/Ruk1ng001_3163666237653361
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
    - interval: 300
      name: ✈️
      proxies:
        - "\U0001F7E1_\U0001F1E8\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6637363964663466"
        - github.com/Ruk1ng001_3666373466643734
        - github.com/Ruk1ng001_6564303835626136
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_3332613161656137"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6638633239323537"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3338633837306238"
        - github.com/Ruk1ng001_6665613065346164
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3836663733313738"
        - github.com/Ruk1ng001_6339646565373938
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3562613332366535"
        - github.com/Ruk1ng001_6439643631343738
        - "\U0001F534_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3461633839316262"
        - github.com/Ruk1ng001_6633616161383765
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366376466663531"
        - "\U0001F7E0_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_6436633566636338"
        - github.com/Ruk1ng001_3436326136353835
        - "\U0001F534_\U0001F1F2\U0001F1E9_\U0001F4BC_github.com/Ruk1ng001_65373938613838"
        - github.com/Ruk1ng001_3139343834313837
        - "\U0001F7E0_\U0001F1F9\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_6163326336393763"
        - github.com/Ruk1ng001_6435306164666165
        - github.com/Ruk1ng001_6437303763303333
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6530653066616434"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3832326561316334"
        - github.com/Ruk1ng001_65353737353831
        - "❓_\U0001F1EB\U0001F1F7_\U0001F4BC_github.com/Ruk1ng001_6336313833646133"
        - github.com/Ruk1ng001_3565383232353338
        - github.com/Ruk1ng001_3839643063653164
        - github.com/Ruk1ng001_3536663336653435
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3462353835613864"
        - github.com/Ruk1ng001_6434316432313361
        - github.com/Ruk1ng001_3333373138336563
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6331396337643034"
        - github.com/Ruk1ng001_3839633563323161
        - "\U0001F7E1_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6465326238386336"
        - github.com/Ruk1ng001_3464386330303961
        - github.com/Ruk1ng001_3561353731633539
        - github.com/Ruk1ng001_3331373033323837
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3331386561633630"
        - github.com/Ruk1ng001_6564633162393539
        - "❓_\U0001F1F3\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6538333965386539"
        - github.com/Ruk1ng001_3533323030353139
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_3436656561303064"
        - github.com/Ruk1ng001_3738303331643362
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3139316366376637"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3466313061336264"
        - github.com/Ruk1ng001_3539616164386266
        - "\U0001F7E1_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_3462396336336561"
        - github.com/Ruk1ng001_6235653130303737
        - github.com/Ruk1ng001_3736393163636436
        - github.com/Ruk1ng001_6465373134613231
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BC_github.com/Ruk1ng001_62356331656266"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3630356538396166"
        - github.com/Ruk1ng001_3331613266376662
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BC_github.com/Ruk1ng001_3662346364343063"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6233646666336239"
        - github.com/Ruk1ng001_3738653764333838
        - github.com/Ruk1ng001_6461656664646666
        - github.com/Ruk1ng001_3563646362656235
        - github.com/Ruk1ng001_3531356165366663
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6435396165323635"
        - github.com/Ruk1ng001_3634303766363039
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6635313935376462"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3161376634373330"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3430386239626665"
        - github.com/Ruk1ng001_6634356239386330
        - github.com/Ruk1ng001_6664623833366434
        - github.com/Ruk1ng001_6332613833396439
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530376532613463"
        - github.com/Ruk1ng001_3136363866356365
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3164373235623138"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231653866616565"
        - github.com/Ruk1ng001_32376632636230
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3535323531306232"
        - github.com/Ruk1ng001_6632323936386239
        - github.com/Ruk1ng001_3435326535353033
        - "\U0001F7E0_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_32393266313138"
        - github.com/Ruk1ng001_3466326438663839
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3537653235303637"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_6266633533306162"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6363653337646238"
        - github.com/Ruk1ng001_3263653961396632
        - github.com/Ruk1ng001_3434623763646434
        - github.com/Ruk1ng001_6235616432666637
        - github.com/Ruk1ng001_3334363461316530
        - github.com/Ruk1ng001_6562633765653631
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3732356432656665"
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3563316530623231"
        - github.com/Ruk1ng001_3431316235386230
        - github.com/Ruk1ng001_3330643631376666
        - github.com/Ruk1ng001_3635646561333861
        - "❓_\U0001F1E6\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6339643963623836"
        - github.com/Ruk1ng001_3264656135663034
        - github.com/Ruk1ng001_6335313363343366
        - github.com/Ruk1ng001_3264323232653631
        - github.com/Ruk1ng001_3864663936653262
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3838313239366531"
        - github.com/Ruk1ng001_37646339616265
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3766373833383632"
        - github.com/Ruk1ng001_6462316532396330
        - github.com/Ruk1ng001_3863393430613433
        - "\U0001F534_\U0001F1E8\U0001F1FE_\U0001F4BB_github.com/Ruk1ng001_6562383162303233"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938373665326235"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366613032306438"
        - github.com/Ruk1ng001_6464323733386635
        - github.com/Ruk1ng001_3835616365363435
        - github.com/Ruk1ng001_6636333131363533
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6636613335396662"
        - github.com/Ruk1ng001_3631303430363134
        - "❓_\U0001F1FB\U0001F1F3_\U0001F4BB_github.com/Ruk1ng001_36623766636330"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6366653862323663"
        - github.com/Ruk1ng001_6664383662343533
        - github.com/Ruk1ng001_3964393736633237
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6266303037326563"
        - github.com/Ruk1ng001_31633031356434
        - github.com/Ruk1ng001_3562653962643961
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_6262343564626666"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3937323063633262"
        - github.com/Ruk1ng001_3961633062323636
        - github.com/Ruk1ng001_3939313435643833
        - github.com/Ruk1ng001_6235353439613934
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261313034383062"
        - github.com/Ruk1ng001_62363830373766
        - "\U0001F7E1_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3864323032616265"
        - "❓_\U0001F1F7\U0001F1FA_\U0001F4BB_github.com/Ruk1ng001_6138353633353661"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3938346131343737"
        - github.com/Ruk1ng001_62396364396633
        - github.com/Ruk1ng001_3162626630326566
        - github.com/Ruk1ng001_3565393236356239
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3930336534343032"
        - github.com/Ruk1ng001_3833633361613064
        - github.com/Ruk1ng001_3763326462356666
        - github.com/Ruk1ng001_3462373763626437
        - github.com/Ruk1ng001_3865303030376334
        - "\U0001F534_\U0001F1F3\U0001F1F1_\U0001F4BC_github.com/Ruk1ng001_3731316561623335"
        - github.com/Ruk1ng001_3865353236366537
        - github.com/Ruk1ng001_3533643165623038
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3365326437333032"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_32623732633836"
        - github.com/Ruk1ng001_6263383164363435
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6534316535343765"
        - github.com/Ruk1ng001_6439636165306266
        - github.com/Ruk1ng001_6237393931363935
        - github.com/Ruk1ng001_3933383531336336
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3465663662643363"
        - github.com/Ruk1ng001_6461313636333037
        - github.com/Ruk1ng001_3563636561396535
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3765373539656433"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3631396266633036"
        - github.com/Ruk1ng001_3332633631356137
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_3864316238366566"
        - github.com/Ruk1ng001_3836613565346433
        - github.com/Ruk1ng001_6563376135316539
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3238613565323034"
        - github.com/Ruk1ng001_6138316533313466
        - github.com/Ruk1ng001_3232383931303434
        - "❓_\U0001F1F5\U0001F1ED_\U0001F4BB_github.com/Ruk1ng001_3130336466626137"
        - github.com/Ruk1ng001_633965653638
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233373239616665"
        - github.com/Ruk1ng001_6539393930303131
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6361396633613130"
        - github.com/Ruk1ng001_6664363964386637
        - "❓_\U0001F1EE\U0001F1E9_\U0001F4BB_github.com/Ruk1ng001_3234386265306637"
        - github.com/Ruk1ng001_3638626336613364
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6666343663363939"
        - github.com/Ruk1ng001_3563303034333061
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3134346466626432"
        - github.com/Ruk1ng001_3365306661356538
        - "❓_\U0001F1F0\U0001F1FF_\U0001F4BB_github.com/Ruk1ng001_6466383662326434"
        - github.com/Ruk1ng001_6666386564313730
        - github.com/Ruk1ng001_6331663466396538
        - github.com/Ruk1ng001_3763373361656635
        - "\U0001F7E0_\U0001F1E8\U0001F1F7_\U0001F4BB_github.com/Ruk1ng001_3864656261333838"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3437643934306132"
        - github.com/Ruk1ng001_6235393862366434
        - github.com/Ruk1ng001_6561643733643262
        - github.com/Ruk1ng001_3465343033313861
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3337303962316533"
        - github.com/Ruk1ng001_3562336539356461
        - github.com/Ruk1ng001_3836373438313464
        - github.com/Ruk1ng001_3338373762663838
        - github.com/Ruk1ng001_3732366531346165
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6561653937373535"
        - github.com/Ruk1ng001_3934653330313137
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6233346332613134"
        - github.com/Ruk1ng001_3963393535633736
        - github.com/Ruk1ng001_6339373633313431
        - github.com/Ruk1ng001_3433393632646435
        - "❓_\U0001F1F1\U0001F1F9_\U0001F4BB_github.com/Ruk1ng001_6138643765613863"
        - "\U0001F7E0_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6439353565343935"
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3261646537326636"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6639663132643532"
        - github.com/Ruk1ng001_3739386461386566
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6466343932643963"
        - github.com/Ruk1ng001_6337623839316331
        - github.com/Ruk1ng001_3831353438663762
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BC_github.com/Ruk1ng001_61666238386237"
        - github.com/Ruk1ng001_6164333933623864
        - github.com/Ruk1ng001_3666356461666565
        - github.com/Ruk1ng001_3234623938643265
        - github.com/Ruk1ng001_3435326431633532
        - "❓_\U0001F1EF\U0001F1F5_\U0001F4BB_github.com/Ruk1ng001_3162663536316130"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6538343430613237"
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6333383736646266"
        - github.com/Ruk1ng001_6363663038353964
        - github.com/Ruk1ng001_6337373661366432
        - github.com/Ruk1ng001_6665373339653631
        - github.com/Ruk1ng001_3464353338646333
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_3530616264346635"
        - github.com/Ruk1ng001_6238373730616661
        - github.com/Ruk1ng001_3333326330366538
        - github.com/Ruk1ng001_3166393234333836
        - github.com/Ruk1ng001_3564306633373131
        - github.com/Ruk1ng001_3636623563313461
        - github.com/Ruk1ng001_6332626561616562
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6630643630303430"
        - github.com/Ruk1ng001_3137323838373536
        - "\U0001F7E1_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6237633464653263"
        - github.com/Ruk1ng001_3763646137393936
        - github.com/Ruk1ng001_3662653032626239
        - github.com/Ruk1ng001_6330363833363365
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3731306565313163"
        - "❓_\U0001F1F8\U0001F1E6_\U0001F4BB_github.com/Ruk1ng001_3336666431626665"
        - "❓_\U0001F1E9\U0001F1EA_\U0001F4BB_github.com/Ruk1ng001_65373562316336"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3465613334343038"
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6139626263326265"
        - "❓_\U0001F1EC\U0001F1E7_\U0001F4BB_github.com/Ruk1ng001_64356634326166"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3739306363303663"
        - github.com/Ruk1ng001_6436613836333164
        - github.com/Ruk1ng001_6366646333613962
        - github.com/Ruk1ng001_6539396539366363
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_6438666661363665"
        - github.com/Ruk1ng001_3338626463343632
        - "\U0001F7E1_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3164386239623931"
        - github.com/Ruk1ng001_3338656239353332
        - "❓_\U0001F1EA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6231323462353234"
        - "❓_\U0001F1E8\U0001F1F3:\U0001F1ED\U0001F1F0_\U0001F4BB_github.com/Ruk1ng001_3664336563333934"
        - github.com/Ruk1ng001_6234343230363461
        - "\U0001F7E0_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_6230303931383832"
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3435626465646163"
        - github.com/Ruk1ng001_3164663535313063
        - github.com/Ruk1ng001_3362626135633261
        - "❓_\U0001F1FA\U0001F1F8_\U0001F4BB_github.com/Ruk1ng001_3231333634613635"
        - "❓_\U0001F1F3\U0001F1F1_\U0001F4BB_github.com/Ruk1ng001_3532386233626535"
      test-timeout: 5
      tolerance: 50
      type: url-test
      url: https://www.gstatic.com/generate_204
rules:
    - DOMAIN-SUFFIX,csdn.net,DIRECT
    - DOMAIN-SUFFIX,juejin.cn,DIRECT
    - DOMAIN-SUFFIX,linux.do,DIRECT
    - DOMAIN-SUFFIX,plugins.jetbrains.com,DIRECT
    - DOMAIN-SUFFIX,shared.oaifree.com,DIRECT
    - RULE-SET,apple_cdn,DIRECT
    - RULE-SET,apple_services,DIRECT
    - RULE-SET,domestic_ip,DIRECT
    - RULE-SET,lan_ip,DIRECT
    - RULE-SET,apple_cn_non_ip,DIRECT,DIRECT
    - RULE-SET,direct_non_ip,DIRECT
    - RULE-SET,domestic_non_ip,DIRECT
    - RULE-SET,lan_non_ip,DIRECT
    - RULE-SET,microsoft_cdn_non_ip,DIRECT
    - RULE-SET,microsoft_non_ip,DIRECT
    - RULE-SET,reject_domainset,REJECT
    - RULE-SET,reject_ip,REJECT
    - RULE-SET,sogouinput,REJECT
    - RULE-SET,reject_non_ip,REJECT
    - RULE-SET,reject_non_ip_no_drop,REJECT
    - "DOMAIN-SUFFIX,api.themoviedb.org,\U0001F3AC"
    - "DOMAIN-SUFFIX,github.com,\U0001F419"
    - "DOMAIN-SUFFIX,raw.githubusercontent.com,\U0001F419"
    - "RULE-SET,cdn_domainset,\U0001F3AF"
    - "RULE-SET,download_domainset,\U0001F3AF"
    - "RULE-SET,stream_ip,\U0001F4FA"
    - RULE-SET,telegram_ip,✈️
    - "RULE-SET,ai_non_ip,\U0001F916"
    - "RULE-SET,download_non_ip,\U0001F3AF"
    - "RULE-SET,global_non_ip,\U0001F3AF"
    - RULE-SET,reject_non_ip_drop,REJECT-DROP
    - "RULE-SET,stream_non_ip,\U0001F4FA"
    - RULE-SET,telegram_non_ip,✈️
    - "MATCH,\U0001F3AF"
dns:
    enable: true
    prefer-h3: true
    ipv6-timeout: 100
    use-hosts: true
    use-system-hosts: true
    respect-rules: true
    nameserver:
        - system
        - https://*********/dns-query
        - https://doh.pub/dns-query
    fallback-filter:
        geoip: true
        geoip-code: CN
    enhanced-mode: fake-ip
    fake-ip-range: ********/8
    fake-ip-filter:
        - dns.msftnsci.com
        - www.msftnsci.com
        - www.msftconnecttest.com
    default-nameserver:
        - ***************
        - *******
    proxy-server-nameserver:
        - https://*********/dns-query
        - https://doh.pub/dns-query
ntp:
    server: time.apple.com
    port: 123
    interval: 30
tun:
    dns-hijack:
        - 0.0.0.0:53
    auto-route: true
    auto-detect-interface: true
    inet6-address:
        - fdfe:dcba:9876::1/126
tuic-server:
    max-idle-time: 15000
    authentication-timeout: 1000
    alpn:
        - h3
    max-udp-relay-packet-size: 1500
iptables:
    inbound-interface: lo
    dns-redirect: true
experimental:
    quic-go-disable-ecn: true
profile:
    store-selected: true
    store-fake-ip: true
geox-url:
    geoip: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip.dat
    mmdb: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/Country.mmdb
    asn: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/GeoLite2-ASN.mmdb
    geosite: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
sniffer:
    enable: true
    override-destination: true
    skip-domain:
        - Mijia Cloud
        - +.push.apple.com
    force-dns-mapping: true
    parse-pure-ip: true
    sniff:
        HTTP:
            ports:
                - "80"
                - 8080-8880
            override-destination: true
        QUIC:
            ports:
                - "443"
                - "8443"
        TLS:
            ports:
                - "443"
                - "8443"
