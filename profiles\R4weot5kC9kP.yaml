proxies:
  - name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: >-
        /TelegramU0001F1E8U0001F1F3?ed=2048@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn----@Evay_vpn
  - auth: dongtaiwang.com
    down: 100 Mbps
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 6.4MB/s"
    password: dongtaiwang.com
    port: 50066
    server: ************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    udp: true
    up: 100 Mbps
  - name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 3.7MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 2.8MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 3.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: sk.laoyoutiao.link
  - name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 4.0MB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 3.0MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram@V2ray_Alpha/?ed=2048
    servername: sk.laoyoutiao.link
  - name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 1.9MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /TelegramU0001F1E8U0001F1F3
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1E8\U0001F1E6加拿大1 | ⬇️ 1.6MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 3ac2de34-47c5-4dd5-afc0-8fb4b05d4077
    ws-opts:
      headers:
        Host: blaze-can-118.blazecanada.site
      path: /?ed=2560
    servername: blaze-can-118.blazecanada.site
  - name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram @MxlShare @WangCai2 /
  - auth: dongtaiwang.com
    down: 100 Mbps
    name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 5.1MB/s"
    password: dongtaiwang.com
    port: 5355
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    udp: true
    up: 100 Mbps
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /TelegramU0001F1E8U0001F1F3 @WangCai2
  - name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    servername: cf.d3z.net
  - name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 1.3MB/s"
    network: ws
    password: Aimer
    port: 443
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 1.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /TelegramU0001F1E8U0001F1F3
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 4.0MB/s"
    network: ws
    password: Aimer
    port: 2096
    server: ***********
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 4.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 2.9MB/s"
    network: ws
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: *************
    skip-cert-verify: false
    sni: dDdCcCVGHyu.999834.xyZ
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: dDdCcCVGHyu.999834.xyZ
      path: /8y30nl6G2U0xOiraYkzOdf
  - name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 3.5MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 2.9MB/s"
    network: ws
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: ***********
    skip-cert-verify: false
    sni: ttTtTtttTTtt7.Taipei102.DPDns.oRG
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: ttTtTtttTTtt7.Taipei102.DPDns.oRG
      path: /8y30nl6G2U0xOiraYkzOdf
  - auth: 06c625ea-5902-11ee-9e87-f23c9313b177
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 4.2MB/s"
    password: 06c625ea-5902-11ee-9e87-f23c9313b177
    port: 1743
    server: cef8f521-t0fds0-t0l7ae-4mce.la.shifen.uk
    type: hysteria2
    udp: true
    sni: cef8f521-t0fds0-t0l7ae-4mce.la.shifen.uk
  - name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 1.3MB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 7248e825-887c-48b9-83bc-c26bc6392bf8
    ws-opts:
      headers:
        Host: xxcdvfgt.191268.xyz
      path: /W02wBrOOqlSUywV3ibrzzKXJGy3S1
    servername: xxcdvfgt.191268.xyz
  - auth: 9b6419f8-8120-442e-a48c-6bf52aef0abf
    down: 20
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 2.3MB/s"
    password: 9b6419f8-8120-442e-a48c-6bf52aef0abf
    port: 30001
    server: gy1.mlshu.top
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 20
    sni: gy1.mlshu.top
  - name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 2.1MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
    ws-opts:
      headers:
        Host: tTTtTTT67.459.PP.uA
      path: /5UW2C42lpZ7Dj4VDwVOkZfoq
    servername: tTTtTTT67.459.PP.uA
  - name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 3.7MB/s"
    network: ws
    password: Aimer
    port: 443
    server: ***********
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /
  - name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 3.0MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: '/[ByEbraSha]'
    servername: ' [By EbraSha]'
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 2.3MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: a7c9c017-db10-4d15-b01b-0634db498b57
    ws-opts:
      headers:
        Host: XcdvfGBnHU7.0890604.XyZ
      path: /xZjr7v1DqrYyamxeTh7sLJtI1
    servername: XcdvfGBnHU7.0890604.XyZ
  - name: "\U0001F1EB\U0001F1EE芬兰1 | ⬇️ 3.7MB/s"
    network: ws
    password: Aimer
    port: 443
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 1.9MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 4.4MB/s"
    network: ws
    password: Aimer
    port: 2087
    server: ***************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 4.1MB/s"
    network: ws
    password: Aimer
    port: 2087
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 3.8MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: /TelegramU0001F1E8U0001F1F3
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 4.3MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
    ws-opts:
      headers:
        Host: DdDFRt5.890602.xyZ
      path: /5UW2C42lpZ7Dj4VDwVOkZfoq
    servername: DdDFRt5.890602.xyZ
  - name: "\U0001F1FA\U0001F1F8美国32 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /
  - auth: d17d3890-037d-4c2c-844f-33e2b7e17c0f
    name: "\U0001F1FA\U0001F1F8美国33 | ⬇️ 3.6MB/s"
    obfs: salamander
    obfs-password: ZDQxZDhjZDk4ZjAwYjIwNA==
    password: d17d3890-037d-4c2c-844f-33e2b7e17c0f
    port: 33650
    server: mg.dport.top
    skip-cert-verify: true
    sni: mg.dport.top
    type: hysteria2
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 3.0MB/s"
    network: ws
    port: 443
    server: zula.ir
    tls: true
    type: vless
    udp: true
    uuid: 1d173704-d5cc-4ce2-8b06-3a9a71037530
    ws-opts:
      headers:
        Host: rayan-army.foton.dpdns.org
      path: /?ed=2560
    servername: rayan-army.foton.dpdns.org
  - name: "\U0001F1EB\U0001F1EE芬兰2 | ⬇️ 1.0MB/s"
    network: ws
    port: 443
    server: duke.ns.cloudflare.com
    tls: true
    type: vless
    udp: true
    uuid: ee6774c0-9b19-4ff1-8b30-2da4b71977e2
    ws-opts:
      headers:
        Host: edga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
    servername: edga.aimercc.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国34 | ⬇️ 4.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 1.2MB/s"
    network: ws
    password: Aimer
    port: 2087
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: e9979910-79d1-4621-a93c-b2a579c44ba7
    ws-opts:
      headers:
        Host: vngsupply.ip-ddns.com
      path: /J5aLQOY1R9ONWYCM
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国1 | ⬇️ 2.2MB/s"
    network: ws
    port: 80
    server: vizzpn.freenet.channel.vizzfrag.ir
    tls: false
    type: vless
    udp: true
    uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
    ws-opts:
      headers:
        Host: zoomgov.vipren.biz.id
      path: /**************=443
  - name: "\U0001F1FA\U0001F1F8美国35 | ⬇️ 4.2MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 2d068083-2cb0-4ae3-a44a-6fc82f3039cc
    ws-opts:
      headers:
        Host: sCCcxZZZXfVG.999834.xyZ
      path: /laTDC7FYNlEa06b88JSo
    servername: sCCcxZZZXfVG.999834.xyZ
  - name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 4.1MB/s"
    network: ws
    password: Aimer
    port: 2096
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国36 | ⬇️ 3.6MB/s"
    network: ws
    password: Aimer
    port: 2083
    server: ***********
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国37 | ⬇️ 3.5MB/s"
    network: ws
    password: 2e65577e-8fdb-4bb1-a495-96f3122099a7
    port: 443
    server: **************
    sni: zzzZZZZz.222769.XYZ
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: zzzZZZZz.222769.XYZ
      path: /Ws1FkyQlT190JQOSZb9TPR
  - name: "\U0001F1FA\U0001F1F8美国38 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /
  - name: "\U0001F1FA\U0001F1F8美国39 | ⬇️ 2.8MB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国40 | ⬇️ 2.4MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: a7c9c017-db10-4d15-b01b-0634db498b57
    ws-opts:
      headers:
        Host: XcdvfGBnHU7.0890604.XyZ
      path: /xZjr7v1DqrYyamxeTh7sLJtI1
    servername: XcdvfGBnHU7.0890604.XyZ
  - name: "\U0001F1E8\U0001F1E6加拿大2 | ⬇️ 1.2MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 288124da-0d68-42f4-9f48-70dc4dcc55a6
    ws-opts:
      headers:
        Host: 999O0.859886.xyz
      path: /e49RZLgIb0TdfgF5HdHEIupMZeK
    servername: 999O0.859886.xyz
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55
    name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 1.8MB/s"
    port: 37519
    protocol: udp
    server: **************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11
  - alterId: 0
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 1.2MB/s"
    port: 3686
    server: b6430370-t0fds0-t32kxf-dbk8.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 45845d5e-0605-11f0-8cf9-f23c93136cb3
  - name: "\U0001F1FA\U0001F1F8美国41 | ⬇️ 4.9MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国42 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alterId: 0
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰4 | ⬇️ 1.3MB/s"
    port: 3686
    server: 72843b8e-t0fds0-tgxeiy-3kis.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 117fc3d8-fd04-11ed-b345-f23c93136cb3
  - auth: be8cc8f6-0c6a-11f0-a5a3-f23c93141fad
    name: "\U0001F1FA\U0001F1F8美国43 | ⬇️ 5.8MB/s"
    password: be8cc8f6-0c6a-11f0-a5a3-f23c93141fad
    port: 1743
    server: 09dd7de5-t0fds0-t0szqc-b4pt.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 09dd7de5-t0fds0-t0szqc-b4pt.la.shifen.uk
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 1.8MB/s"
    network: ws
    password: Aimer
    port: 802
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 5.5MB/s"
    port: 3687
    server: 72843b8e-t0fds0-tgxeiy-3kis.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 117fc3d8-fd04-11ed-b345-f23c93136cb3
  - name: "\U0001F1E9\U0001F1EA德国7 | ⬇️ 3.8MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国44 | ⬇️ 2.5MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 509ed9b7-8d64-4204-8ec8-6b749020ac3f
    ws-opts:
      headers:
        Host: oo098.89060004.xyZ
      path: /VlJKGXt3aXBStn7
    servername: oo098.89060004.xyZ
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰5 | ⬇️ 2.7MB/s"
    network: ws
    port: 2096
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 5fe4abb7-92e6-4390-a17c-fd887f2f1c93
    ws-opts:
      headers:
        Host: www.dollardoon.com
      path: >-
        /@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7@V2RAYFAST_7?ed=2048
    servername: www.dollardoon.com
  - alpn:
      - http%2F1.1
    name: "\U0001F1E8\U0001F1E6加拿大3 | ⬇️ 1.5MB/s"
    network: ws
    password: 288124da-0d68-42f4-9f48-70dc4dcc55a6
    port: 443
    server: *************
    skip-cert-verify: true
    sni: rRfGty6.890606.XYz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: rRfGty6.890606.XYz
      path: /raChT39pjLFYRA5HdHEIupMZeK
  - name: "\U0001F1FA\U0001F1F8美国45 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F1E8\U0001F1E6加拿大4 | ⬇️ 1.7MB/s"
    network: ws
    password: 288124da-0d68-42f4-9f48-70dc4dcc55a6
    port: 443
    server: *************
    sni: rRfGty6.890606.XYz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: rRfGty6.890606.XYz
      path: /raChT39pjLFYRA5HdHEIupMZeK
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 7.6MB/s"
    port: 3687
    server: bd76bc68-t0fds0-t0yugh-dnss.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 16955d72-1794-11f0-a035-f23c95b6f51d
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 5.1MB/s"
    port: 3687
    server: c8e8974d-t0fds0-t0l7ae-4mce.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 06c625ea-5902-11ee-9e87-f23c9313b177
  - alpn:
      - http%2F1.1
    name: "\U0001F1FA\U0001F1F8美国46 | ⬇️ 4.1MB/s"
    network: ws
    password: 509ed9b7-8d64-4204-8ec8-6b749020ac3f
    port: 443
    server: ************
    sni: CcCVfGTyU.89060004.Xyz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: CcCVfGTyU.89060004.Xyz
      path: /5MkU0nARgHwk3aXBStn7
  - name: "\U0001F300其他1-PH | ⬇️ 3.9MB/s"
    network: ws
    password: Aimer
    port: 443
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1EB\U0001F1EE芬兰3 | ⬇️ 4.0MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1E9\U0001F1EA德国8 | ⬇️ 3.7MB/s"
    network: ws
    password: Aimer
    port: 443
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国47 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /Telegram@MxlShare@WangCai2/
    servername: sk.laoyoutiao.link
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 3.0MB/s"
    port: 3687
    server: 330f75ad-t0fds0-tckngf-5fdt.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 712bdc36-24be-11ee-be53-f23c9313b177
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国48 | ⬇️ 3.8MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 509ed9b7-8d64-4204-8ec8-6b749020ac3f
    ws-opts:
      headers:
        Host: oo098.89060004.xyZ
      path: /VlJKGXt3aXBStn7
    servername: oo098.89060004.xyZ
  - name: "\U0001F1FA\U0001F1F8美国49 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - name: "\U0001F1E9\U0001F1EA德国9 | ⬇️ 3.8MB/s"
    network: ws
    password: Aimer
    port: 2053
    server: **********
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾5 | ⬇️ 8.6MB/s"
    port: 3687
    server: 71dda0bb-t0fds0-t3fjp7-cdfn.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: *************-11f0-9ab7-f23c95b6f51d
  - name: "\U0001F1FA\U0001F1F8美国50 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /TelegramрЯЗ®рЯЗ≥
  - name: "\U0001F300其他2-AM | ⬇️ 2.7MB/s"
    network: ws
    port: 443
    server: cdnjs.com
    tls: true
    type: vless
    udp: true
    uuid: db400287-fb42-441d-8a76-5624a8a96f49
    ws-opts:
      headers:
        Host: rayan-roof.atena.dpdns.org
      path: /
    servername: rayan-roof.atena.dpdns.org
  - name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 3.6MB/s"
    network: ws
    port: 50000
    server: tp50000.kr.proxyip.fgfw.eu.org
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: test.saas.cname.123153.xyz
      path: /snippets
    servername: test.saas.cname.123153.xyz
  - name: "\U0001F300其他3-未识别 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /Telegram
    servername: us.laoyoutiao.link
  - auth: a16d62f6-048d-11f0-af5a-f23c93136cb3
    name: "\U0001F1FA\U0001F1F8美国51 | ⬇️ 7.9MB/s"
    password: a16d62f6-048d-11f0-af5a-f23c93136cb3
    port: 1743
    server: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
    sni: b9c3912b-t07z40-t1l1nh-d6ar.la.shifen.uk
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国52 | ⬇️ 3.0MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
    ws-opts:
      headers:
        Host: XXCsDERT6.777159.XyZ
      path: /O9jlBCbIm3xr1D40NK
    servername: XXCsDERT6.777159.XyZ
  - alterId: 0
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰6 | ⬇️ 1.1MB/s"
    port: 3686
    server: 0ae16477-t0fds0-t15y47-63bp.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 92b2a3d4-f353-11ef-b714-f23c93136cb3
  - name: "\U0001F1FA\U0001F1F8美国53 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ************8
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F300其他4-未识别 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 583ceab3-**************-9bedc625ad4e
    ws-opts:
      headers:
        Host: ip.langmanshanxi.top
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: ip.langmanshanxi.top
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国54 | ⬇️ 1.8MB/s"
    network: ws
    port: 443
    server: xcvbnnmklo.00890604.xyz
    tls: true
    type: vless
    udp: true
    uuid: a7c9c017-db10-4d15-b01b-0634db498b57
    ws-opts:
      headers:
        Host: xCVbNNMklo.00890604.xyZ
      path: /xZjr7v1DqrYyamxeTh7sLJtI1
    servername: xCVbNNMklo.00890604.xyZ
  - name: "\U0001F1EB\U0001F1EE芬兰4 | ⬇️ 1.1MB/s"
    network: ws
    password: Aimer
    port: 2096
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alpn:
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国55 | ⬇️ 3.6MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: ffcf7ec1-3e09-4821-b3d9-b426a107b73b
    ws-opts:
      headers:
        Host: EDfrT.frEEvPNatm2025.DPdNS.oRG
      path: /O9jlBCbIm3xr1D40NK
    servername: EDfrT.frEEvPNatm2025.DPdNS.oRG
  - auth: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    name: "\U0001F1FA\U0001F1F8美国56 | ⬇️ 6.1MB/s"
    password: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    port: 1743
    server: c7f65381-t0fds0-t15y47-63bp.la.shifen.uk
    type: hysteria2
    udp: true
    sni: c7f65381-t0fds0-t15y47-63bp.la.shifen.uk
  - name: "\U0001F1EB\U0001F1EE芬兰5 | ⬇️ 3.0MB/s"
    network: ws
    password: Aimer
    port: 2083
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - auth: 712bdc36-24be-11ee-be53-f23c9313b177
    name: "\U0001F1FA\U0001F1F8美国57 | ⬇️ 3.0MB/s"
    password: 712bdc36-24be-11ee-be53-f23c9313b177
    port: 1743
    server: 80e8c73c-t0fds0-tckngf-5fdt.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 80e8c73c-t0fds0-tckngf-5fdt.la.shifen.uk
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾6 | ⬇️ 3.9MB/s"
    port: 3687
    server: 0ae16477-t0fds0-t15y47-63bp.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 92b2a3d4-f353-11ef-b714-f23c93136cb3
  - alpn:
      - http%2F1.1
    name: "\U0001F1FA\U0001F1F8美国58 | ⬇️ 1.3MB/s"
    network: ws
    password: 0f7070cd-c91d-4532-a51f-56da4f0e94be
    port: 443
    server: ************
    sni: uuujjnmm.444752.xyz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: uuujjnmm.444752.xyz
      path: /ctHoQlqeZn8pbEUSLppj7jCmY
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国59 | ⬇️ 1.1MB/s"
    network: ws
    port: 80
    server: ***********
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: xxxdcr4.008880888.xyz
      path: /SHToRcqfiraYkzOdf
    servername: xxxdcr4.008880888.xYz
  - auth: 45845d5e-0605-11f0-8cf9-f23c93136cb3
    name: "\U0001F1FA\U0001F1F8美国60 | ⬇️ 1.5MB/s"
    password: 45845d5e-0605-11f0-8cf9-f23c93136cb3
    port: 1743
    server: 9967175b-t0fds0-t32kxf-dbk8.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 9967175b-t0fds0-t32kxf-dbk8.la.shifen.uk
  - name: "\U0001F1E9\U0001F1EA德国10 | ⬇️ 1.4MB/s"
    network: ws
    password: Aimer
    port: 12110
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alpn:
      - h2
    grpc-opts:
      grpc-service-name: 8y30nl6G2U0xOfo1zTYgBe8V
    name: "\U0001F1FA\U0001F1F8美国61 | ⬇️ 1.7MB/s"
    network: grpc
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: *************
    skip-cert-verify: false
    sni: ssXCDFRT.4444936.xYz
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国62 | ⬇️ 1.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alpn:
      - h2
    grpc-opts:
      grpc-service-name: 8y30nl6G2U0xOfo1zTYgBe8V
    name: "\U0001F1FA\U0001F1F8美国63 | ⬇️ 2.9MB/s"
    network: grpc
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: *************
    skip-cert-verify: false
    sni: OOOLLMki.222767.XYz
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国64 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 34c01e61-f4f3-4afb-a83b-406caf8caa33
    ws-opts:
      headers:
        Host: yg.bantony199.top
      path: /Telegram
  - alpn:
      - h2
    grpc-opts:
      grpc-service-name: 8y30nl6G2U0xOfo1zTYgBe8V
    name: "\U0001F1FA\U0001F1F8美国65 | ⬇️ 3.7MB/s"
    network: grpc
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: ***********
    skip-cert-verify: false
    sni: zzza23E4.TAiPEI102.DPdNS.ORg
    type: trojan
    udp: true
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国66 | ⬇️ 4.3MB/s"
    network: ws
    port: 80
    server: **************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: lllkjhgf.444682.xyz
      path: /SHToRcqfiraYkzOdf
    servername: LLlKJhgf.444682.xyZ
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国67 | ⬇️ 4.6MB/s"
    network: ws
    port: 10801
    server: ssm.pimax.com
    skip-cert-verify: true
    tls: true
    type: vmess
    udp: true
    uuid: 68876abb-0e56-4beb-b959-34a4bc57a6a2
    ws-opts:
      headers:
        Host: ssm.pimax.com
      path: /fkgevws
    servername: ssm.pimax.com
  - name: "\U0001F1FA\U0001F1F8美国68 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 34c01e61-f4f3-4afb-a83b-406caf8caa33
    ws-opts:
      headers:
        Host: yg.bantony199.top
      path: /Telegram
  - name: "\U0001F1FA\U0001F1F8美国69 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram@WangCai2/?ed=2560
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国70 | ⬇️ 4.0MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: c226ac5d-65e9-4379-95c3-fb542bc242d8
    ws-opts:
      headers:
        Host: HHhhhHhhhhH.777198.xYz
      path: /OjdW89Bpg4ykd4O
    servername: HHhhhHhhhhH.777198.xYz
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国71 | ⬇️ 4.1MB/s"
    network: ws
    password: 20e89a95-cedf-4fe3-9421-517ce66be170
    port: 443
    server: **************
    skip-cert-verify: false
    sni: lllKJHGF.444682.Xyz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: lllKJHGF.444682.Xyz
      path: /8y30nl6G2U0xOiraYkzOdf
  - name: "\U0001F1FA\U0001F1F8美国72 | ⬇️ 3.4MB/s"
    network: ws
    password: a96cb093-b164-4bc6-bd27-deb0e385de07
    port: 443
    server: ************
    sni: uuuUuuUuJ.999864.Xyz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: uuuUuuUuJ.999864.Xyz
      path: /CNLtVWdiKPpIlRig3qizHXb
  - name: "\U0001F300其他5-PH | ⬇️ 5.8MB/s"
    network: ws
    password: Aimer
    port: 443
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国73 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560fp=chrome"
  - name: "\U0001F1FA\U0001F1F8美国74 | ⬇️ 2.6MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F300其他6-未识别 | ⬇️ 4.4MB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /Telegram @MxlShare @WangCai2 /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国75 | ⬇️ 3.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - name: "\U0001F1FA\U0001F1F8美国76 | ⬇️ 4.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国77 | ⬇️ 3.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /
  - auth: 16955d72-1794-11f0-a035-f23c95b6f51d
    name: "\U0001F1FA\U0001F1F8美国78 | ⬇️ 4.2MB/s"
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1743
    server: 33dee576-t0fds0-t0yugh-dnss.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 33dee576-t0fds0-t0yugh-dnss.la.shifen.uk
  - alpn:
      - http%2F1.1
    name: "\U0001F1FA\U0001F1F8美国79 | ⬇️ 4.6MB/s"
    network: ws
    password: cf8c791e-9d0b-4e90-aaf6-41ac62468416
    port: 443
    server: ************
    sni: 66666666G.857856.xYz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: 66666666G.857856.xYz
      path: /XkJmZCwVxJO8180gomOew3d
  - auth: 6286105c-fb6d-11ef-be96-f23c93136cb3
    name: "\U0001F1FA\U0001F1F8美国80 | ⬇️ 6.3MB/s"
    password: 6286105c-fb6d-11ef-be96-f23c93136cb3
    port: 1743
    server: 1f05f904-t0fds0-t48q54-4550.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 1f05f904-t0fds0-t48q54-4550.la.shifen.uk
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国81 | ⬇️ 3.2MB/s"
    network: ws
    port: 443
    server: *************
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: dddcccvfg.999834.xyz
      path: /SHToRcqfiraYkzOdf
    servername: DDDcccvfg.999834.xYZ
  - name: "\U0001F1FA\U0001F1F8美国82 | ⬇️ 3.0MB/s"
    network: ws
    password: fa050497-fc2a-45ee-89c0-96670c4ecb65
    port: 443
    server: *************
    skip-cert-verify: true
    sni: Rrr4.8906004.xYZ
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: Rrr4.8906004.xYZ
      path: /DZxb5QZyWgQPuXTwt
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国83 | ⬇️ 4.0MB/s"
    network: ws
    port: 443
    server: dddfrrgttt.445.pp.ua
    tls: true
    type: vless
    udp: true
    uuid: c7f423b7-ced8-43da-a9ae-e906cb4a222c
    ws-opts:
      headers:
        Host: DDdFRRgtTt.445.Pp.ua
      path: /wU3lZaUTRQTqot0LE
    servername: DDdFRRgtTt.445.Pp.ua
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国84 | ⬇️ 4.5MB/s"
    network: ws
    port: 80
    server: *************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: ssszaqw.999834.xyz
      path: /SHToRcqfiraYkzOdf
    servername: SsszaQw.999834.xYZ
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国2 | ⬇️ 1.1MB/s"
    network: ws
    port: 80
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 2036e2c3-18a5-4eed-9db4-f91a7f02c7d5
    ws-opts:
      headers:
        Host: zoomgov.vipren.biz.id
      path: /**************=443
  - alpn:
      - http%2F1.1
    name: "\U0001F1FA\U0001F1F8美国85 | ⬇️ 1.5MB/s"
    network: ws
    password: e4cbe8b8-37db-4aaa-8469-b84f34c51ebc
    port: 443
    server: ************
    sni: 6666YHjU.777159.xYz
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: 6666YHjU.777159.xYz
      path: /dilyfCPEmLvYr5hYXD
  - name: "\U0001F1FA\U0001F1F8美国86 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - name: "\U0001F1EB\U0001F1EE芬兰6 | ⬇️ 1.9MB/s"
    network: ws
    password: Aimer
    port: 35307
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国87 | ⬇️ 5.9MB/s"
    password: 826038a8-1b2f-4ddc-a3e6-663011cadb15
    port: 10001
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1EB\U0001F1EE芬兰7 | ⬇️ 2.3MB/s"
    network: ws
    password: Aimer
    port: 2083
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国88 | ⬇️ 2.7MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国89 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国90 | ⬇️ 4.0MB/s"
    network: ws
    password: Aimer
    port: 443
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国91 | ⬇️ 2.6MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - auth: 9b6419f8-8120-442e-a48c-6bf52aef0abf
    down: 20
    name: "\U0001F1FA\U0001F1F8美国92 | ⬇️ 2.2MB/s"
    password: 9b6419f8-8120-442e-a48c-6bf52aef0abf
    port: 30001
    server: gy2.mlshu.top
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 20
    sni: gy2.mlshu.top
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾7 | ⬇️ 9.5MB/s"
    port: 3687
    server: d9edde97-t0fds0-t48q54-4550.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 6286105c-fb6d-11ef-be96-f23c93136cb3
  - alpn:
      - http%2F1.1
    name: "\U0001F1FA\U0001F1F8美国93 | ⬇️ 3.6MB/s"
    network: ws
    password: 2e65577e-8fdb-4bb1-a495-96f3122099a7
    port: 443
    server: ************
    sni: zzzZZZZz.222769.XYZ
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: zzzZZZZz.222769.XYZ
      path: /Ws1FkyQlT190JQOSZb9TPR
  - name: "\U0001F1FA\U0001F1F8美国94 | ⬇️ 4.9MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国95 | ⬇️ 3.9MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国96 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1E9\U0001F1EA德国11 | ⬇️ 3.9MB/s"
    network: ws
    password: Aimer
    port: 2053
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国97 | ⬇️ 3.4MB/s"
    network: ws
    password: 07a3df8f-2a2c-42f8-ad92-65889d90f3bf
    port: 443
    server: ************
    skip-cert-verify: false
    sni: rrrRrRRrT.459.pp.ua
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: rrrRrRRrT.459.pp.ua
      path: /znQImc22ijDwVOkZfoq
  - name: "\U0001F1E9\U0001F1EA德国12 | ⬇️ 1.4MB/s"
    network: ws
    password: Aimer
    port: 2087
    server: ***********
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1E9\U0001F1EA德国13 | ⬇️ 5.2MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1E9\U0001F1EA德国14 | ⬇️ 6.4MB/s"
    network: ws
    password: Aimer
    port: 443
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国98 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - alterId: 0
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 3.6MB/s"
    port: 23001
    server: tls.16.node-for-bigairport.win
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: c69374da-2208-4cbd-b81e-cdf88b5e7f53
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾8 | ⬇️ 7.2MB/s"
    port: 3687
    server: 47fecf30-t0fds0-tclkqs-deip.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 7a2512b4-09ee-11f0-8d46-f23c9313b177
  - name: "\U0001F1FA\U0001F1F8美国99 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560fp=chrome
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国100 | ⬇️ 4.1MB/s"
    network: ws
    port: 443
    server: ************
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: rrrrrr5.857657.xyz
      path: /SHToRcqfiraYkzOdf
    servername: rRRRRR5.857657.xyZ
  - name: "\U0001F1EB\U0001F1EE芬兰8 | ⬇️ 1.7MB/s"
    network: ws
    password: Aimer
    port: 2096
    server: ************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国101 | ⬇️ 4.1MB/s"
    network: ws
    password: f108e0e2-5f12-42b6-9e67-1b2f073ffb2b
    port: 443
    server: **************
    skip-cert-verify: false
    sni: CCcvfgt6.852224.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: CCcvfgt6.852224.dpdns.org
      path: /CA5bMmr2JMum8sDKRwvFCJq
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾9 | ⬇️ 6.4MB/s"
    port: 3687
    server: e4c957b1-t0fds0-t2zeef-d70y.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 537d7098-fdbe-11ef-8ebc-f23c93141fad
  - name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 1.4MB/s"
    network: ws
    port: 443
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: b7f8c66b-40f0-4160-8b88-76a482e2b593
    ws-opts:
      headers:
        Host: rs-sg4.lzjnb.shop
      path: /lzj
    servername: rs-sg4.lzjnb.shop
  - auth: df7bed3c-06eb-11f0-8ebc-f23c93141fad
    name: "\U0001F1FA\U0001F1F8美国102 | ⬇️ 3.2MB/s"
    password: df7bed3c-06eb-11f0-8ebc-f23c93141fad
    port: 1743
    server: 029b613a-t0fds0-t9lb2a-1glq.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 029b613a-t0fds0-t9lb2a-1glq.la.shifen.uk
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾10 | ⬇️ 9.7MB/s"
    port: 3687
    server: b6430370-t0fds0-t32kxf-dbk8.77.iwskwai.com
    tls: false
    type: vmess
    udp: true
    uuid: 45845d5e-0605-11f0-8cf9-f23c93136cb3
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国103 | ⬇️ 2.0MB/s"
    network: ws
    port: 80
    server: ***********
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    ws-opts:
      headers:
        Host: cccccccvvf.008880888.xyz
      path: /SHToRcqfiraYkzOdf
    servername: ccCCcCcVvf.008880888.xYz
  - alterId: 0
    cipher: auto
    grpc-opts:
      grpc-service-name: SHToRcqffo1zTYgBe8V
    name: "\U0001F1FA\U0001F1F8美国104 | ⬇️ 3.3MB/s"
    network: grpc
    port: 443
    server: *************
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    servername: oOoKKKjHGF.4444936.xyZ
  - auth: *************-11f0-9ab7-f23c95b6f51d
    name: "\U0001F1FA\U0001F1F8美国105 | ⬇️ 2.8MB/s"
    password: *************-11f0-9ab7-f23c95b6f51d
    port: 1743
    server: 6647d8f1-t0fds0-t3fjp7-cdfn.la.shifen.uk
    type: hysteria2
    udp: true
    sni: 6647d8f1-t0fds0-t3fjp7-cdfn.la.shifen.uk
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 1.0MB/s"
    network: ws
    port: 80
    server: hkt.gotochinatown.net
    skip-cert-verify: true
    tls: false
    type: vmess
    udp: true
    uuid: 715a2574-9c25-11eb-8673-f23c9164ca5d
    ws-opts:
      headers:
        Host: hkt.gotochinatown.net
      path: /
    servername: broadcastlv.chat.bilibili.com
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 1.0MB/s"
    network: ws
    port: 80
    server: hkt.gotochinatown.net
    tls: false
    type: vmess
    udp: true
    uuid: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
  - name: "\U0001F300其他7-未识别 | ⬇️ 1.5MB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 2.3MB/s"
    network: ws
    port: 80
    server: hkt.gotochinatown.net
    tls: false
    type: vmess
    udp: true
    uuid: c0a2c394-e27b-11ee-91f6-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
  - name: "\U0001F1FA\U0001F1F8美国106 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 34c01e61-f4f3-4afb-a83b-406caf8caa33
    ws-opts:
      headers:
        Host: yg.bantony199.top
      path: /Telegram
    servername: Telegram-channel-WangCai2
  - client-fingerprint: chrome
    name: "\U0001F1EB\U0001F1EE芬兰9 | ⬇️ 1.4MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: e6236ebc-7334-4bed-977f-0c20bcdfcc00
    ws-opts:
      headers:
        Host: barayeiranmahsang.ghormehsabzi.dpdns.org
      path: /Join---hibyevpn---Join---hibyevpn?ed=2560
    servername: barayeiranmahsang.ghormehsabzi.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国107 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
    servername: sk.laoyoutiao.link
  - name: "\U0001F1FA\U0001F1F8美国108 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: /Telegram @WangCai2 /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国109 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: sk.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3
  - name: "\U0001F1EB\U0001F1EE芬兰10 | ⬇️ 4.2MB/s"
    network: ws
    port: 443
    server: sage.ns.cloudflare.com
    tls: true
    type: vless
    udp: true
    uuid: ee6774c0-9b19-4ff1-8b30-2da4b71977e2
    ws-opts:
      headers:
        Host: edga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
    servername: edga.aimercc.dpdns.org
  - alpn:
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国110 | ⬇️ 3.1MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 662e38ba-8427-4955-94aa-76f5347a0ce8
    ws-opts:
      headers:
        Host: eeEEEeR.666470.xYZ
      path: /6DuxYMYmrGrnGKRtF5UvWyyVQu
    servername: eeEEEeR.666470.xYZ
  - name: "\U0001F1EB\U0001F1EE芬兰11 | ⬇️ 3.9MB/s"
    network: ws
    password: Aimer
    port: 8443
    server: **************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国111 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 0132166f-e702-48ed-a9e7-b07af768faf8
    ws-opts:
      headers:
        Host: cf.d3z.net
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - alterId: 0
    cipher: auto
    grpc-opts:
      grpc-service-name: SHToRcqffo1zTYgBe8V
    name: "\U0001F1FA\U0001F1F8美国112 | ⬇️ 6.1MB/s"
    network: grpc
    port: 443
    server: ***********
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 20e89a95-cedf-4fe3-9421-517ce66be170
    servername: ZzZA23E4.tAIPEI102.dpDns.ORg
  - name: "\U0001F1EB\U0001F1EE芬兰12 | ⬇️ 1.8MB/s"
    network: ws
    password: Aimer
    port: 46741
    server: *************
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - name: "\U0001F1FA\U0001F1F8美国113 | ⬇️ 1.6MB/s"
    network: ws
    password: Aimer
    port: 443
    server: sage.ns.cloudflare.com
    skip-cert-verify: true
    sni: epga.aimercc.dpdns.org
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: epga.aimercc.dpdns.org
      path: /?ed=2560&proxyip=ts.hpc.tw
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国114 | ⬇️ 2.4MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 0f7070cd-c91d-4532-a51f-56da4f0e94be
    ws-opts:
      headers:
        Host: SssXzAw.444652.XYZ
      path: /nSABZLQbEUSLppj7jCmY
    servername: SssXzAw.444652.XYZ
  - name: "\U0001F1FA\U0001F1F8美国115 | ⬇️ 3.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: b5441b0d-2147-4898-8a6a-9b2c87f58382
    ws-opts:
      headers:
        Host: bitget1.asdasd.click
      path: "/Telegram\U0001F1E8\U0001F1F3"
    servername: Telegram-channel-WangCai2
  - name: "\U0001F1FA\U0001F1F8美国116 | ⬇️ 3.7MB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /Telegram
proxy-groups:
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F680 手动切换"
    include-all: true
    type: select
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    interval: 300
    tolerance: 50
  - name: "\U0001F4F2 电报消息"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4AC Ai平台"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4F9 油管视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F3A5 奈飞视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F3A5 奈飞节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 巴哈姆特"
    type: select
    proxies:
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F680 节点选择"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 哔哩哔哩"
    type: select
    proxies:
      - "\U0001F3AF 全球直连"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
  - name: "\U0001F30D 国外媒体"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F30F 国内媒体"
    type: select
    proxies:
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F4E2 谷歌FCM"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软Bing
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软云盘
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软服务
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - DIRECT
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F34E 苹果服务"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3AE 游戏平台"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3B6 网易音乐"
    type: select
    include-all: true
    filter: (?i)网易|音乐|NetEase|Music
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F6D1 广告拦截"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F343 应用净化"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F41F 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    include-all: true
    filter: (?i)港|HK|hk|Hong Kong|HongKong|hongkong
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    include-all: true
    filter: (?i)日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1FA\U0001F1F2 美国节点"
    include-all: true
    filter: (?i)美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1E8\U0001F1F3 台湾节点"
    include-all: true
    filter: (?i)台|新北|彰化|TW|Taiwan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F8\U0001F1EC 狮城节点"
    include-all: true
    filter: (?i)新加坡|坡|狮城|SG|Singapore
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    include-all: true
    filter: (?i)KR|Korea|KOR|首尔|韩|韓
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F3A5 奈飞节点"
    include-all: true
    filter: (?i)NF|奈飞|解锁|Netflix|NETFLIX|Media
    type: select
rule-providers:
  LocalAreaNetwork:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/LocalAreaNetwork.list
    path: ./ruleset/LocalAreaNetwork.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  UnBan:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/UnBan.list'
    path: ./ruleset/UnBan.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanAD:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanAD.list'
    path: ./ruleset/BanAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanProgramAD:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanProgramAD.list
    path: ./ruleset/BanProgramAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleFCM:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/GoogleFCM.list
    path: ./ruleset/GoogleFCM.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/GoogleCN.list
    path: ./ruleset/GoogleCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  SteamCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/SteamCN.list
    path: ./ruleset/SteamCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bing:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Bing.list'
    path: ./ruleset/Bing.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OneDrive:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/OneDrive.list
    path: ./ruleset/OneDrive.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Microsoft:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Microsoft.list
    path: ./ruleset/Microsoft.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Apple:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Apple.list'
    path: ./ruleset/Apple.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Telegram:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Telegram.list
    path: ./ruleset/Telegram.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  AI:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/AI.list
    path: ./ruleset/AI.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OpenAi:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/OpenAi.list
    path: ./ruleset/OpenAi.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  NetEaseMusic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/NetEaseMusic.list
    path: ./ruleset/NetEaseMusic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Epic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Epic.list
    path: ./ruleset/Epic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Origin:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Origin.list
    path: ./ruleset/Origin.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Sony:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Sony.list
    path: ./ruleset/Sony.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Steam:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Steam.list
    path: ./ruleset/Steam.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Nintendo:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Nintendo.list
    path: ./ruleset/Nintendo.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  YouTube:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/YouTube.list
    path: ./ruleset/YouTube.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Netflix:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Netflix.list
    path: ./ruleset/Netflix.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bahamut:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bahamut.list
    path: ./ruleset/Bahamut.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BilibiliHMT:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/BilibiliHMT.list
    path: ./ruleset/BilibiliHMT.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bilibili:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bilibili.list
    path: ./ruleset/Bilibili.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaMedia.list
    path: ./ruleset/ChinaMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyMedia.list
    path: ./ruleset/ProxyMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyGFWlist:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyGFWlist.list
    path: ./ruleset/ProxyGFWlist.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaDomain:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaDomain.list
    path: ./ruleset/ChinaDomain.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaCompanyIp:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaCompanyIp.list
    path: ./ruleset/ChinaCompanyIp.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Download:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Download.list
    path: ./ruleset/Download.list
    behavior: classical
    interval: 86400
    format: text
    type: http
rules:
  - "PROCESS-NAME,subs-check.exe,\U0001F3AF 全球直连"
  - "PROCESS-NAME,subs-check,\U0001F3AF 全球直连"
  - "RULE-SET,LocalAreaNetwork,\U0001F3AF 全球直连"
  - "RULE-SET,UnBan,\U0001F3AF 全球直连"
  - "RULE-SET,BanAD,\U0001F6D1 广告拦截"
  - "RULE-SET,BanProgramAD,\U0001F343 应用净化"
  - "RULE-SET,GoogleFCM,\U0001F4E2 谷歌FCM"
  - "RULE-SET,GoogleCN,\U0001F3AF 全球直连"
  - "RULE-SET,SteamCN,\U0001F3AF 全球直连"
  - 'RULE-SET,Bing,Ⓜ️ 微软Bing'
  - 'RULE-SET,OneDrive,Ⓜ️ 微软云盘'
  - 'RULE-SET,Microsoft,Ⓜ️ 微软服务'
  - "RULE-SET,Apple,\U0001F34E 苹果服务"
  - "RULE-SET,Telegram,\U0001F4F2 电报消息"
  - "RULE-SET,AI,\U0001F4AC Ai平台"
  - "RULE-SET,NetEaseMusic,\U0001F3B6 网易音乐"
  - "RULE-SET,Epic,\U0001F3AE 游戏平台"
  - "RULE-SET,Origin,\U0001F3AE 游戏平台"
  - "RULE-SET,Sony,\U0001F3AE 游戏平台"
  - "RULE-SET,Steam,\U0001F3AE 游戏平台"
  - "RULE-SET,Nintendo,\U0001F3AE 游戏平台"
  - "RULE-SET,YouTube,\U0001F4F9 油管视频"
  - "RULE-SET,Netflix,\U0001F3A5 奈飞视频"
  - "RULE-SET,Bahamut,\U0001F4FA 巴哈姆特"
  - "RULE-SET,BilibiliHMT,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,Bilibili,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,ChinaMedia,\U0001F30F 国内媒体"
  - "RULE-SET,ProxyMedia,\U0001F30D 国外媒体"
  - "RULE-SET,ProxyGFWlist,\U0001F680 节点选择"
  - "RULE-SET,ChinaDomain,\U0001F3AF 全球直连"
  - "RULE-SET,ChinaCompanyIp,\U0001F3AF 全球直连"
  - "RULE-SET,Download,\U0001F3AF 全球直连"
  - "GEOIP,CN,\U0001F3AF 全球直连"
  - "MATCH,\U0001F41F 漏网之鱼"
