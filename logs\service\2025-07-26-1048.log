Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-26T10:48:27.974884800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T10:48:27.981002700+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-26T10:48:27.981002700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T10:48:27.981002700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-26T10:48:27.984712000+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-07-26T10:48:27.985222200+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-26T10:48:28.196436500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118688"
time="2025-07-26T10:48:28.200956400+08:00" level=info msg="Initial configuration complete, total time: 223ms"
time="2025-07-26T10:48:28.221827600+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-26T10:48:28.863047800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-26T10:48:28.910385800+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-26T10:48:28.913933500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-26T10:48:28.914597900+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-26T10:48:28.916881500+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-26T10:48:28.921311700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-26T10:48:28.921311700+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-26T10:48:28.925966600+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-07-26T10:48:28.963114400+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-26T10:48:30.053883400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T10:48:30.053883400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T10:48:30.053883400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-26T10:48:30.053883400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-26T10:48:30.402800100+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-26T10:48:30.477597800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:30.494836000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:30.494836000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:48:30.510972600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:30.518045600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:30.679063300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:30.679574900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:30.759243500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:30.813670600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:30.816187600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:30.816187600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:30.829362300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:30.829871400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:30.829871400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:30.830954900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:30.834518500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:30.835028900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:30.835028900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.011190300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.146876500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.146876500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.358919800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:31.434525200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:31.499057300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:31.500089700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.500595600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.633826700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.692366200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:31.740532000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.742938700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.758121800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:31.759199000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:31.760451800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:31.784490500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:31.788808600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:31.793774800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.794282500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.796307000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:31.796811900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.797893600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:31.942682200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:31.969107800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:31.971552400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:32.016546300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.017626600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.034438600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:32.151296100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.238925000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.252081400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:32.286918000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.334838100+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-26T10:48:32.386900900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:32.419668200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:32.419668200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:32.426080700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:32.426585900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:32.426585900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:32.427094100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:32.429679000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:32.430186300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:32.430186300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:32.433699200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.496540700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:32.501256600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.550242400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:32.584115800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.630014900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:32.647443200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:32.739582600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.751209900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:32.764145300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:32.808069500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:32.875882600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:32.981304000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:32.992662400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.069531100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.071579400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:33.091787700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.223548400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:33.228016300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:33.368284500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.409057400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.440444500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:33.536869100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:33.575314500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-26T10:48:33.675279600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:33.716081300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:33.752836300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:33.918606000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp 8.210.174.30:443: i/o timeout"
time="2025-07-26T10:48:33.918606000+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-26T10:48:33.928144600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.121378200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.122810500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.223042700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:34.336405900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.352971100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:34.355222100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.356991500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:34.357495800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.357495800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:34.357495800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.357495800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.360324200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.364689500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.404573200+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.535809300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.537633100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.538142200+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.638054500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.647774100+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.658192600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.694458100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:34.759200700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:34.769154000+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.812270600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:34.918511300+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:34.928261300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-26T10:48:34.983243800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:35.054619200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-26T10:48:35.061971200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:35.357293000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:35.441450700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58521 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:35.476076700+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": EOF"
time="2025-07-26T10:48:36.023352000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:58501 --> ssr.cnring.shop:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: context deadline exceeded"
time="2025-07-26T10:48:36.295389900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:36.295901500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: context canceled"
time="2025-07-26T10:48:36.297997400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.301978100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:36.303319100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.303319100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:36.303878000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:36.303878000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.306623300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.307349400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.363353000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:36.404633900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.416508800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.437054400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:36.455629900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:36.518994400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.518994400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:36.522191100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:36.522704700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: context canceled"
time="2025-07-26T10:48:36.533152300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.545647300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:36.545647300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:36.620550400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:36.626945700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.634344400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.695037800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:58802 --> services.gfe.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.743900500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.744414800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:36.782136100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.783640300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:36.787177900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:36.787177900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:36.787688000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:36.787688000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: context canceled"
time="2025-07-26T10:48:36.795777300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:36.855179400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:36.928371400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:37.005877700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.043008100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:37.072285600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:37.112073100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.241738800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:37.367332000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:37.369953200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:37.372276000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:37.374793100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:37.375905200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.377967500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.380102600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:37.380614600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.384023600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:37.385553300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.389732700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:37.390852900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.495613000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.509351400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.557523900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.619372400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.632395800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.744088600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.759646500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:37.825573400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:37.982075700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:37.983596300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:37.985323600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:37.986391300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:37.986391300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:37.986902500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:37.988427000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.103800600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.227807900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.361957100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.463028500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.463542900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:38.463542900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.465662200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.467220200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:38.467220200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:38.467220200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.468950900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:38.471502800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.499617900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:38.506273900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.570006000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.573070300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.576321000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.576321000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:38.576321000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:38.576832700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:38.577343100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.577343100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:38.578358900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:38.578358900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:38.647249600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:38.669917700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.684962100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.686858700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.708599500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.730862500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.780392000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:38.780392000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.800370400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:38.811613200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.847642700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:38.874748800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:38.874748800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:38.880368300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:38.880368300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:38.880877400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:38.880877400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:38.918927100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: dial tcp 157.148.132.106:65501: i/o timeout"
time="2025-07-26T10:48:38.918927100+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-26T10:48:38.978233000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.979276500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.979789500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:38.981544900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:38.989869400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:39.023428000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:39.090137200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.090657200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.101069100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.119105900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.133684000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:39.134197200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: context canceled"
time="2025-07-26T10:48:39.180937800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:39.192140600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.193269300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.204112500+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60804 --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-26T10:48:39.237391100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.241074600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:39.305396700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:39.307162200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.387901600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:39.387901600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.392481300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:39.394018600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:39.403010000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59502 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.407874100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:39.428724000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.429252900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.429764100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:39.440384800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.513895600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:39.517716200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59502 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:39.549174400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:39.566560100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.570307400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.601808900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.609976200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.614499300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.705959500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.827747100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.829071500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.836243500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.919603100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:39.960078600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:39.962182800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:39.984329500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.059572200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.090369200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.112753100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.113774400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.187410600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:40.193548500+08:00" level=error msg="Free-6 | ❓_🇬🇧_💻_github.com/Ruk1ng001_6432663864326466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-26T10:48:40.193548500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T10:48:40.227625700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.262268100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.332931000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.433232200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:40.437700900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.482356400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.488977200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.518642400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.672760700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.680214600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59595 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.753086500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:40.753086500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.805163900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59229 --> gfwsl.geforce.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:40.884978900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:40.892680900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:40.928985000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:40.972193800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:41.137737600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.147754500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59379 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:41.204193700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59248 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:41.220651300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.297347500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.301935000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:41.310074900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59232 --> api-prod.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: dial tcp 157.148.132.106:65109: i/o timeout"
time="2025-07-26T10:48:41.408625000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.412645900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:41.448656100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:41.457210800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.521179700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.559533000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:41.647851900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.690738800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.771558200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.797347800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:41.838289600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59595 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:41.882558700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:41.924735200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.015214000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.016251500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:42.097920600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.150374100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:42.190117000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59663 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.235433200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.281488500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59322 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: context deadline exceeded"
time="2025-07-26T10:48:42.416109300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59462 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.426718900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59721 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.448924600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:42.535478000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59721 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.598328400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:42.600939800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:42.646041300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59721 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.711574900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:42.713143900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:42.740346500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59426 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.779927000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59721 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.889829200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59428 --> windows.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:42.992560800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59595 --> www.msn.com:443 error: cnsg.1ga4cs.ccddn4.icu:65523 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg3.cc.tjcct.xyz, not dl.sg3.baidu.com"
time="2025-07-26T10:48:43.183592600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:59401 --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp 8.210.174.30:443: i/o timeout"
time="2025-07-26T10:48:43.335926700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59419 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:43.400302000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59431 --> main.vscode-cdn.net:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: context deadline exceeded"
time="2025-07-26T10:48:43.580671900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59436 --> cacerts.digicert.com:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: context deadline exceeded"
time="2025-07-26T10:48:43.658155200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:43.662078000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:43.665446400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:43.668241300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:43.672182100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:43.701956200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:43.761233800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59502 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:43.837219900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:43.876673600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:44.169848900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.295258300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59502 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: context deadline exceeded"
time="2025-07-26T10:48:44.330381400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.336818700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:44.401594400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:44.407293900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.417064100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.510674300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.515262000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:44.515262000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:44.518078700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.521647300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.613795600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.616671800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.640504500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:44.669791400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.720353700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65022 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg6.cc.tjcct.xyz, not dl.sg6.baidu.com"
time="2025-07-26T10:48:44.734338100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:44.748103600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:44.749207900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:44.749207900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:44.796221700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.829711200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:44.913987900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59596 --> config.edge.skype.com:443 error: cnsg.1ga4cs.ccddn4.icu:65522 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg1.cc.tjcct.xyz, not dl.sg1.baidu.com"
time="2025-07-26T10:48:44.949745400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.053932700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-26T10:48:45.302475300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:45.391852200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:45.450243300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:45.618613300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:45.622723200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:45.700213100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:45.847172300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:45.874346200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:46.187023000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:46.348387400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:46.422844200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:46.573969600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:46.670471900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:46.702357900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:46.709068900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:46.779442400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:46.924154000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:47.010344800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:47.029061000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59730 --> edge.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:47.730337400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:47.787567200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:47.787567200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:47.789632700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:47.790654800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:47.790654800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:47.792648300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:47.792648300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:47.803432700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:47.917442200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:47.984828900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.028367700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.031565700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:48.052345000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:48.135097700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.155324200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.291423700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.484696600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.566831600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.680360000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:48.685025800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:48.695161300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:48.725666600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59833 --> prod.otel.kaizen.nvidia.com:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:48.737406400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:48.914815400+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-26T10:48:49.158645600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:49.204892000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:49.240301400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59816 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:49.369728600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:49.664921900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:49.720019700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:50.316874700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:50.826888900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:51.025765900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:59979 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65300 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp4.cc.tjcct.xyz, not dl.bgp4.baidu.com"
time="2025-07-26T10:48:51.294897900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60048 --> mobile.events.data.microsoft.com:443 error: cnsg.1ga4cs.ccddn4.icu:65110 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp8.cc.tjcct.xyz, not dl.bgp8.baidu.com"
time="2025-07-26T10:48:51.631509800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:51.632020500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:51.635397100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp3.cc.tjcct.xyz, not dl.bgp3.baidu.com"
time="2025-07-26T10:48:51.635397100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65301 connect error: context canceled"
time="2025-07-26T10:48:51.637485100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:51.637993400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:51.642459300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:51.642459300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:51.783566500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:51.799234000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:51.803946900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:51.848692400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:51.848692400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:51.849718300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:51.883320800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:52.438037500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:52.443545800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:52.448197800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:52.448701300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:52.450738100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:52.451248900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:48:52.452269600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.bgp7.cc.tjcct.xyz, not dl.bgp7.baidu.com"
time="2025-07-26T10:48:52.452779500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: cnsg.1ga4cs.ccddn4.icu:65109 connect error: context canceled"
time="2025-07-26T10:48:53.130636100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.252894500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.338532200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.382167600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.566194700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:48:53.580618900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.838099400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:53.843712800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:54.388083500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:54.502440200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:54.586124300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:54.691798800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:55.582292000+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:55.860133300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:56.260061100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:56.701622300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:56.829502900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:56.865328200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:48:57.817580200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60293 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-26T10:48:57.965411200+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:60319 --> api2.cursor.sh:443 error: gzcm.g0nyy3.ccddn4.icu:65217 connect error: context deadline exceeded"
time="2025-07-26T10:48:57.980268800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.017025400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.387160400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.389054300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.389555700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.476932700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-26T10:49:00.476932700+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": EOF"
time="2025-07-26T10:49:00.525626100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-26T10:49:00.525626100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-26T10:49:01.836805400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:01.836805400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: context canceled"
time="2025-07-26T10:49:03.826687700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:03.827197100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:04.816344000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60679 --> oneclient.sfx.ms:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:08.409129700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:08.915042600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-26T10:49:09.401130100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60749 --> static.edge.microsoftapp.net:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:09.631633400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:11.472439500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:12.792413300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:12.799647500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:14.317609900+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-26T10:49:15.023930400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:15.127250300+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> 52pokemon.xz61.cn:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:15.412159000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-26T10:49:16.541014300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-26T10:49:16.989655100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:49:17.248990100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:17.478013700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:19.300593200+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-26T10:49:27.472215500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:28.359647200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:28.582713500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:40.581605700+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gh-proxy.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:49:47.073602500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:49:49.266696400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:62107 --> x1.c.lencr.org:80 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:08.915406600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-26T10:50:12.646200500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:62510 --> spc4.s3.ap-east-1.amazonaws.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:12.758931300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:62510 --> spc4.s3.ap-east-1.amazonaws.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:12.895214200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:62510 --> spc4.s3.ap-east-1.amazonaws.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:12.946851700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62535 --> weatheroffer.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:12.948866100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62536 --> weatheroffer.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:13.008956200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:62510 --> spc4.s3.ap-east-1.amazonaws.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:13.052042100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62536 --> weatheroffer.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:13.055096500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62535 --> weatheroffer.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:13.186969100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62536 --> weatheroffer.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:21.161808500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62680 --> www.bingapis.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:22.319146300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62680 --> www.bingapis.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:22.426198200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62680 --> www.bingapis.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:22.565248300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62680 --> www.bingapis.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:22.679944000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62680 --> www.bingapis.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:26.878096700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:50:31.084252300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:63030 --> m.stripe.network:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:32.236788200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:63030 --> m.stripe.network:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:35.935946300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-26T10:50:36.150130300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-26T10:50:37.087340500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-26T10:50:40.650833100+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-26T10:50:42.209836900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60233 --> edge-consumer-static.azureedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:50:56.209430100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:51:37.813541700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:51:43.206013300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61994 --> my.microsoftpersonalcontent.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:51:43.309104600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61994 --> my.microsoftpersonalcontent.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:51:43.422636700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61994 --> my.microsoftpersonalcontent.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:51:43.546713400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61994 --> my.microsoftpersonalcontent.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:51:46.216475200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62128 --> api.onedrive.com:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:51:48.915751600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-26T10:51:52.862570200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62401 --> skyapi.live.net:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:52:06.177502800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:52:49.918818600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:53:16.088559300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-26T10:53:17.602597000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-26T10:53:17.764844800+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-26T10:53:19.494546300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:53:21.028459000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-26T10:53:39.411707200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65439 --> fp.msedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:53:39.525296500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:65439 --> fp.msedge.net:443 error: cnsg.1ga4cs.ccddn4.icu:65501 connect error: tls: failed to verify certificate: x509: certificate is valid for dl.sg2.cc.tjcct.xyz, not dl.sg2.baidu.com"
time="2025-07-26T10:54:02.410084700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:54:31.533968300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-26T10:54:48.916042700+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-26T10:55:12.074447600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6636346264353037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-26T10:55:12.074447600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
