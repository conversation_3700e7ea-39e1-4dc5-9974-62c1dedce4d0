Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-27T19:17:52.020283800+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-27T19:17:52.025925900+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-06-27T19:17:52.025925900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-27T19:17:52.026437700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-27T19:17:52.030556300+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-06-27T19:17:52.031063500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-27T19:17:52.230485400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119081"
time="2025-06-27T19:17:52.234524600+08:00" level=info msg="Initial configuration complete, total time: 211ms"
time="2025-06-27T19:17:52.237026600+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-06-27T19:17:52.237026600+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-06-27T19:17:52.237026600+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-27T19:17:52.255219400+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-06-27T19:17:52.693027100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-06-27T19:17:52.877866100+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.878377500+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.885164200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:52.885164200+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.889159500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:52.889159500+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.892237000+08:00" level=warning msg="[Provider] cordcloud-2 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.892237000+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.895830400+08:00" level=warning msg="[Provider] cordcloud-1 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.928832800+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-06-27T19:17:52.935563800+08:00" level=warning msg="[Provider] telegramcidr not updated for a long time, force refresh"
time="2025-06-27T19:17:52.937611200+08:00" level=warning msg="[Provider] apple not updated for a long time, force refresh"
time="2025-06-27T19:17:52.938633300+08:00" level=warning msg="[Provider] claude not updated for a long time, force refresh"
time="2025-06-27T19:17:52.939140900+08:00" level=warning msg="[Provider] gemini not updated for a long time, force refresh"
time="2025-06-27T19:17:52.939654300+08:00" level=warning msg="[Provider] private not updated for a long time, force refresh"
time="2025-06-27T19:17:52.943630300+08:00" level=warning msg="[Provider] icloud not updated for a long time, force refresh"
time="2025-06-27T19:17:52.946019800+08:00" level=warning msg="[Provider] github not updated for a long time, force refresh"
time="2025-06-27T19:17:52.946019800+08:00" level=warning msg="[Provider] netflix not updated for a long time, force refresh"
time="2025-06-27T19:17:52.947546000+08:00" level=warning msg="[Provider] tld-not-cn not updated for a long time, force refresh"
time="2025-06-27T19:17:52.952301200+08:00" level=warning msg="[Provider] openai not updated for a long time, force refresh"
time="2025-06-27T19:17:52.952620700+08:00" level=warning msg="[Provider] games-cn not updated for a long time, force refresh"
time="2025-06-27T19:17:52.954121500+08:00" level=warning msg="[Provider] google not updated for a long time, force refresh"
time="2025-06-27T19:17:52.954121500+08:00" level=warning msg="[Provider] applications not updated for a long time, force refresh"
time="2025-06-27T19:17:52.956476700+08:00" level=warning msg="[Provider] copilot not updated for a long time, force refresh"
time="2025-06-27T19:17:52.959078500+08:00" level=warning msg="[Provider] microsoft not updated for a long time, force refresh"
time="2025-06-27T19:17:52.959591200+08:00" level=warning msg="[Provider] google-rules not updated for a long time, force refresh"
time="2025-06-27T19:17:52.993947800+08:00" level=warning msg="[Provider] gfw not updated for a long time, force refresh"
time="2025-06-27T19:17:53.118024600+08:00" level=warning msg="[Provider] cncidr not updated for a long time, force refresh"
time="2025-06-27T19:17:53.237599600+08:00" level=warning msg="[Provider] advertising not updated for a long time, force refresh"
time="2025-06-27T19:17:53.269266800+08:00" level=warning msg="[Provider] proxy not updated for a long time, force refresh"
time="2025-06-27T19:17:53.787083300+08:00" level=warning msg="[Provider] direct not updated for a long time, force refresh"
time="2025-06-27T19:17:53.873658200+08:00" level=warning msg="[Provider] reject not updated for a long time, force refresh"
time="2025-06-27T19:17:54.860775200+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-06-27T19:17:54.860775200+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-27T19:17:55.389663600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:55.833405000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:56.467569600+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
time="2025-06-27T19:17:57.190568000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:57.878961700+08:00" level=error msg="PKM2 | 剩余流量：59.88 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:17:57.878961700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:17:57.889541000+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:17:57.889541000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:17:57.889541000+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:17:57.889541000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:17:58.540454100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:17:59.270022900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-27T19:17:59.270022900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-27T19:17:59.270022900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-27T19:17:59.270022900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-27T19:18:03.940427600+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-06-27T19:18:03.940427600+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-06-27T19:18:03.940427600+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-06-27T19:18:06.251560600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6662343730366236 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:06.251560600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:10.246774500+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-27T19:18:10.249394800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:18:10.258385900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:18:10.263500700+08:00" level=warning msg="[Provider] cordcloud-2 not updated for a long time, force refresh"
time="2025-06-27T19:18:10.266100100+08:00" level=warning msg="[Provider] cordcloud-1 not updated for a long time, force refresh"
time="2025-06-27T19:18:12.248552400+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-06-27T19:18:12.892622300+08:00" level=error msg="[Provider] cordcloud-2 pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": context deadline exceeded"
time="2025-06-27T19:18:12.895869800+08:00" level=error msg="[Provider] cordcloud-1 pull error: Get \"https://www.ccsub.org/link/0bq2vG3xxpKOWhUy?clash=1\": context deadline exceeded"
time="2025-06-27T19:18:13.875259600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3964666639373836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:13.875259600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.253853000+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.253853000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.253853000+08:00" level=error msg="PKM2 | 剩余流量：59.67 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.253853000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.258402200+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.258402200+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.258402200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.258402200+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.258402200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.258402200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:15.263920600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3361643165643062 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:15.263920600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:21.448003400+08:00" level=error msg="[Provider] cordcloud-2 pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": context canceled"
time="2025-06-27T19:18:21.448518000+08:00" level=error msg="[Provider] cordcloud-1 pull error: Get \"https://www.ccsub.org/link/0bq2vG3xxpKOWhUy?clash=1\": context canceled"
time="2025-06-27T19:18:21.448518000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3564326133656163 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-27T19:18:21.448518000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:21.448518000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6361346130376364 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-27T19:18:21.448518000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:26.189309900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6238626566353737 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:26.189309900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:30.263563400+08:00" level=error msg="[Provider] cordcloud-2 pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": context deadline exceeded"
time="2025-06-27T19:18:30.266598700+08:00" level=error msg="[Provider] cordcloud-1 pull error: Get \"https://www.ccsub.org/link/0bq2vG3xxpKOWhUy?clash=1\": context deadline exceeded"
time="2025-06-27T19:18:32.455674000+08:00" level=error msg="Free-6 | ❓_🇻🇳_💻_github.com/Ruk1ng001_6665626634313562 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:32.455674000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:34.074062500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3433376336326339 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:34.074062500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:38.530726300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-06-27T19:18:49.863391800+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:18:50.017023300+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:18:50.978206400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3435393638616162 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:50.978206400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:18:59.371011300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6235363030346435 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:18:59.371011300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:19:00.182665500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3365643635303963 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:19:00.182665500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:19:05.979576500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3831613962383836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:19:05.979576500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:19:20.005553900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6439396661616134 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:19:20.005553900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:19:44.381786200+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-06-27T19:19:44.381786200+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-06-27T19:19:44.381786200+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-06-27T19:19:44.381786200+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-06-27T19:19:49.611196500+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:19:49.762249900+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:20:15.591818100+08:00" level=error msg="PKM2 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:20:15.591818100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:20:15.591818100+08:00" level=error msg="PKM2 | 剩余流量：59.66 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:20:15.591818100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:20:15.591818100+08:00" level=error msg="PKM2 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:20:15.591818100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:21:29.109574400+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:21:29.223377700+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:22:58.667378200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:23:07.193356800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:23:15.249392500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3361643165643062 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:15.249392500+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:15.249392500+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:15.249392500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:23:15.249392500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:23:15.249392500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:23:15.250328800+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-27T19:23:55.223158300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3966646632313038 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:55.223158300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:23:57.477538700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161316366313835 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:57.477538700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:23:59.092928900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6166363330646563 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:23:59.092928900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:24:11.732612200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3137663032396662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:24:11.732612200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:24:13.287858200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3830643639653264 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:24:13.287858200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:24:15.310905900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3434663135633137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:24:15.310905900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:24:20.311148100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6133623963323330 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:24:20.311148100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:24:28.653844500+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:24:28.759940200+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:28:00.081143200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:28:10.121801100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:28:15.137227800+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:28:15.137227800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:28:15.137227800+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:28:15.137227800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:28:15.248779500+08:00" level=error msg="PKM2 | 剩余流量：59.66 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:28:15.248779500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:28:42.208063400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6538323937663637 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:28:42.208063400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:29:08.446957900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3434663135633137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:29:08.446957900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:29:14.803290200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6439396661616134 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:29:14.803290200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:29:48.361700600+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:29:48.425674300+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:33:01.295652800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:33:11.249085600+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-27T19:33:11.977430400+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-27T19:33:12.048424200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:33:20.254100600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6231636539633035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:33:20.254100600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:33:20.379058900+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
time="2025-06-27T19:33:35.333278300+08:00" level=error msg="Free-6 | ❓_🇻🇳_💻_github.com/Ruk1ng001_6665626634313562 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:33:35.333278300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:35:08.060377100+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:35:08.304936000+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:37:14.774343600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:57829(KOOK.exe) --> api.m.taobao.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-27T19:37:14.774343600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:57828(KOOK.exe) --> api.m.taobao.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-27T19:38:02.980516500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:38:11.249685000+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-27T19:38:11.820116700+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-27T19:38:19.823381100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:38:30.258172000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6231363230356238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:30.258172000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:38:32.579213600+08:00" level=error msg="Free-6 | 🟡_🇨🇦_💼_github.com/Ruk1ng001_3235323834376264 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:32.579213600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:38:48.997287200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638346235636366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:48.997287200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:38:50.064361000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3435393638616162 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:50.064361000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:38:55.041533600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3736636330636335 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:55.041533600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:38:55.705303100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161316366313835 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:38:55.705303100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:39:00.441266100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431346261336137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:39:00.441266100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:39:00.726919800+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:60710(clash-verge.exe) --> github.com:443 error: **************:5067 connect error: shcuac.aag436.ccddn4.icu:4700 connect error: context deadline exceeded"
time="2025-06-27T19:39:04.587136100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6638353637396533 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:39:04.587136100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:39:05.441440100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3831613962383836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:39:05.441440100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:39:09.177094000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3137663032396662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:39:09.177094000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:39:17.215355200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6133623963323330 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:39:17.215355200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:40:27.597579200+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:40:28.032390200+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:43:05.931369600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:43:11.248126400+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-27T19:43:15.248794600+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:15.248794600+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:15.248794600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:15.248794600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:15.248794600+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:15.248794600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:23.405437400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6238626566353737 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:23.405437400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:25.103989300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6663316566656434 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:25.103989300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:26.287475100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-27T19:43:29.575194500+08:00" level=error msg="CC1 | 广州移动转香港NTT5[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:29.575194500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:33.914383200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6239316163303966 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:33.914383200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:47.838780600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6331353039643137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:47.838780600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:52.012521000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6361346130376364 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-27T19:43:52.012521000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T19:43:55.867058000+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
time="2025-06-27T19:44:17.305368500+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
time="2025-06-27T19:44:58.926980600+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
time="2025-06-27T19:45:47.009811000+08:00" level=error msg="[Provider] cordcloud-1 pull error: 522 <none>"
time="2025-06-27T19:45:47.527373000+08:00" level=error msg="[Provider] cordcloud-2 pull error: 522 <none>"
time="2025-06-27T19:46:20.416730300+08:00" level=error msg="[Provider] free-1 pull error: 404 Not Found"
