# 基本设置
port: 7890                # HTTP代理端口
socks-port: 7891          # SOCKS代理端口
allow-lan: false           # 允许局域网连接
bind-address: "*"         # 绑定地址，* 表示所有地址
mode: rule                # 代理模式，rule表示规则模式
log-level: info           # 日志级别
ipv6: false
# 外部控制配置
# external-controller: 127.0.0.1:9090 # 允许通过 RESTful API 或 Web 界面（如 Clash Dashboard）在本地控制 Clash
# secret:    建议设置  #外部控制,api访问密钥，确保只有知道密码的人才能控制 Clash
# 性能优化
unified-delay: true       # 更换延迟计算方式，去除握手等额外延迟
tcp-concurrent: true      # 启用 TCP 并发连接。这允许 Clash 同时建立多个 TCP 连接，可以提高网络性能和连接速度

find-process-mode: strict  # 设置进程查找模式为严格模式，Clash 会更精确地识别和匹配网络流量来源的进程
global-client-fingerprint: chrome  # 设置全局客户端指纹为 Chrome，使 Clash 在建立连接时模拟 Chrome 浏览器的 TLS 指纹，增强隐私性和绕过某些网站的指纹检测


# 所有节点
all: &all {type: select, use: [订阅 1, 订阅 2, 订阅 3, 订阅 4, 订阅 8]}

# 订阅更新和延迟测试相关
p: &p {type: http, interval: 21600, health-check: {enable: true, url: "http://www.google.com/generate_204", interval: 1800, filter: "(?i)^(?!unavailable).*$"}}

proxy-providers:  # Meta支持机场通用订阅

  订阅 1: # 井号后面是注释，可修改
    <<: *p         #T02
    # url:     
    url: ""
  
  订阅 2: # 下面的订阅 3 是一个标准示例，非可用订阅
    <<: *p       #雷霆
    # url: 
    url: ""
  
  订阅 3: # 
    <<: *p
    # url:
    url: ""
 
  订阅 4: # 在你没有弄明白规则的写法之前，不要随意删减订阅 1 至订阅 8，哪怕 url 为空  自留地
    <<: *p         #T02
    # url:     
    url: ""

  订阅 5: # 
    <<: *p
    # url:
    url: ""
    
  订阅 6: # 
    <<: *p
    url:
    # url: ""
    
  订阅 7: # 
    <<: *p
    url:
    # url: ""
    
  订阅 8: # 
    <<: *p
    # url:
    url: ""



proxies:
# - name: webshare
#   type: socks5  类型,如果ss 就写 ss
#   server:  代理服务器域名/ip
#   port: 50010  端口
#   username:   用户名
#   password:   密码
#   tls: false
#   skip-cert-verify: true
#   udp: true
#   dialer-proxy: dialer-selector




# Profile Enhancement Merge Template for Clash Verge
proxy-groups:
  - name: 唯快不破
    include-all: true
    type: select
    proxies:
      - 自动选择
    icon: https://github.com/clash-verge-rev/clash-verge-rev/raw/main/src-tauri/icons/icon.png

  - name: dialer-selector
    include-all: true
    type: select
    proxies:
      - 自动选择
    icon: https://github.com/clash-verge-rev/clash-verge-rev/raw/main/src-tauri/icons/icon.png

  - name: 自动选择
    type: url-test
    include-all: true
    url: http://www.gstatic.com/generate_204
    interval: 300
    icon: https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg

  - name: AI
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Orz-3/mini/master/Color/OpenAI.png

  - name: 国外网站
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Global.png

  - name: 国际媒体
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/YouTube.png

  - name: 苹果服务
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Apple_1.png

  - name: 微软服务
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Microsoft.png

  - name: 谷歌服务
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Google_Search.png

  - name: 电报消息
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Telegram.png

  - name: 推特消息
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Twitter.png

  - name: 游戏平台
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Game.png

  - name: Emby
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Emby.png

  - name: 广告拦截
    include-all: true
    type: select
    proxies:
      - REJECT
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Advertising.png

  - name: 兜底分流
    include-all: true
    type: select
    proxies:
      - 唯快不破
      - Direct
      - 自动选择
    icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Final.png

  - name: Direct
    include-all: true
    type: select
    proxies:
      - DIRECT



rule-providers:
  # AI 类
  ai:
    type: http
    behavior: classical
    format: yaml
    path: ./rules/ai.yaml
    url: "https://fastly.jsdelivr.net/gh/MadisonWirtanen/WARP-Clash-with-ZJU-Rules@main/ai.yaml"
    interval: 86400
    
  Gemini: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Gemini.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Gemini.yaml"
    interval: 86400

  Claude: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Claude.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Claude.yaml"
    interval: 86400

# 广告类
  AdBlock:
    type: http
    behavior: classical
    format: yaml
    path: ./rules/AdBlock.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/AdBlock.yaml"
    interval: 86400
    
  BanAD:
    type: http
    behavior: domain
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list"
    path: ./ruleset/BanAD.yaml
    interval: 86400

  BanProgramAD:
    type: http
    behavior: domain
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list"
    path: ./ruleset/BanProgramAD.yaml
    interval: 86400

  BanEasyList:
    type: http
    behavior: domain
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanEasyList.list"
    path: ./ruleset/BanEasyList.yaml
    interval: 86400

  reject:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/reject.yaml
    interval: 86400

# 补丁类
  collection: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/collection.yaml
    url: "https://gist.githubusercontent.com/cnfree8964/0864fd1d2e88936a095fb40d74ce4993/raw/collection.yaml"

  gfw:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt"
    path: ./ruleset/gfw.yaml
    interval: 86400

  proxy:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt"
    path: ./ruleset/proxy.yaml
    interval: 86400

  tld-not-cn:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt"
    path: ./ruleset/tld-not-cn.yaml
    interval: 86400

  ProxyGFWlist:
    type: http
    behavior: domain
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyGFWlist.list"
    path: ./ruleset/BanEasyList.yaml
    interval: 86400

  ProxyClient: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/ProxyClient.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/ProxyClient.yaml"

  ChinaDomain:
    type: http
    behavior: classical
    format: yaml
    path: ./rules/ChinaDomain.yaml
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list"
    interval: 86400

  ChinaCompanyIp:
    type: http
    behavior: classical
    format: yaml
    path: ./rules/ChinaCompanyIp.yaml
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaCompanyIp.list"
    interval: 86400

  China: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/China.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/China.yaml"
    interval: 86400

  cncidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt"
    path: ./ruleset/cncidr.yaml
    interval: 86400

  lancidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt"
    path: ./ruleset/lancidr.yaml
    interval: 86400

# 服务类
  telegramcidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt"
    path: ./ruleset/telegramcidr.yaml
    interval: 86400

  PayPal: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/PayPal.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/PayPal.yaml"
    interval: 86400

  TikTok: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/TikTok.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/TikTok.yaml"
    interval: 86400

  IDM: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/IDM.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/IDM.yaml"

# 流媒体
  Amazon: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Amazon.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Amazon.yaml"
    interval: 86400

  Netflix: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Netflix.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Netflix.yaml"
    interval: 86400

  Spotify: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Spotify.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Spotify.yaml"
    interval: 86400
    
  DisneyPlus: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/DisneyPlus.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/DisneyPlus.yaml"
    
  Hulu: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Hulu.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Hulu.yaml"

  HBO: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/HBO.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/HBO.yaml"

# 游戏类
  Steam: 
    type: http
    behavior: classical
    format: yaml
    path: ./rules/Steam.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/Steam.yaml"
    interval: 86400
  AD:
    type: http
    behavior: classical
    url: https://adrules.top/adrules_domainset.txt
    path: ./rule-providers/AD.list

  Apple:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Apple.list
    path: ./rule-providers/Apple.list

  Google:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Google.list
    path: ./rule-providers/Google.list

  YouTube:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/YouTube.list
    path: ./rule-providers/YouTube.list

  Telegram:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Telegram.list
    path: ./rule-providers/Telegram.list

  Twitter:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Twitter.list
    path: ./rule-providers/Twitter.list

  Epic:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Epic.list
    path: ./rule-providers/Epic.list

  AI:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/AI.list
    path: ./rule-providers/AI.list

  Emby:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Emby.list
    path: ./rule-providers/Emby.list


  Bahamut:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Bahamut.list
    path: ./rule-providers/Bahamut.list


  Disney:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Disney.list
    path: ./rule-providers/Disney.list

  PrimeVideo:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/PrimeVideo.list
    path: ./rule-providers/PrimeVideo.list

  OneDrive:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/OneDrive.list
    path: ./rule-providers/OneDrive.list

  Github:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Github.list
    path: ./rule-providers/Github.list

  Microsoft:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Microsoft.list
    path: ./rule-providers/Microsoft.list

  Lan:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/Lan.list
    path: ./rule-providers/Lan.list

  ProxyGFW:
    type: http
    behavior: classical
    url: https://github.com/Repcz/Tool/raw/X/Clash/Rules/ProxyGFW.list
    path: ./rule-providers/ProxyGFW.list




# dns:
#   enable: true
#   ipv6: false
#   enhanced-mode: redir-host
#   listen: 0.0.0.0:1053
#   default-nameserver:
#   - tls://**********
#   - ************
#   nameserver:
#   - *******
#   - *******
#   - tls://*******
#   - tls://*******
#   - https://cloudflare-dns.com/dns-query
#   - https://dns.google/dns-query
#   nameserver-policy:
#     geosite:cn: https://doh.pub/dns-query

# dns:
#   enable: true
#   ipv6: false
#   default-nameserver:
#     - *********
#     - *********
#   nameserver:
#     - *******
#     - *******
#   fallback:
#     - tls://*******:853
#     - tls://*******:853
#   fallback-filter:
#     geoip: true
#     geoip-code: CN
#     ipcidr:
#       - 240.0.0.0/4
#   enhanced-mode: fake-ip
#   fake-ip-range: **********/16
#   fake-ip-filter:
#     - '*.lan'
#     - 'localhost.ptlogin2.qq.com'
#     - 'dns.msftncsi.com'
#     - 'www.msftncsi.com'
#     - 'www.msftconnecttest.com'


# DNS配置
dns:
  enable: true            # 启用 Clash 的 DNS 功能
  ipv6: false              # IPv6 DNS 解析
  listen: '0.0.0.0:53'    # DNS 服务器监听地址和端口，:53 表示监听所有接口的 53 端口
  enhanced-mode: fake-ip  # 设置增强模式为 fake-ip 模式，提高解析速度和连接性能
  fake-ip-range: **********/16  # 设置 fake-ip 的 IP 地址范围为 **********/16
  use-hosts: true         # 启用 hosts 文件解析，Clash 会检查系统的 hosts 文件
  use-system-hosts: true  # 使用系统的 hosts 文件进行域名解析
  prefer-h3: true         # 如果DNS服务器支持DoH3会优先使用h3，提升性能
  respect-rules: true     # 让 DNS 解析遵循 Clash 的路由规则
  # 定义不使用虚假IP解析的域名列表
  fake-ip-filter:
    - '*.lan'
    - localhost.ptlogin2.qq.com
    - '+.srv.nintendo.net'
    - '+.stun.playstation.net'
    - '+.msftconnecttest.com'
    - '+.msftncsi.com'
    - '+.xboxlive.com'
    - 'xbox.*.microsoft.com'
    - '*.battlenet.com.cn'
    - '*.battlenet.com'
    - '*.blzstatic.cn'
    - '*.battle.net'
  

  # 建议多写点 DNS （ clash 的机制是同时向所有 DNS 发起解析请求，用返回最快那个，后续的直接丢弃）
  # 用来解析 nameserver 和 fallback 里面的域名的，必须为 IP, 可为加密 DNS
  default-nameserver:
    - *******           # Google DNS
    - *******           # Cloudflare DNS
    - ************      # 腾讯DNSPod
    - *********         # 阿里DNS
    - *********         # 阿里DNS
    - ************      # 百度DNS

  # 用于direct出口域名解析的 DNS 服务器
  # 如果不填则遵循nameserver-policy、nameserver和fallback的配置
  direct-nameserver:
    - system
    - https://doh.pub/dns-query          #腾讯
    - https://dns.alidns.com/dns-query   #阿里
    - ***************                    # 114
    - ***************                    # 114
    - ***********                        # 百度-电信
    - ************                       # 百度-联通
    - ************                       # 百度-联通
    - *************                      # OneDNS-南方
    - **************                     # OneDNS-北方
  nameserver: 
    - https://*******/dns-query#🚀 节点选择
    - https://*******/dns-query#🚀 节点选择
  # 解析代理服务器域名的DNS服务器,就是节点本身中包含的域名
  # 代理节点域名解析服务器，仅用于解析代理节点的域名，如果不填则遵循nameserver-policy、nameserver和fallback的配置
  proxy-server-nameserver:
    - https://*******/dns-query          # Cloudflare DNS
    - https://*******/dns-query          # Google DNS
    - https://doh.pub/dns-query          #腾讯
    - https://dns.alidns.com/dns-query   #阿里
    - https://*******/dns-query          #Quad9
    - https://**************/dns-query   #OpenDNS

# 配置文件管理
profile:
  store-selected: true    # 保存用户选择的代理配置，在 Clash 重启后仍然记住上次选择的代理服务器
  store-fake-ip: true     # 适用于 Clash 在 Fake IP 模式下工作时。它会将域名到 Fake IP 的映射关系保存下来，确保 Clash 重启后依然能使用相同的 Fake IP 分配给相同的域名，保持连接的一致性和稳定性

# 域名嗅探
sniffer:
  enable: true            # 启用网络流量嗅探功能，允许 Clash 分析和识别流量类型
  sniff:
    TLS:
      ports: [443, 8443]  # 要嗅探的 TLS 流量端口，包括标准 HTTPS 端口 443 和常用的备用 HTTPS 端口 8443
    HTTP:
      ports: [80, 8080-8880]  # 要嗅探的 HTTP 流量端口，包括标准 HTTP 端口 80 和常用的 HTTP 端口范围 8080-8880
      override-destination: true  # 启用目标覆盖功能，允许 Clash 根据嗅探到的域名重写请求的目标地址

# GeoData 配置
geodata-mode: true        # 启用地理数据模式，使 Clash 使用 GeoIP/GeoSite 数据库进行流量的地理位置识别和规则匹配
geo-auto-update: true     # 启用地理数据库自动更新功能，Clash 会定期检查并更新 GeoIP/GeoSite 数据库
geo-update-interval: 24   # 设置地理数据库的更新间隔为24小时，Clash 会每24小时自动检查一次更新（小时）
geodata-loader: standard  # 设置地理数据加载器为标准模式，使用标准的数据解析和加载方式处理地理数据库
# GeoData下载源（使用国内加速下载源）
# 备用下载域名https://ghfast.top、https://ghproxy.net、https://ghgo.xyz
geox-url:
  geoip: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat
  geosite: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
  mmdb: https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb
  asn: https://fastgh.lainbo.com/https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb

tun:
  enable: true
  stack: mixed
  auto-route: true
  auto-redirect: true
  auto-detect-interface: true
  dns-hijack:
    - any:53
    - tcp://any:53
  device: utun0
  mtu: 1500
  strict-route: true
  gso: true
  gso-max-size: 65536
  udp-timeout: 300
  iproute2-table-index: 2022
  iproute2-rule-index: 9000
  endpoint-independent-nat: false
  route-address-set:
    - ruleset-1
  route-exclude-address-set:
    - ruleset-2
  route-address:
    - 0.0.0.0/1
    - *********/1
    - "::/1"
    - "8000::/1"
  route-exclude-address:
    - ***********/16
    - fc00::/7
  include-interface:
    - eth0
  exclude-interface:
    - eth1
  include-uid:
    - 0
  include-uid-range:
    - 1000:9999
  exclude-uid:
    - 1000
  exclude-uid-range:
    - 1000:9999
  include-android-user:
    - 0
    - 10
  include-package:
    - com.android.chrome
  exclude-package:
    - com.android.captiveportallogin

rules:

#自定义规则
  - DOMAIN-KEYWORD,youtube,国际媒体
  - DOMAIN-SUFFIX,googlevideo.com,国际媒体
  - DOMAIN-KEYWORD,google,AI
  - DOMAIN-KEYWORD,claude,AI
  - DOMAIN,api-proxy.me,AI
  - DOMAIN,grok.com,AI  
  - DOMAIN-KEYWORD,ai,AI
  - DOMAIN,api.theoremhub.asia,DIRECT
  - DOMAIN-KEYWORD,cloudflare.com,唯快不破
  - DOMAIN-SUFFIX,nvidia.com,AI
  - DOMAIN-SUFFIX,cloudflare.com,DIRECT
  - DOMAIN-SUFFIX,immersive-translate.owenyoung.com,REJECT
  - DOMAIN-SUFFIX,immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,test-api2.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,api2.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,config.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,app.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,dash.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,api.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,immersive-translate.deno.dev,REJECT
  - DOMAIN-SUFFIX,ai.immersivetranslate.com,REJECT
  - DOMAIN-SUFFIX,test-ai.immersivetranslate.com,REJECT
  - DOMAIN-KEYWORD,immersivetranslate,REJECT
  - DOMAIN-KEYWORD,immersive-translate,REJECT

  # 解锁AI
  - RULE-SET,ai,AI
  - RULE-SET,Gemini,AI
  - RULE-SET,Claude,AI
  - RULE-SET,Google,谷歌服务
  - DOMAIN,meta.ai,AI
  - DOMAIN,claude.ai,AI
  - DOMAIN,groq.com,AI
  - DOMAIN-SUFFIX,console.anthropic.com,AI
  - DOMAIN,anthropic.com,AI
  - DOMAIN,claude.ai,AI
  - DOMAIN,console.anthropic.com,AI
  - DOMAIN,anthropic.com,AI
  - DOMAIN-SUFFIX,aistudio.google.com,AI
  - DOMAIN,browser-intake-datadoghq.com,AI
  - DOMAIN,static.cloudflareinsights.com,AI
  - DOMAIN,ai-pro.org,AI
  - DOMAIN-SUFFIX,ai.com,AI
  - DOMAIN-SUFFIX,algolia.net,AI
  - DOMAIN-SUFFIX,api.statsig.com,AI
  - DOMAIN-SUFFIX,auth0.com,AI
  - DOMAIN-SUFFIX,chatgpt.com,AI
  - DOMAIN-SUFFIX,chatgpt.livekit.cloud,AI
  - DOMAIN-SUFFIX,client-api.arkoselabs.com,AI
  - DOMAIN-SUFFIX,events.statsigapi.net,AI
  - DOMAIN-SUFFIX,featuregates.org,AI
  - DOMAIN-SUFFIX,host.livekit.cloud,AI
  - DOMAIN-SUFFIX,identrust.com,AI
  - DOMAIN-SUFFIX,intercom.io,AI
  - DOMAIN-SUFFIX,intercomcdn.com,AI
  - DOMAIN-SUFFIX,launchdarkly.com,AI
  - DOMAIN-SUFFIX,oaistatic.com,AI
  - DOMAIN-SUFFIX,oaiusercontent.com,AI
  - DOMAIN-SUFFIX,observeit.net,AI
  - DOMAIN-SUFFIX,poe.com,AI
  - DOMAIN-SUFFIX,segment.io,AI
  - DOMAIN-SUFFIX,sentry.io,AI
  - DOMAIN-SUFFIX,stripe.com,AI
  - DOMAIN-SUFFIX,turn.livekit.cloud,AI
  - DOMAIN-KEYWORD,openai,AI

  - DOMAIN-SUFFIX,googleapis.com,AI # Google chat 服务
  - DOMAIN-SUFFIX,gstatic.com,AI # Google静态资源
  - DOMAIN-SUFFIX,xn--ngstr-lra8j.com,AI # Google Play下载服务
  - DOMAIN-SUFFIX,github.io,AI #Github Pages
  - DOMAIN-SUFFIX,dadada.acaisbest.com,唯快不破
  - DOMAIN-SUFFIX,isroots.com,AI
  - DOMAIN-KEYWORD,marscode,AI
  - DOMAIN-SUFFIX,appmiu.com,Direct
  - DOMAIN,v2rayse.com,唯快不破 # V2rayse节点工具
  - DOMAIN-KEYWORD,cocopilot.org,唯快不破
  - DOMAIN-KEYWORD,n256,Direct
  - DOMAIN-SUFFIX,quark.cn,Direct
  - DOMAIN-KEYWORD,alipan,Direct
  - DOMAIN-KEYWORD,learning.google.com,AI
  - RULE-SET,AD,广告拦截
  - RULE-SET,AI,AI
  - RULE-SET,Apple,苹果服务
  - RULE-SET,Telegram,电报消息
  - RULE-SET,Twitter,推特消息
  - RULE-SET,Steam,游戏平台
  - RULE-SET,Epic,游戏平台
  - RULE-SET,Emby,Emby
  - RULE-SET,Spotify,国际媒体
  - RULE-SET,Bahamut,国际媒体
  - RULE-SET,Netflix,国际媒体
  - RULE-SET,Disney,国际媒体
  - RULE-SET,PrimeVideo,国际媒体
  - RULE-SET,HBO,国际媒体
  - GEOSITE,gfw,国外网站
  - GEOSITE,onedrive,微软服务
  - GEOSITE,github,微软服务
  - GEOSITE,microsoft,微软服务
  - RULE-SET,China,DIRECT
  - GEOIP,lan,DIRECT
  - GEOIP,CN,DIRECT

  


# 服务类
  - RULE-SET,YouTube,国际媒体
  - RULE-SET,TikTok,国际媒体
  - RULE-SET,PayPal,唯快不破
  - RULE-SET,telegramcidr,唯快不破
  - DOMAIN-KEYWORD,oaifree,DIRECT
  - DOMAIN-SUFFIX,shared.oaifree.com,DIRECT
  

# 屏蔽广告

  - RULE-SET,AdBlock,广告拦截
  - RULE-SET,reject,广告拦截
  - RULE-SET,BanAD,广告拦截
  - RULE-SET,BanProgramAD,广告拦截
  - RULE-SET,BanEasyList,广告拦截
  
# 补丁规则
  - RULE-SET,gfw,唯快不破
  - RULE-SET,proxy,唯快不破
  - RULE-SET,tld-not-cn,唯快不破
  - RULE-SET,ProxyGFWlist,唯快不破
  - RULE-SET,ProxyClient,唯快不破
  - RULE-SET,ChinaDomain,DIRECT,no-resolve
  - RULE-SET,ChinaCompanyIp,DIRECT,no-resolve
  - RULE-SET,cncidr,DIRECT,no-resolve
  - RULE-SET,China,DIRECT,no-resolve
  - RULE-SET,lancidr,DIRECT,no-resolve
  - RULE-SET,collection,DIRECT,no-resolve

# 默认规则 
  - GEOIP,CN,DIRECT,no-resolve
  - GEOSITE,geolocation-cn,DIRECT
  - GEOSITE,geolocation-!cn,唯快不破
  - MATCH,兜底分流
