Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-25T20:08:27.584882500+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-25T20:08:27.591038000+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-06-25T20:08:27.591038000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-25T20:08:27.591545400+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-25T20:08:27.595653600+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-06-25T20:08:27.595653600+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-25T20:08:27.806634300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119081"
time="2025-06-25T20:08:27.810698700+08:00" level=info msg="Initial configuration complete, total time: 222ms"
time="2025-06-25T20:08:27.812796300+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-06-25T20:08:27.812796300+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-06-25T20:08:27.812796300+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-25T20:08:27.823519600+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-06-25T20:08:28.291526800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:28.291526800+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-06-25T20:08:28.297611300+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-06-25T20:08:28.297611300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:28.309608600+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-06-25T20:08:28.388074800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-06-25T20:08:29.865702600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:30.772079900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:33.310101100+08:00" level=error msg="PKM2 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:33.310101100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:33.310611700+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-25T20:08:35.050820900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:35.880163400+08:00" level=error msg="[GEO] update GEO database error: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-06-25T20:08:35.880163400+08:00" level=error msg="[GEO] Failed to update GEO database: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-06-25T20:08:37.364683800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:08:39.646553700+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:64386 --> cdn.jsdelivr.net:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:08:44.799669600+08:00" level=error msg="CC2 | 上海联通转美国Cera4[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:44.799669600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:47.259023000+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-25T20:08:47.259023000+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-25T20:08:47.259023000+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-25T20:08:47.259023000+08:00" level=warning msg="because PKM2 宝可梦-2 failed multiple times, active health check"
time="2025-06-25T20:09:04.458313100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3966646632313038 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:09:04.458313100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:09:04.644605200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262663137346362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:09:04.644605200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:09:07.735609800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3332313737376635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:63702->**************:20013: use of closed network connection"
time="2025-06-25T20:09:07.735609800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:09:19.271133500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6133376536353861 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:09:19.271133500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:31.069347800+08:00" level=error msg="CC1 | 江苏联通转香港HKC2[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:31.069347800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:33.288183200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:33.288183200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:33.817278700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3138643161363433 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:33.817278700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:36.546413800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:13:40.201677500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:13:57.159954100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:57.159954100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:58.909699600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3966646632313038 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:58.909699600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:13:59.395522900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262663137346362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:13:59.395834400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:14:04.384617400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3631386238633566 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:14:04.384617400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:14:13.207165800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3562653738653833 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:14:13.207165800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:18:29.283828000+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T20:18:33.287722800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6532646535653936 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:18:33.287722800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:18:36.578274200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6231363230356238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:18:36.578274200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:18:38.005332600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:18:42.389369700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:18:54.399962000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3163643537616465 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:18:54.399962000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:18:59.491964400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262663137346362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:18:59.491964400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:19:07.171660200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6132313239366539 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:19:07.171660200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:19:15.927372500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3436313361393735 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:19:15.927372500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:19:30.768871300+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T20:19:36.415060600+08:00" level=error msg="PKM2 | 🇺🇸【北美洲】美国01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:19:36.415060600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:23:04.933326500+08:00" level=error msg="CC2 | 安徽联通转美国DP4[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:23:04.933326500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:23:12.406124200+08:00" level=error msg="CC2 | 江苏联通转日本BGP5[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:23:12.406124200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:23:30.955889600+08:00" level=error msg="CC1 | 安徽联通转美国Cera3[倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:23:30.955889600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:23:31.682511400+08:00" level=error msg="CC1 | 安徽联通转美国TE[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:23:31.682511400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:23:39.421870400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:23:46.331471300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:23:53.199203900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6636643065643461 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:23:53.199203900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:28:09.096685800+08:00" level=error msg="CC2 | 广东移动转日本NTT6[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:28:09.096685800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:28:29.283715300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-06-25T20:28:33.289964900+08:00" level=error msg="PKM1 | 剩余流量：194.08 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:28:33.289964900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:28:41.906010400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:28:50.471598500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6638353637396533 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:28:50.471598500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:28:51.318811400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638346235636366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:28:51.318811400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:28:53.602513200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:28:54.063062300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:28:54.063062300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:29:04.580361200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3666303430363133 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:29:04.580361200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:33.289664600+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:33.289664600+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:33.289664600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:33.289664600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:33.290678000+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:33.290678000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:35.913225000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6363333431306462 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:35.913225000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:41.914068800+08:00" level=error msg="CC1 | 江苏联通转美国Cera2[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:41.914068800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:33:43.336757000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:33:51.868552800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3163643537616465 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:33:51.868552800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:34:00.063925700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3332313737376635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": write tcp *************:64624->**************:20013: use of closed network connection"
time="2025-06-25T20:34:00.063925700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:34:11.226854900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:38:16.664810100+08:00" level=error msg="CC2 | 江苏联通转德国HZ2[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:16.664810100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:33.288710700+08:00" level=error msg="PKM2 | 套餐到期：2025-07-25 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:33.288710700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:33.288710700+08:00" level=error msg="PKM2 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:33.288710700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:33.289212200+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:33.289212200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:33.291712700+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:33.291712700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:33.292643500+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:33.293145700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:35.455611600+08:00" level=error msg="CT1 | 🇲🇾 马来西亚-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:35.455611600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:50.502158600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3736373261393638 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:50.502158600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:38:52.085662700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:38:58.112642500+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3736613264636265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:38:58.112642500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:39:07.543122700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6132313239366539 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:39:07.543122700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:39:17.704911800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:43:33.287672900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:33.287672900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:34.438149800+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:34.438149800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:35.068347100+08:00" level=error msg="CT2 | 🇲🇾 马来西亚-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:35.068347100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:37.144068000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6431666634386430 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:37.144068000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:40.142974300+08:00" level=error msg="Free-6 | 🟡_🇨🇳_📶_github.com/Ruk1ng001_3937386236653034 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:40.142974300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:52.622923800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6638353637396533 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:52.622923800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:53.561297300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:43:56.483377700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:56.483377700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:43:58.752253400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3132346430383734 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:43:58.752253400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:44:03.848943800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3332313737376635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": write tcp *************:55240->**************:20013: use of closed network connection"
time="2025-06-25T20:44:03.848943800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:44:06.354665000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6362303862313432 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:44:06.354665000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:44:10.309136400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3536626135366330 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:44:10.309136400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:44:19.811016800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:48:33.288248700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:48:33.288789000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:48:34.416746100+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:48:34.416746100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:48:44.926027100+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3430386239626665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:48:44.926027100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:48:47.880497700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3966393064623439 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:48:47.880497700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:48:55.690568100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:49:06.513096700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6161353163613063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:49:06.513096700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:49:07.744078100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6536326533623033 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:49:07.744078100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:49:10.044322800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6532633162646265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:49:10.044322800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:49:10.174426800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3436313361393735 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:49:10.174426800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:49:10.581659700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6133376536353861 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:49:10.581659700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:49:32.903371200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:53:40.787469200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3139303032643065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:53:40.787469200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:53:50.443746600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6436396464316462 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:53:50.443746600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:53:58.294909300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:54:33.708679400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/lancidr) **********:63130(WeChat.exe) --> **************:12114 error: dial tcp **************:12114: i/o timeout"
time="2025-06-25T20:54:38.568869300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:54:39.263512800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/lancidr) **********:63143(WeChat.exe) --> **************:12114 error: dial tcp **************:12114: i/o timeout"
time="2025-06-25T20:54:44.316086400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/lancidr) **********:63168(WeChat.exe) --> **************:12114 error: dial tcp **************:12114: i/o timeout"
time="2025-06-25T20:58:33.288485300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:33.288485300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:40.331709900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3364353138656364 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:40.331709900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:45.294554400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3564623464653764 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:45.294554400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:45.394133600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6331353039643137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:45.394133600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:49.902090900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6634393130623334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:49.902090900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:51.004021200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638346235636366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:51.004021200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:55.411152600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:58:55.411152600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:58:59.604960200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:59:05.361432000+08:00" level=error msg="Free-6 | 🟡_🇨🇳_📶_github.com/Ruk1ng001_3939636432666235 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:59:05.361432000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:59:44.469238800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:03:36.555345000+08:00" level=error msg="CC1 | 上海联通转美国Cera5[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:03:36.555345000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:03:39.889987100+08:00" level=error msg="CC1 | 安徽联通转美国Cera4[倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:03:39.889987100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:03:58.407489600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3966646632313038 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:03:58.407489600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:04:01.333522400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:04:49.716913200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:08:29.283870500+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:08:33.287973000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:08:33.287973000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:08:40.315309400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3364353138656364 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:08:40.315309400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:08:46.037192800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3361323934376435 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:08:46.037192800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:08:55.674076300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3334356634346233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:08:55.674076300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:08:56.720531500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3536663336653435 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": write tcp *************:56652->*************:30805: use of closed network connection"
time="2025-06-25T21:08:56.720531500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:08:58.358225800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:08:58.358225800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:09:02.747139700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:09:11.146287900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3737363230616438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:09:11.146287900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:09:30.040884400+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:10:09.728314300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": context deadline exceeded"
time="2025-06-25T21:10:14.856579300+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:10:36.103506800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:10:54.857296100+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:11:54.857953100+08:00" level=error msg="[Provider] free-1 pull error: context deadline exceeded"
time="2025-06-25T21:13:23.995827500+08:00" level=error msg="CC2 | 上海联通转美国Cera[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:23.995827500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:13:33.288259100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6532646535653936 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:33.288259100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:13:43.452371800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3364353138656364 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:43.452371800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:13:46.424469800+08:00" level=error msg="CC1 | 广州移动转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:46.424469800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:13:51.778469600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262633532663362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:51.778469600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:13:53.079514300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3635633731653565 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:13:53.079514300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:14:03.207391900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6433613932316138 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:14:03.207391900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:14:03.975863300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:15:38.798853300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-06-25T21:16:04.126547000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:18:35.206089500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6231363230356238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:18:35.206089500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:18:45.947551200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3564623464653764 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:18:45.947551200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:18:50.071015700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6565333165663961 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:18:50.071015700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:19:11.067977700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3737363230616438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:19:11.067977700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:19:23.987315600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-06-25T21:19:47.544486400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:21:10.714977200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:23:29.283543300+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:23:30.413132800+08:00" level=error msg="CC2 | 上海联通转美国Cera[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:30.413132800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:33.289008000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6532646535653936 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:33.289008000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:36.680607200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6231363230356238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:36.680607200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:40.998495000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3139303032643065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:40.998495000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:49.261796300+08:00" level=error msg="CC1 | 江苏联通转美国Cera2[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:49.261796300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:54.911055300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3736373261393638 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:54.911055300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:23:56.770435200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638346235636366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:23:56.770435200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:24:35.001194500+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:24:37.922582400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-06-25T21:24:43.444371000+08:00" level=error msg="CC2 | 上海联通转美国Cera4[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:24:43.444371000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:25:07.720367600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-06-25T21:25:30.354291500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:26:12.422282300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:28:29.286016600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-06-25T21:28:33.288036700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:28:33.288036700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:28:36.160657700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.170736900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.184875400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.197645800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.223745500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.273228500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.333049300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:36.676985200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.624354000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.635130400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.654807400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.677269900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.685488900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.700749000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.713014000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:60870(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.814495600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.878505400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.888913100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.903796700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:37.939608700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.017630200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.046265200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.056738100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.072340300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.096424700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.105215900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.150407200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.154303600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.171689800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.227306800+08:00" level=error msg="CC1 | 上海联通转美国Cera5[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:28:38.227306800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:28:38.290539500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.407387800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.607318700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.611905100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.626800600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.641652700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.741387700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:38.862450700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:39.566915700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61279(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:39.612007800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61209(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:39.862650700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61333(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.712193300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.722206400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.734007300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.744389700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.766631000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:40.892822300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:41.161544600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:41.414014400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:41.598296900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:42.599194400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:61840(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.603506900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.613767900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.629348700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.658852900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.672299100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:43.806959700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:44.000050500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:44.562002900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:44.635048400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:45.635241600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/aizex) **********:62188(msedge.exe) --> panter-c.aizex.cn:443 error: dial tcp ***************:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-06-25T21:28:51.697800300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3163643537616465 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:28:51.697800300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:29:00.376563500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6239316163303966 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:29:00.376563500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:30:37.562035500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:31:19.192601000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:32:22.037393600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:64820(SGDownload.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-25T21:32:22.042232400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:64825(SGTool.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-25T21:33:29.289338600+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:33:29.290346700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-06-25T21:33:33.290596900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:33:33.290596900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:33:39.426412300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3139303032643065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:33:39.426412300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:33:45.501048200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3564623464653764 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:33:45.501048200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:33:46.970524700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6436396464316462 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:33:46.970524700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:33:48.587668300+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6166613631656565 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:33:48.587668300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:34:09.401755000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3138303566326163 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:34:09.401755000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:34:14.408846700+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:34:30.756464500+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:34:36.853323200+08:00" level=error msg="CT1 | 🇲🇾 马来西亚-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:34:36.853323200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:34:54.409110200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:35:39.706783200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:36:25.779026400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:38:33.296434800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6439323134616531 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:33.296434800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:33.296434800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:33.296434800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:39.263303100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6532653363323735 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-06-25T21:38:39.263303100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:41.198181400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3139303032643065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:41.198181400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:44.264278000+08:00" level=error msg="Free-6 | ❓_🇺🇸_💻_github.com/Ruk1ng001_3664616434633365 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:44.264278000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:46.447626100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3462623835623738 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:46.447626100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:47.669289500+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6462333232623238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:47.669289500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:52.284164300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262633532663362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:52.284164300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:54.386434500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3635633731653565 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:54.386434500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:57.284509300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3433376336326339 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:57.284509300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:38:59.983601500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3137663032396662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:38:59.983601500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:39:03.251485400+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262663137346362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:39:03.251485400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:39:10.985150400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3631386238633566 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:39:10.985150400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:40:42.568121000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:41:04.609711600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:41:05.933686900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-06-25T21:41:28.972454900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:41:44.610559800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:42:01.373221700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": context deadline exceeded"
time="2025-06-25T21:42:44.610915900+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-06-25T21:43:29.283689800+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:43:33.288407800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:43:33.288407800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:43:52.367464900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3736373261393638 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:43:52.367464900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:44:03.219527700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3332313737376635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59773->**************:20013: use of closed network connection"
time="2025-06-25T21:44:03.219527700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:44:11.951030200+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6161353163613063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:44:11.951030200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:44:11.966326000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3737363230616438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:44:11.966326000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:44:31.315404700+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:44:41.315950400+08:00" level=error msg="CC2 | 上海联通转美国Cera2[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:44:41.315950400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:44:43.145614300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-06-25T21:44:52.617113800+08:00" level=error msg="CC1 | 江苏联通转美国Cera2[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:44:52.617113800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:45:51.629340700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:46:36.143215500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:48:29.283651200+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:48:33.288092400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:33.288092400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:33.290278100+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:33.290278100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:36.493578300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3635346632373133 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:36.493578300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:47.497848900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3462623338363362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:47.497848900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:48.343314000+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6166613631656565 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:48.343314000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:52.768029500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3433376336326339 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:52.768029500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:48:58.420934000+08:00" level=error msg="Free-6 | 🟡_🇨🇳_📶_github.com/Ruk1ng001_6138366238623337 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:48:58.420934000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:49:13.054962700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6363303062373335 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:49:13.054962700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:49:30.286836300+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:49:44.056628100+08:00" level=error msg="CC2 | 安徽联通转美国Cera3[倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:49:44.056628100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:50:56.742025600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:51:43.729159600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:53:29.291742900+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:53:36.194212000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6231363230356238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:53:36.194212000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:53:38.844239700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3635346632373133 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:53:38.844239700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:53:49.728662300+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3430386239626665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:53:49.728662300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:53:50.742477700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3564623464653764 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:53:50.742477700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:53:52.396515500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6331353039643137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:53:52.397022600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:05.830106600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3262663137346362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:05.830106600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:09.026667300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3564363435363464 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:09.026667300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:15.611229700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6132313239366539 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:15.611229700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:41.237612300+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-06-25T21:54:46.238744400+08:00" level=error msg="Free-1 | 🇨🇳 台湾02|BGP|EIP failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:46.238744400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:50.473098600+08:00" level=error msg="CC1 | 上海联通转美国Cera[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:50.473098600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:54:50.880300200+08:00" level=error msg="CC2 | 上海联通转美国Cera4[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:54:50.880300200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:55:04.073360800+08:00" level=error msg="CC1 | 江苏联通转美国Cera[倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:55:04.073360800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:55:13.139424400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65025 connect error: dial tcp ***************:65025: operation was canceled"
time="2025-06-25T21:56:05.089907600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:56:52.324873400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T21:58:29.283606600+08:00" level=warning msg="because CC1 CordCloud-1 failed multiple times, active health check"
time="2025-06-25T21:58:29.747390700+08:00" level=warning msg="because PKM1 宝可梦-1 failed multiple times, active health check"
time="2025-06-25T21:58:33.287838100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6532646535653936 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:33.287838100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:33.287838100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:33.287838100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:35.393599600+08:00" level=error msg="CT2 | 🇲🇾 马来西亚-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:35.393599600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:35.484400200+08:00" level=error msg="CT2 | 🇲🇾 马来西亚-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:35.484400200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:36.025514200+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6363653962623532 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:36.025514200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:40.883987600+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3139303032643065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:40.883987600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:45.856085200+08:00" level=error msg="CC1 | 广州移动转香港NTT3[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:45.856085200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:52.054120600+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6166613631656565 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:52.054120600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T21:58:59.390247000+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_32323462396161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T21:58:59.390247000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
