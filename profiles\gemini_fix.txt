# Gemini 代理组修复说明

由于编码问题，Gemini 代理组的节点顺序需要手动调整。

## 当前问题
第1119-1120行有重复的落地节点和编码问题的emoji。

## 需要手动修改的内容
在 Script.js 的第1117-1126行，将 Gemini 代理组的 proxies 数组修改为：

```javascript
"proxies": generateProxyListWithLanding([
  "🎯 落地节点",                   // 首选：美国落地节点，纯净度高
  "🇺🇸 美国",                     // 次选：其他美国节点
  "🇸🇬 新加坡",                   // 备选：亚太地区优选
  "🏠 家宽",                       // 备选：稳定连接
  "🌍 欧洲",                       // 备选：欧洲节点
  "🇭🇰 香港",                     // 备选：香港节点
  "⚙️ 自动选择",                   // 备选：自动选择
  "🔗 直连"                        // 最后：直连
], providerNames),
```

## 修改原因
将落地节点调整为首选，因为：
1. 落地节点服务器在美国
2. 纯净度高，适合Google服务
3. 稳定性好，延迟合理
