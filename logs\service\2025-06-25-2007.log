Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-25T20:07:45.700010900+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-25T20:07:45.705651400+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-06-25T20:07:45.705651400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-25T20:07:45.706161800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-25T20:07:45.709754600+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-06-25T20:07:45.710261400+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-25T20:07:45.913751400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119081"
time="2025-06-25T20:07:45.928527400+08:00" level=info msg="Initial configuration complete, total time: 225ms"
time="2025-06-25T20:07:45.930679600+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-06-25T20:07:45.930679600+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-06-25T20:07:45.930679600+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-25T20:07:45.949858700+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-06-25T20:07:46.459726600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:07:46.465443000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-25T20:07:46.476404900+08:00" level=warning msg="[Provider] cordcloud-1 not updated for a long time, force refresh"
time="2025-06-25T20:07:46.503456400+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-06-25T20:07:46.513682700+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-06-25T20:07:48.456318900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-25T20:07:51.474263100+08:00" level=error msg="Free-6 | 🟡_🇨🇳_📶_github.com/Ruk1ng001_3130633934323433 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.474263100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转台湾BGP[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转日本NTT7[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转美国GS[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转美国GS3[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转美国GS2[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转美国GS4[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转日本NTT[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转日本NTT3[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转日本NTT2[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=error msg="CC2 | 上海电信转日本NTT4[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476148600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转日本NTT2[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转美国GS3[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转日本NTT7[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转美国GS[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转美国GS4[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转日本NTT3[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转美国GS2[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=error msg="CC1 | 上海电信转台湾BGP[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:51.476683700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:53.579022100+08:00" level=error msg="[Sniffer] [***************] [tls] may not have any sent data, Consider adding skip"
time="2025-06-25T20:07:53.917992200+08:00" level=error msg="[GEO] update GEO database error: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-06-25T20:07:53.917992200+08:00" level=error msg="[GEO] Failed to update GEO database: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-06-25T20:07:54.189424500+08:00" level=error msg="CT2 | 🇸🇬  专线新加坡-3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:54.189424500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:54.213141600+08:00" level=error msg="CT1 | 🇸🇬  专线新加坡-3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:54.213141600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:54.331909700+08:00" level=error msg="CT1 | 🇸🇬  专线新加坡-4 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:54.331909700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:55.821216800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3163313439343237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:55.821216800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:56.459436500+08:00" level=error msg="CT2 | 🇭🇰 专线香港-3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:56.459436500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:56.476392800+08:00" level=error msg="CC2 | 上海移动转台湾BGP[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:56.476392800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:56.476908300+08:00" level=error msg="CC1 | 上海移动转台湾BGP[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:56.476908300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:57.361213100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6363333431306462 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:57.361213100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:07:57.671224700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:51636(svchost.exe) --> g.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:07:58.331462800+08:00" level=warning msg="[UDP] dial 📢 Google (match RuleSet/google-rules) **********:50549 --> clients2.google.com:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:07:58.630037400+08:00" level=warning msg="[UDP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:137(:System) --> fe3cr.delivery.mp.microsoft.com:137 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:07:59.539102100+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:51856 --> cdn.jsdelivr.net:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:07:59.714943900+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:50303 --> fastly.jsdelivr.net:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:07:59.937320400+08:00" level=error msg="CC1 | 上海移动转日本NTT[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:07:59.937320400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.175900200+08:00" level=error msg="CC2 | 上海移动转日本NTT[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.175900200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.177416100+08:00" level=error msg="CC1 | 上海移动转日本NTT2[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.177416100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.177917400+08:00" level=error msg="CC2 | 上海移动转日本NTT2[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.177917400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.182505700+08:00" level=error msg="CC2 | 上海移动转香港BGP[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.182505700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.183014700+08:00" level=error msg="CC1 | 上海移动转香港BGP[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.183014700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.183014700+08:00" level=error msg="CC1 | 上海移动转香港BGP2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.183014700+08:00" level=error msg="CC2 | 上海移动转香港BGP2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.183014700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.183014700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.262611100+08:00" level=warning msg="[UDP] dial 📢 Google (match RuleSet/google-rules) **********:52109(msedge.exe) --> clients2.google.com:443 error: can't resolve ip: couldn't find ip: clients2.google.com"
time="2025-06-25T20:08:00.487558700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:51996(OneDrive.exe) --> storage.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:00.509919300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:51997(OneDrive.exe) --> roaming.officeapps.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:00.834934900+08:00" level=error msg="CC2 | 上海移动转香港HKT3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.834934900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.889625600+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:52106(msedgewebview2.exe) --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:00.927784700+08:00" level=error msg="CC2 | 上海移动转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.927784700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:00.927784700+08:00" level=error msg="CC1 | 上海移动转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:00.927784700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.178718900+08:00" level=error msg="CT1 | 🇺🇸 美国-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.178718900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.466010900+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.466010900+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港03丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.466010900+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.466010900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.466010900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.466010900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.466664900+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港04丨超速ᴠ⁶【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.466664900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.474463700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6431666634386430 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.474463700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.476494300+08:00" level=error msg="CC2 | 上海移动转香港NTT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.476494300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.476494300+08:00" level=error msg="CC2 | 上海移动转香港NTT3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.476494300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.477003500+08:00" level=error msg="CC1 | 上海移动转香港NTT3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.477003500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.477003500+08:00" level=error msg="CC1 | 上海移动转香港NTT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:01.477003500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:01.508211000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:52233(CrossDeviceService.exe) --> default.exp-tas.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:01.621595100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:52396(svchost.exe) --> client.wns.windows.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:03.606156800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: go.cqjs.link:80 connect error: dial tcp *************:80: operation was canceled\ndial tcp *************:80: operation was canceled\ndial tcp *************:80: operation was canceled\ndial tcp *************:80: operation was canceled"
time="2025-06-25T20:08:03.850089600+08:00" level=error msg="CT1 | 🇺🇸 美国-3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:03.850089600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:03.851694200+08:00" level=error msg="CT1 | 🇺🇸 美国-4 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:03.851694200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:03.936414700+08:00" level=error msg="CT2 | 🇺🇸 美国-3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:03.936414700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.100326000+08:00" level=error msg="CC2 | 上海联通转台湾HiNet[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.100835200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.190293600+08:00" level=error msg="CT2 | 🇺🇸 美国-4 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.190293600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.190795100+08:00" level=error msg="CC1 | 上海联通转德国DF[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.190795100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.190795100+08:00" level=error msg="CC2 | 上海联通转德国DF[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.190795100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.213823900+08:00" level=error msg="CT1 | 🇲🇾 马来西亚-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.213823900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.332308200+08:00" level=error msg="CT1 | 🇳🇬  尼日利亚-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.332308200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.436704300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:52771(OneDrive.exe) --> 194366-ipv4mte.gr.global.aa-rt.sharepoint.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:04.451210300+08:00" level=error msg="CT2 | 🇲🇾 马来西亚-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.451210300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.692476900+08:00" level=error msg="CT2 | 🇳🇬  尼日利亚-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.692476900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.912217800+08:00" level=error msg="CT1 | 🇬🇧 英国-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.912217800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.913513500+08:00" level=error msg="CT1 | 🇩🇪 德国-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.913513500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:04.937725000+08:00" level=error msg="CC1 | 上海联通转新加坡BGP[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:04.937725000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:05.078725500+08:00" level=warning msg="[UDP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55416 --> substrate.office.com:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:08:05.176074200+08:00" level=error msg="CC2 | 上海联通转新加坡BGP[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:05.176074200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:05.177642200+08:00" level=error msg="CC1 | 上海联通转新加坡BGP2[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:05.177642200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:05.178170000+08:00" level=error msg="CC2 | 上海联通转新加坡BGP2[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:05.178170000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:05.967205900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53017(svchost.exe) --> v10.events.data.microsoft.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:06.474605000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3338373762663838 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:06.474605000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:06.521203200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53128(msedge.exe) --> edge-consumer-static.azureedge.net:443 error: context deadline exceeded"
time="2025-06-25T20:08:06.637450800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53174(msedge.exe) --> substrate.office.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:06.637450800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53175(msedge.exe) --> substrate.office.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:06.873863400+08:00" level=error msg="CC2 | 上海联通转日本BGP5[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:54570->************:65100: use of closed network connection"
time="2025-06-25T20:08:06.873863400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:07.677998700+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53306(svchost.exe) --> g.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:07.866343500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-25T20:08:07.866343500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-25T20:08:07.866343500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-25T20:08:07.866343500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-25T20:08:08.610056100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53440(OneDrive.exe) --> 194366-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:09.100954200+08:00" level=error msg="CC2 | 上海联通转日本BGP2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:09.100954200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:09.625586000+08:00" level=error msg="CC1 | 上海联通转日本BGP4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:09.625586000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:10.036106500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53691(OneDrive.exe) --> skydrive.wns.windows.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.077315100+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡05丨超速ᴠ⁶【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:10.077315100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:10.178093600+08:00" level=error msg="CC1 | 上海联通转日本BGP6[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:10.178093600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:10.493719200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53897(OneDrive.exe) --> storage.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.516231500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:53898(OneDrive.exe) --> roaming.officeapps.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.899442000+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:54186 --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.899442000+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:54611(clash-verge.exe) --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.899943800+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:54192(msedgewebview2.exe) --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:10.899943800+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:53942 --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:11.230996900+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本04丨超速ᴠ⁶【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.230996900+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本05丨超速ᴠ⁶【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.230996900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.230996900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.234862600+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本06丨超速ᴠ⁶【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.234862600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.235383700+08:00" level=error msg="CC1 | 上海联通转日本NTT12[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.235383700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.235383700+08:00" level=error msg="CC1 | 上海联通转日本NTT13[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.235383700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.466242000+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本原生01丨专线【4x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.466242000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:11.466242000+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本原生02丨专线【4x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:11.466242000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:12.891300900+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本原生03丨专线【4x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:12.891300900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:12.896842800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:54297(SIHClient.exe) --> fe3cr.delivery.mp.microsoft.com:443 error: **************:5067 connect error: shct.1kgbbf2mga.ccddn4.icu:65301 connect error: context deadline exceeded"
time="2025-06-25T20:08:13.574827000+08:00" level=error msg="CC1 | 上海联通转日本NTT4[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.574827000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:13.682942300+08:00" level=error msg="CC2 | 上海联通转日本NTT4[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.682942300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:13.691179800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:54432(OneDrive.exe) --> 194366-ipv4mte.gr.global.aa-rt.sharepoint.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:13.777782000+08:00" level=error msg="CC2 | 上海联通转日本NTT5[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.777782000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:13.909629400+08:00" level=error msg="PKM1 | 🇹🇼【亚洲】台湾家宽01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.909629400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:13.909629400+08:00" level=error msg="CC2 | 上海联通转日本NTT6[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.909629400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:13.910644200+08:00" level=error msg="CC2 | 上海联通转日本NTT7[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:13.910644200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:14.548489800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:54830(msedge.exe) --> substrate.office.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:14.548489800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:54831(msedge.exe) --> substrate.office.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:14.625724200+08:00" level=error msg="CC1 | 上海联通转日本NTT7[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:14.625724200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:14.652093400+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:51506 --> fastly.jsdelivr.net:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:08:14.915271500+08:00" level=warning msg="[TCP] dial 💻 Copilot (match RuleSet/copilot) **********:54646(FileSyncHelper.exe) --> self.events.data.microsoft.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:15.128201400+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:54713 --> cdn.jsdelivr.net:443 error: can't resolve ip: couldn't find ip"
time="2025-06-25T20:08:15.867926300+08:00" level=error msg="CC2 | 上海联通转日本TE[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:15.867926300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:16.167473400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:54792(msedge.exe) --> edge-consumer-static.azureedge.net:443 error: context deadline exceeded"
time="2025-06-25T20:08:16.652559800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55613(CrossDeviceService.exe) --> default.exp-tas.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:16.784063500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55000(OneDrive.exe) --> mobile.events.data.microsoft.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:16.874567200+08:00" level=error msg="CC2 | 上海联通转日本TE3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:16.874567200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:17.326300600+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:55157(clash-verge.exe) --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:17.326300600+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:55828(msedgewebview2.exe) --> github.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:17.891755000+08:00" level=error msg="PKM1 | 🇰🇷【亚洲】韩国家宽04丨超速【6x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:17.891755000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:17.894845900+08:00" level=error msg="CC1 | 上海联通转美国AN[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:17.894845900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:17.894845900+08:00" level=error msg="CC1 | 上海联通转日本TE3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:17.894845900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:17.979958200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55318(svchost.exe) --> ctldl.windowsupdate.com:80 error: context deadline exceeded"
time="2025-06-25T20:08:18.629221500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55389(OneDrive.exe) --> 194366-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:18.699127600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55454(svchost.exe) --> g.live.com:443 error: context deadline exceeded"
time="2025-06-25T20:08:20.091518700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6331353039643137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:20.091518700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:23.081221000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6636346264353037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:23.081221000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-25T20:08:23.461964500+08:00" level=error msg="CC1 | 上海联通转美国Cera5[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-25T20:08:23.461964500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
