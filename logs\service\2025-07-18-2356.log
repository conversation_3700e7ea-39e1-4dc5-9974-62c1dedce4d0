Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-18T23:56:34.898619200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T23:56:34.904262200+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-18T23:56:34.904262200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T23:56:34.904773800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-18T23:56:34.908363700+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7484"
time="2025-07-18T23:56:34.908363700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-18T23:56:35.116833300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119041"
time="2025-07-18T23:56:35.120102200+08:00" level=info msg="Initial configuration complete, total time: 218ms"
time="2025-07-18T23:56:35.121106000+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-18T23:56:35.121106000+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-18T23:56:35.121106000+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-18T23:56:35.137457500+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-18T23:56:35.665882900+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-18T23:56:35.777052800+08:00" level=warning msg="[Provider] free-2 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.777563000+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.778086000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:35.778086000+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.780041800+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.786218100+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.792262200+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.794344200+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-07-18T23:56:35.794849500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:35.794849500+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.817296200+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-07-18T23:56:35.840621800+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-18T23:56:36.281306900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-18T23:56:36.774564200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:37.558871900+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-18T23:56:37.742082000+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-18T23:56:37.771750900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:56:37.772283200+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:38.013806500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-18T23:56:38.397335300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:38.553536300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:38.553536300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-18T23:56:38.553536300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:38.741890900+08:00" level=error msg="[Sniffer] [**************] [tls] may not have any sent data, Consider adding skip"
time="2025-07-18T23:56:39.057168200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.057168200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.328465800+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-18T23:56:39.351709800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-18T23:56:39.351709800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-18T23:56:39.362202300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.362202300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.451835900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.451835900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.452341300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.552417700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.555805300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.555805300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.556307800+08:00" level=error msg="Free-2 | 🇸🇬SG_2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-18T23:56:39.556307800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:39.762563000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.788741200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.792843100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:39.989755700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.009382000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.009382000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.013658100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-18T23:56:40.211898500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.232101300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.233676000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.242636100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.243282300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.439707300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.443948000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.498487300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.517130300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.777312100+08:00" level=error msg="Free-2 | 🇹🇼TW_2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777312100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.777819900+08:00" level=error msg="FL | 套餐到期：2025-08-10 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777819900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.777819900+08:00" level=error msg="FL | 香港 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777819900+08:00" level=error msg="FL | 美国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777819900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.777819900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.777819900+08:00" level=error msg="FL | 距离下次重置剩余：23 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777819900+08:00" level=error msg="FL | 剩余流量：999.9 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.777819900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.777819900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.789646800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.792736200+08:00" level=error msg="PKM2 | 套餐到期：2025-08-25 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.792736200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.792736200+08:00" level=error msg="PKM2 | 剩余流量：56.79 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.792736200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.795116300+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.795116300+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.795116300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.795116300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.817588100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6566333337323830 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:40.817588100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:40.818089900+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-18T23:56:40.841169600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:40.841169600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:40.841169600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:40.841169600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:40.854731800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:40.891358700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.070091700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.088929100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.125466700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.181407100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.282338200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.499456400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.499456400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-18T23:56:41.647107700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.803670700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.886629100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.936314700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:41.936314700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-18T23:56:42.091311600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:42.283478000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:42.517395100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:42.972403100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:42.982224400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:43.016535100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> fastgh.lainbo.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-18T23:56:43.117302000+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-18T23:56:43.119460900+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-18T23:56:43.120480100+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-18T23:56:43.120480100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:43.128851600+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-18T23:56:43.128851600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-18T23:56:43.128851600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context canceled"
time="2025-07-18T23:56:43.129882800+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-18T23:56:43.129882800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:43.172290500+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-18T23:56:43.418282000+08:00" level=error msg="Free-2 | 🇨🇦CA_3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-18T23:56:43.418282000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:44.029127500+08:00" level=error msg="[GEO] update GEO database error: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-07-18T23:56:44.029127500+08:00" level=error msg="[GEO] Failed to update GEO database: can't download GeoIP database file: Get \"https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat\": EOF"
time="2025-07-18T23:56:44.217405500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-18T23:56:44.217405500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-18T23:56:45.118238700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:56:45.118238700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:45.118238700+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-18T23:56:45.118238700+08:00" level=warning msg="because Free-2 failed multiple times, active health check"
time="2025-07-18T23:56:45.793155000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-18T23:56:45.817959600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3262323339376535 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:45.817959600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:46.542646700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-18T23:56:46.750451300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-18T23:56:47.753629700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-18T23:56:48.119778100+08:00" level=error msg="FL | 剩余流量：999.9 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.119778100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.128965800+08:00" level=error msg="PKM2 | 套餐到期：2025-08-25 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.128965800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.128965800+08:00" level=error msg="PKM2 | 距离下次重置剩余：11 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.128965800+08:00" level=error msg="PKM2 | 备用官网 https://52pokemon.huxx.top failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.128965800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.128965800+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.128965800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.128965800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.129983700+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.129983700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.129983700+08:00" level=error msg="PKM1 | 套餐到期：长期有效 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.129983700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.129983700+08:00" level=error msg="PKM1 | 备用官网 https://52pokemon.huxx.top failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.129983700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.129983700+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:48.129983700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:48.172560700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:48.172560700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:48.172560700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:48.172560700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-18T23:56:48.382428600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-18T23:56:49.315930400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-18T23:56:49.966540800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3735373662646135 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:49.966540800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:51.677976000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3233313763666265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:51.677976000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:51.869302400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3465343862366439 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T23:56:51.869302400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T23:56:53.417602700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": context deadline exceeded"
time="2025-07-18T23:56:54.684811400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-18T23:56:54.997869100+08:00" level=warning msg="because Free-2 failed multiple times, active health check"
time="2025-07-18T23:56:54.997869100+08:00" level=warning msg="because Free-2 failed multiple times, active health check"
time="2025-07-18T23:56:54.997869100+08:00" level=warning msg="because Free-2 failed multiple times, active health check"
time="2025-07-18T23:56:54.997869100+08:00" level=warning msg="because Free-2 failed multiple times, active health check"
time="2025-07-18T23:56:58.030971800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-18T23:56:58.030971800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-18T23:56:58.421862900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:56:58.421862900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:56:58.421862900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:56:58.421862900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-18T23:57:00.759711600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": context deadline exceeded"
