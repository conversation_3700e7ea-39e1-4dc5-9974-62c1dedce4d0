port: 7890
socks-port: 7891
mixed-port: 7892
allow-lan: false
bind-address: "*"
mode: rule
log-level: info
ipv6: false
find-process-mode: strict
external-controller: 127.0.0.1:9090
profile:
  store-selected: true
  store-fake-ip: true
unified-delay: true
tcp-concurrent: true
global-ua: clash.meta

sniffer:
  enable: true
  force-dns-mapping: true
  parse-pure-ip: true
  override-destination: true
  sniff:
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true
    TLS:
      ports: [443, 8443]
    QUIC:
      ports: [443, 8443]
  skip-domain:
    - Mijia Cloud

ntp:
  enable: true
  write-to-system: false
  server: ntp.aliyun.com
  port: 123
  interval: 30

dns:
  enable: true
  prefer-h3: false
  listen: 0.0.0.0:1053
  ipv6: false
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  fake-ip-filter:
    - "+.lan"
    - "+.local"
    - localhost.ptlogin2.qq.com
    - "+.msftconnecttest.com"
    - "+.msftncsi.com"
    - "+.googleapis.com"
    - "+.googleapis.cn"
    - alt1-mtalk.google.com
    - alt2-mtalk.google.com
    - alt3-mtalk.google.com
    - alt4-mtalk.google.com
    - alt5-mtalk.google.com
    - alt6-mtalk.google.com
    - alt7-mtalk.google.com
    - alt8-mtalk.google.com
    - mtalk.google.com
  use-hosts: true
  default-nameserver:
    - '***************#DIRECT'
    - '*********#DIRECT'
    - '************#DIRECT'
    - '************#DIRECT'
    - '***********#DIRECT'
  proxy-server-nameserver:
    - 'https://dns.alidns.com/dns-query#DIRECT'
    - 'https://doh.pub/dns-query#DIRECT'
    - 'https://doh.onedns.net/dns-query#DIRECT'
  nameserver:
    - "https://cloudflare-dns.com/dns-query#🌏️Main Proxy"
  nameserver-policy:
    "ntp.aliyun.com": "https://dns.alidns.com/dns-query#DIRECT"
    "+.jsdmirror.com": "https://dns.alidns.com/dns-query#DIRECT"
    "fastly.jsdelivr.net": "https://dns.alidns.com/dns-query#DIRECT"
    "proxy.sakuraikaede.com": "https://dns.alidns.com/dns-query#DIRECT"
    "converter-mitce.sakuraikaede.com": "https://dns.alidns.com/dns-query#DIRECT"
    "+.msftconnecttest.com,+.msftncsi.com": "https://cloudflare-dns.com/dns-query#🌏️Main Proxy"
    "+.googleapis.com,+.googleapis.cn": "https://cloudflare-dns.com/dns-query#🔍Google"
    "rule-set:Bahamut-Site": "https://cloudflare-dns.com/dns-query#🌸Bahamut"
    "rule-set:Bilibili-Site": "https://dns.alidns.com/dns-query#📺Bilibili"
    "rule-set:Discord-Site": "https://cloudflare-dns.com/dns-query#🎙️Discord"
    "rule-set:GoogleFCM-Site": "https://cloudflare-dns.com/dns-query#☁️GoogleFCM"
    "rule-set:Netflix-Site": "https://cloudflare-dns.com/dns-query#📽️Netflix"
    "rule-set:OpenAI-Site": "https://cloudflare-dns.com/dns-query#🤖OpenAI"
    "rule-set:Speedtest-Site": "https://cloudflare-dns.com/dns-query#⏱️Speedtest"
    "rule-set:Spotify-Site": "https://cloudflare-dns.com/dns-query#🎵Spotify"
    "rule-set:Steam-Site": "https://doh.pub/dns-query#🎮Steam"
    "rule-set:Telegram-Site": "https://cloudflare-dns.com/dns-query#✈️Telegram"
    "rule-set:TikTok-Site": "https://cloudflare-dns.com/dns-query#🎶TikTok"
    "rule-set:Apple-Site": "https://doh.pub/dns-query#🍎Apple"
    "rule-set:Google-Site": "https://cloudflare-dns.com/dns-query#🔍Google"
    "rule-set:Microsoft-Site": "https://doh.pub/dns-query#💻Microsoft"
    "rule-set:GFWList-Site": "https://cloudflare-dns.com/dns-query#🧱Blocked Services"
    "rule-set:China-Site": "https://dns.alidns.com/dns-query#🇨🇳China Services"

tun:
  enable: true
  stack: system
  auto-route: true
  auto-detect-interface: true
  strict-route: true
  dns-hijack:
    - any:53
  device: SakuraiTunnel
  mtu: 9000
  endpoint-independent-nat: true

proxies:
  - name: "🇭🇰HK1-A"
    type: vless
    server: ************
    port: 22201
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: rshk.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiowedqwdfiwehjfiwehjfhjewi
      headers:
        Host: rshk.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇭🇰HK2-A"
    type: vless
    server: **************
    port: 45505
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: rshk.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiowedqwdfiwehjfiwehjfhjewi
      headers:
        Host: rshk.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇭🇰HK3-A"
    type: vless
    server: ************
    port: 65240
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: rshk.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiowedqwdfiwehjfiwehjfhjewi
      headers:
        Host: rshk.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇭🇰HK4-A"
    type: vless
    server: ***********
    port: 15230
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: rshk.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiowedqwdfiwehjfiwehjfhjewi
      headers:
        Host: rshk.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇭🇰HK5-A"
    type: vless
    server: ***********
    port: 50641
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: rshk.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiowedqwdfiwehjfiwehjfhjewi
      headers:
        Host: rshk.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇸🇬SG1-B"
    type: vless
    server: ************
    port: 47311
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiosedhfiosfiosdhiofhsdiofhisdofhsd
      headers:
        Host: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇸🇬SG2-B"
    type: vless
    server: ************
    port: 29825
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiosedhfiosfiosdhiofhsdiofhisdofhsd
      headers:
        Host: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇸🇬SG3-B"
    type: vless
    server: **************
    port: 1776
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiosedhfiosfiosdhiofhsdiofhisdofhsd
      headers:
        Host: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇸🇬SG4-B"
    type: vless
    server: *************
    port: 443
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiosedhfiosfiosdhiofhsdiofhisdofhsd
      headers:
        Host: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇸🇬SG5-B"
    type: vless
    server: ************
    port: 30993
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /hiosedhfiosfiosdhiofhsdiofhisdofhsd
      headers:
        Host: sgsgsgsgsg.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇺🇸US1-C"
    type: vless
    server: **************
    port: 17636
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: ususususus.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /sdiohfiosdhfuiosghfuhse
      headers:
        Host: ususususus.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇺🇸US2-C"
    type: vless
    server: *************
    port: 443
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: ususususus.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /sdiohfiosdhfuiosghfuhse
      headers:
        Host: ususususus.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇺🇸US3-C"
    type: vless
    server: *************
    port: 443
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: ususususus.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /sdiohfiosdhfuiosghfuhse
      headers:
        Host: ususususus.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇺🇸US4-C"
    type: vless
    server: *************
    port: 13598
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: ususususus.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /sdiohfiosdhfuiosghfuhse
      headers:
        Host: ususususus.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

  - name: "🇺🇸US5-C"
    type: vless
    server: ************
    port: 19050
    udp: true
    uuid: 42b3f738-177e-463b-a81a-5e31c1725c27
    packet-encoding: xudp
    tls: true
    servername: ususususus.eessgrgrgrgr.cloudns.nz
    client-fingerprint: chrome
    skip-cert-verify: false
    network: ws
    ws-opts:
      path: /sdiohfiosdhfuiosghfuhse
      headers:
        Host: ususususus.eessgrgrgrgr.cloudns.nz
      v2ray-http-upgrade: true
      v2ray-http-upgrade-fast-open: true
    tfo: true
    mptcp: true
    smux:
      enabled: true
      protocol: h2mux
      max-streams: 10
      padding: true

proxy-groups:
  - name: "🌏️Main Proxy"
    type: select
    proxies:
      - "♾️Auto Select"
      - "⚖Load Balance"
      - DIRECT
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🇨🇳China Services"
    type: select
    proxies:
      - DIRECT
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🧱Blocked Services"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🔍Google"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🍎Apple"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - DIRECT
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "💻Microsoft"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - DIRECT
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🌸Bahamut"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "📺Bilibili"
    type: select
    proxies:
      - DIRECT
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🎙️Discord"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "☁️GoogleFCM"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "📽️Netflix"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🤖OpenAI"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "⏱️Speedtest"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🎵Spotify"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🎮Steam"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - DIRECT
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "✈️Telegram"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "🎶TikTok"
    type: select
    proxies:
      - "🌏️Main Proxy"
      - "♾️Auto Select"
      - "⚖Load Balance"
      - REJECT
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C

  - name: "⚖Load Balance"
    type: load-balance
    proxies:
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C
    url: "https://cp.cloudflare.com"
    interval: 180
    lazy: true
    strategy: consistent-hashing

  - name: "♾️Auto Select"
    type: url-test
    proxies:
      - 🇭🇰HK1-A
      - 🇭🇰HK2-A
      - 🇭🇰HK3-A
      - 🇭🇰HK4-A
      - 🇭🇰HK5-A
      - 🇸🇬SG1-B
      - 🇸🇬SG2-B
      - 🇸🇬SG3-B
      - 🇸🇬SG4-B
      - 🇸🇬SG5-B
      - 🇺🇸US1-C
      - 🇺🇸US2-C
      - 🇺🇸US3-C
      - 🇺🇸US4-C
      - 🇺🇸US5-C
    url: "https://cp.cloudflare.com"
    interval: 180
    lazy: false

rule-providers:
  Apple-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Apple/Apple-Site.mrs"
    path: ./ruleset/Apple-Site.mrs
    proxy: DIRECT
    interval: 86400

  Apple-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Apple/Apple-IP.mrs"
    path: ./ruleset/Apple-IP.mrs
    proxy: DIRECT
    interval: 86400

  Bahamut-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Bahamut/Bahamut-Site.mrs"
    path: ./ruleset/Bahamut-Site.mrs
    proxy: DIRECT
    interval: 86400

  Bilibili-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Bilibili/Bilibili-Site.mrs"
    path: ./ruleset/Bilibili-Site.mrs
    proxy: DIRECT
    interval: 86400

  Bilibili-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Bilibili/Bilibili-IP.mrs"
    path: ./ruleset/Bilibili-IP.mrs
    proxy: DIRECT
    interval: 86400

  China-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/China/China-Site.mrs"
    path: ./ruleset/China-Site.mrs
    proxy: DIRECT
    interval: 86400

  China-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/China/China-IP.mrs"
    path: ./ruleset/China-IP.mrs
    proxy: DIRECT
    interval: 86400

  Discord-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Discord/Discord-Site.mrs"
    path: ./ruleset/Discord-Site.mrs
    proxy: DIRECT
    interval: 86400

  GFWList-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/GFWList/GFWList-Site.mrs"
    path: ./ruleset/GFWList-Site.mrs
    proxy: DIRECT
    interval: 86400

  Google-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Google/Google-Site.mrs"
    path: ./ruleset/Google-Site.mrs
    proxy: DIRECT
    interval: 86400

  Google-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Google/Google-IP.mrs"
    path: ./ruleset/Google-IP.mrs
    proxy: DIRECT
    interval: 86400

  GoogleFCM-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/GoogleFCM/GoogleFCM-Site.mrs"
    path: ./ruleset/GoogleFCM-Site.mrs
    proxy: DIRECT
    interval: 86400

  Local-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Local/Local-IP.mrs"
    path: ./ruleset/Local-IP.mrs
    proxy: DIRECT
    interval: 86400

  Microsoft-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Microsoft/Microsoft-Site.mrs"
    path: ./ruleset/Microsoft-Site.mrs
    proxy: DIRECT
    interval: 86400

  Netflix-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Netflix/Netflix-Site.mrs"
    path: ./ruleset/Netflix-Site.mrs
    proxy: DIRECT
    interval: 86400

  Netflix-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Netflix/Netflix-IP.mrs"
    path: ./ruleset/Netflix-IP.mrs
    proxy: DIRECT
    interval: 86400

  OpenAI-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/OpenAI/OpenAI-Site.mrs"
    path: ./ruleset/OpenAI-Site.mrs
    proxy: DIRECT
    interval: 86400

  OpenAI-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/OpenAI/OpenAI-IP.mrs"
    path: ./ruleset/OpenAI-IP.mrs
    proxy: DIRECT
    interval: 86400

  Speedtest-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Speedtest/Speedtest-Site.mrs"
    path: ./ruleset/Speedtest-Site.mrs
    proxy: DIRECT
    interval: 86400

  Spotify-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Spotify/Spotify-Site.mrs"
    path: ./ruleset/Spotify-Site.mrs
    proxy: DIRECT
    interval: 86400

  Spotify-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Spotify/Spotify-IP.mrs"
    path: ./ruleset/Spotify-IP.mrs
    proxy: DIRECT
    interval: 86400

  Steam-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Steam/Steam-Site.mrs"
    path: ./ruleset/Steam-Site.mrs
    proxy: DIRECT
    interval: 86400

  Telegram-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Telegram/Telegram-Site.mrs"
    path: ./ruleset/Telegram-Site.mrs
    proxy: DIRECT
    interval: 86400

  Telegram-IP:
    type: http
    behavior: ipcidr
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/Telegram/Telegram-IP.mrs"
    path: ./ruleset/Telegram-IP.mrs
    proxy: DIRECT
    interval: 86400

  TikTok-Site:
    type: http
    behavior: domain
    format: mrs
    url: "https://cdn.jsdmirror.com/gh/HosheaPDNX/rule-set@V2.0.0/mihomo/TikTok/TikTok-Site.mrs"
    path: ./ruleset/TikTok-Site.mrs
    proxy: DIRECT
    interval: 86400

rules:
  - "AND,((DST-PORT,443),(NETWORK,UDP)),REJECT"
  - "DOMAIN,ntp.aliyun.com,DIRECT"
  - "DOMAIN,proxy.sakuraikaede.com,DIRECT"
  - "DOMAIN,converter-mitce.sakuraikaede.com,DIRECT"
  - "DOMAIN-KEYWORD,msftconnecttest.com,🌏️Main Proxy"
  - "DOMAIN-KEYWORD,msftncsi.com,🌏️Main Proxy"
  - "DOMAIN-KEYWORD,googleapis,🔍Google"
  - "RULE-SET,Bahamut-Site,🌸Bahamut"
  - "RULE-SET,Bilibili-Site,📺Bilibili"
  - "RULE-SET,Discord-Site,🎙️Discord"
  - "RULE-SET,GoogleFCM-Site,☁️GoogleFCM"
  - "RULE-SET,Netflix-Site,📽️Netflix"
  - "RULE-SET,OpenAI-Site,🤖OpenAI"
  - "RULE-SET,Speedtest-Site,⏱️Speedtest"
  - "RULE-SET,Spotify-Site,🎵Spotify"
  - "RULE-SET,Steam-Site,🎮Steam"
  - "RULE-SET,Telegram-Site,✈️Telegram"
  - "RULE-SET,TikTok-Site,🎶TikTok"
  - "RULE-SET,Apple-Site,🍎Apple"
  - "RULE-SET,Google-Site,🔍Google"
  - "RULE-SET,Microsoft-Site,💻Microsoft"
  - "RULE-SET,GFWList-Site,🧱Blocked Services"
  - "RULE-SET,China-Site,🇨🇳China Services"
  - "RULE-SET,Local-IP,DIRECT,no-resolve"
  - "RULE-SET,Bilibili-IP,📺Bilibili"
  - "RULE-SET,Netflix-IP,📽️Netflix"
  - "RULE-SET,OpenAI-IP,🤖OpenAI"
  - "RULE-SET,Spotify-IP,🎵Spotify"
  - "RULE-SET,Telegram-IP,✈️Telegram"
  - "RULE-SET,Apple-IP,🍎Apple"
  - "RULE-SET,Google-IP,🔍Google"
  - "RULE-SET,China-IP,🇨🇳China Services"
  - "MATCH,🌏️Main Proxy"
