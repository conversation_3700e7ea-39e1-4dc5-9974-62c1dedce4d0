Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-23T21:50:54.727074900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-23T21:50:54.732729200+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-23T21:50:54.732729200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-23T21:50:54.733234100+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-23T21:50:54.737144400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7484"
time="2025-07-23T21:50:54.737144400+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-23T21:50:54.953718200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119019"
time="2025-07-23T21:50:54.959427100+08:00" level=info msg="Initial configuration complete, total time: 229ms"
time="2025-07-23T21:50:55.007517200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-23T21:50:55.736728100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-23T21:50:55.942743100+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-23T21:50:55.944778700+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-23T21:50:55.944778700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-23T21:50:55.948986400+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-23T21:50:55.950559300+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-23T21:50:55.951660000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-23T21:50:55.951660000+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-23T21:50:55.986968800+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-23T21:50:57.145160400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:57.230454800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-23T21:50:57.456478000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:50:57.457006100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:50:57.940235200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:57.940235200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:57.940235200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:50:58.093948600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-23T21:50:58.163058000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:50:58.519516900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:58.525546900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:58.577645200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:50:58.578146900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:50:58.824077300+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:50:59.395844900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:59.431588000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:50:59.896331200+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.896331200+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.896331200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.896331200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.896832900+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.896832900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.896832900+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.896832900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.897340600+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.897340600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🎯 Webshare-US专线-Direct failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🇺🇸 Landing-US-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🏠 Landing-Home-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🚀 Landing-Auto-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🚄 Landing-Fast-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898356800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898356800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898868700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898868700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.898868700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.898868700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.908516500+08:00" level=error msg="🔗 Landing-HK-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.908516500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:50:59.908516500+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:50:59.908516500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:00.386486300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:00.387863600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:00.900997500+08:00" level=error msg="FL | 香港 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:00.900997500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:00.976849500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6139646430646237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:00.976849500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:00.987261100+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:51:00.987770800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:51:00.987770800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:51:00.987770800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:51:01.331079300+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:01.331079300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:02.072344100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:02.072344100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:03.119623600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-23T21:51:03.119623600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-23T21:51:03.409320000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:59523(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:51:03.409320000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:59512 --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:51:04.249480600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_63333630656431 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-23T21:51:04.249480600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:05.036062600+08:00" level=error msg="Free-2 | 🇨🇦CA_1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:05.036062600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:05.096212700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59932->************:3596: use of closed network connection"
time="2025-07-23T21:51:05.096212700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:05.945063800+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": net/http: TLS handshake timeout"
time="2025-07-23T21:51:05.951741200+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:05.951741200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:07.182971400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T21:51:08.058876800+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💼_github.com/Ruk1ng001_6136633239376231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:08.058876800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:10.016143500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:60077(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-23T21:51:10.803366900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:12.451038600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T21:51:12.451038600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-23T21:51:14.710957800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-23T21:51:15.147749600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:15.151314600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:15.260062800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:15.260062800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:15.679430500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/lancidr) **********:60432(ssh.exe) --> **********:22 error: dial tcp **********:22: i/o timeout"
time="2025-07-23T21:51:22.747449300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6231366465653033 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:22.747449300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:24.161219200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-23T21:51:25.743701400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/lancidr) **********:60721(ssh.exe) --> **********:22 error: dial tcp **********:22: i/o timeout"
time="2025-07-23T21:51:25.945433900+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T21:51:31.728068700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-23T21:51:35.935930100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3535623233656635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:51:35.935930100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:36.088045400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-23T21:51:39.146447200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:51:39.148481900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:51:39.232224000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:51:49.023415000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6339623532643630 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:51:49.023415000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:51:49.867361800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:49.988911900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:50.106779200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:50.246026200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:50.397591800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:50.665285900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:51.078499300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:51.281157500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:52.378769400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:52.569021600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:51:53.478954400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:61266(OneDrive.exe) --> skydrive.wns.windows.com:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-23T21:51:57.586310600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-23T21:51:59.501235800+08:00" level=error msg="CT1 | 🇹🇷 土耳其-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:61502->**************:21991: use of closed network connection"
time="2025-07-23T21:51:59.501235800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:05.959726300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:52:09.275410300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_63333630656431 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62069->************:44123: use of closed network connection"
time="2025-07-23T21:52:09.275410300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:13.075377500+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:52:13.075377500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:15.945909200+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": net/http: TLS handshake timeout"
time="2025-07-23T21:52:16.531958500+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💼_github.com/Ruk1ng001_6136633239376231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:52:16.531958500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:31.867416900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3536356664306537 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-23T21:52:31.867416900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:37.505336800+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💼_github.com/Ruk1ng001_3662346364343063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:52:37.505336800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:43.630235400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3535623233656635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62555->************:32962: use of closed network connection"
time="2025-07-23T21:52:43.630235400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:54.254481200+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3136376638393361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:52:54.254481200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:55.715711300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6339623532643630 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:52:55.715711300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:52:58.416809400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:52:59.632187100+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:52:59.717073300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:52:59.722644800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:53:06.511687800+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:53:06.511687800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:53:21.160042600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-23T21:53:23.449644400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:53:30.338714100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:53:30.338714100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:53:42.311594500+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:53:42.311594500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:53:47.952349600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3536356664306537 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:53:47.952349600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:53:53.998386500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6339636361653237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:53:53.998386500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:53:55.946567000+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T21:53:56.406259500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:53:56.476843900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:53:56.632270600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55230(svchost.exe) --> login.live.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:54:01.637066400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:55556(svchost.exe) --> login.live.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:54:03.994909700+08:00" level=error msg="CC | 广东移动转香港HKT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:03.994909700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:05.308365300+08:00" level=error msg="CC | 广港专线转香港BGP[M][Trojan][倍率:1.7] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:05.308365300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:15.224496900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:54:36.562693500+08:00" level=error msg="CC | 上海联通转香港HKC4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:36.563196500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:38.389127300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainSuffix/qq.com) **********:57124(QQMusic.exe) --> wspeed.qq.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-23T21:54:38.804883200+08:00" level=error msg="CC | 广东移动转香港NTT3[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:38.804883200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:40.424306000+08:00" level=error msg="CC | 广州移动转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:40.424306000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:42.697377900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:57110(QQMusic.exe) --> y.gtimg.cn:14000 error: dial tcp [2408:8726:1001:122:37::17]:14000: connectex: The requested address is not valid in its context.\ndial tcp ***************:14000: i/o timeout"
time="2025-07-23T21:54:44.962334900+08:00" level=error msg="CC | 深港专线转香港BGP8[M][Trojan][倍率:2.5] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:44.962334900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:45.643986500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:54:50.645538000+08:00" level=error msg="Free-2 | 🇭🇰HK_3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:50.645538000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:53.963794900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_63333630656431 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59020->************:44123: use of closed network connection"
time="2025-07-23T21:54:53.963794900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:54.821479000+08:00" level=error msg="Free-2 | 🇬🇧UK_1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:54.821479000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:55.725883600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6436613836333164 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:55.725883600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:55.975823300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-23T21:54:55.975823300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:54:58.713046900+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💼_github.com/Ruk1ng001_6136633239376231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:54:58.713046900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:00.020272700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:55:00.073789600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:55:00.073789600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:55:07.742808900+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:55:07.742808900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:14.122819700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3536356664306537 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:55:14.122819700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:26.207278700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3535623233656635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:60764->************:32962: use of closed network connection"
time="2025-07-23T21:55:26.207278700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:40.419729100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3334356634346233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:55:40.419729100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:41.880762000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:55:46.881166100+08:00" level=error msg="PKM2 | 距离下次重置剩余：11 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:55:46.881166100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:51.811152300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:61226(nvcontainer.exe) --> prod.otel.kaizen.nvidia.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:55:53.658472600+08:00" level=error msg="CC | 广东移动转日本NTT6[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:55:53.658472600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:55:58.601848000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_63333630656431 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-23T21:55:58.601848000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:56:01.006002900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:61695(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:01.952817800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:61771(msedge.exe) --> weatheroffer.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:01.952817800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:61770(msedge.exe) --> weatheroffer.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:02.584726000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62015->************:3596: use of closed network connection"
time="2025-07-23T21:56:02.584726000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:56:06.011273200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62000(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:06.958870400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62059(msedge.exe) --> weatheroffer.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:06.959418900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:62058(msedge.exe) --> weatheroffer.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:07.006152900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62065(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:08.290877800+08:00" level=error msg="CC | 广州移动转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:56:08.290877800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:56:09.720009500+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-23T21:56:09.899870400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:56:11.610723400+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:62257(svchost.exe) --> login.live.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:12.011891200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62265(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:12.019582800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62267(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:15.947055700+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T21:56:17.025345800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62529(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:47.506361200+08:00" level=error msg="Free-6 | ❓_🇦🇺_💻_github.com/Ruk1ng001_6339643963623836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:56:47.506361200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:56:48.207608500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:56:51.826662500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:63110(nvcontainer.exe) --> prod.otel.kaizen.nvidia.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T21:56:56.352149300+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:56:56.468664300+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:57:00.524595300+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:57:00.618060500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:57:00.650886600+08:00" level=error msg="MLY | HK-香港 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:57:00.650886600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:57:01.624409600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:57:14.591945800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:57:20.375467700+08:00" level=error msg="MLY | RU-俄罗斯 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:57:20.375467700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:57:29.549080900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6433613932316138 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:57:29.549080900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:57:47.231973600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6339636361653237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:57:47.231973600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:57:51.493847700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3232353230663863 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:57:51.493847700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:58:08.831171400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-23T21:58:15.026902300+08:00" level=error msg="CC | 广港专线转香港BGP3[M][倍率:1.7] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:58:15.026902300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:58:21.033691900+08:00" level=error msg="MLY | HK-香港 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T21:58:21.033691900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T21:58:35.947150400+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T21:59:00.924816200+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T21:59:00.959298600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T21:59:01.969422800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T21:59:56.277308000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T21:59:56.345433900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:00:55.947895500+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:00:56.310805300+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:01:01.331298700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:01:01.415410900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:01:02.274690800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:01:04.115040300+08:00" level=error msg="Free-2 | 🇨🇦CA_3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:01:04.115040300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:01:07.060167900+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💼_github.com/Ruk1ng001_6136633239376231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:01:07.060167900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:01:15.123427000+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:01:15.123427000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:01:20.894086400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6231366465653033 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:01:20.894086400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:01:21.169060500+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-23T22:02:31.906607700+08:00" level=error msg="CC | 广东移动转香港HKT4[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:02:31.906607700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:02:56.327979800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:02:56.349500000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:02:56.580516400+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:03:01.725829900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:03:01.734565100+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:03:02.776389600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:03:15.948530600+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:04:30.861558800+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-07-23T22:05:02.195314200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:05:03.149652000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:05:04.136328500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:05:21.772714000+08:00" level=error msg="CC | 上海联通转美国Cera5[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:05:21.772714000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:05:35.948918400+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:05:56.582745700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:05:56.638454500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:05:56.763225100+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:05:56.815263800+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-07-23T22:05:57.071049700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:05:59.897895500+08:00" level=error msg="🚀 Landing-Auto-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:05:59.897895500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:00.898951600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6139646430646237 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:00.898951600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:03.897640200+08:00" level=error msg="CC CordCloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:03.897640200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:13.969481600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3563643661393839 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:13.969481600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:16.817167400+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:16.817167400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:20.909888200+08:00" level=error msg="CC | 广州移动转香港HKC2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:20.909888200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:22.570530800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6231366465653033 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:22.570530800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:06:33.076012500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-23T22:06:50.551335700+08:00" level=error msg="Free-6 | ❓_🇦🇺_💻_github.com/Ruk1ng001_6339643963623836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:06:50.551335700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:07:02.460068800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:07:03.592596700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:07:04.235384700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:07:55.949259300+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:08:56.347672600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:08:56.498212900+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:08:56.775879600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:08:59.897106000+08:00" level=error msg="🏠 Landing-Home-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:08:59.897106000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:09:02.579467700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:09:03.979459600+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:09:04.496491100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:10:15.950021900+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:10:56.498070600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:11:00.899192900+08:00" level=error msg="FL | 俄罗斯 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:00.899192900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:00.899192900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3439626230313037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:00.899758800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:03.189947100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:11:04.683077100+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:11:05.024499300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:11:07.146465100+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💼_github.com/Ruk1ng001_6136633239376231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:07.146465100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:08.868011800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6363346662326630 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:08.868011800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:08.940503800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.1ga4cs.ccddn4.icu:65401 connect error: context canceled"
time="2025-07-23T22:11:15.988719800+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:15.988719800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:30.025050400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3731623864613462 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:30.025050400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:47.678121900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6339623532643630 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:47.678121900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:11:49.726876100+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-23T22:11:56.124562400+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-07-23T22:11:56.452262500+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:11:56.615963300+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:11:59.897671700+08:00" level=error msg="CT1 CreaTivity-1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:11:59.897671700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:12:00.634485300+08:00" level=error msg="Free-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:12:00.634485300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:12:13.585713700+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-07-23T22:12:35.951063000+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:13:03.557690800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:13:05.073002000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:13:05.395313300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:14:13.783935800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> gist.githubusercontent.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-23T22:14:13.783935800+08:00" level=error msg="[Provider] free-2 pull error: Get \"https://gist.githubusercontent.com/byrisk/b8954fed7476b150ccb71e41e9ed1f1e/raw/MQNODES.yaml\": EOF"
time="2025-07-23T22:14:45.951765700+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": net/http: TLS handshake timeout"
time="2025-07-23T22:14:56.459061000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:14:56.762670000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:14:59.897971800+08:00" level=error msg="CC CordCloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:14:59.897971800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:14:59.900325700+08:00" level=error msg="CC CordCloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:14:59.900325700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:15:00.447158900+08:00" level=error msg="CC CordCloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:15:00.447158900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:15:04.149392300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:15:05.615723000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:15:05.764657600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:16:03.695008500+08:00" level=error msg="MLY | SG - 新加坡 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:03.695008500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:04.157452500+08:00" level=error msg="MLY | SG - 新加坡 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:04.157452500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:16.232525300+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:16.232525300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:22.373686000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6231366465653033 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:22.373686000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:50.221084100+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_6435396165323635 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:50.221084100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:50.591300700+08:00" level=error msg="Free-6 | ❓_🇦🇺_💻_github.com/Ruk1ng001_6339643963623836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-23T22:16:50.591300700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-23T22:16:54.621810500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-23T22:17:04.752198400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-23T22:17:05.952687200+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-23T22:17:05.997474800+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-23T22:17:06.134369500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-23T22:17:56.529794800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:17:56.749765600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-23T22:18:30.640028200+08:00" level=warning msg="Mihomo shutting down"
