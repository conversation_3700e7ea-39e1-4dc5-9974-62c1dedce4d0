Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-24T21:28:07.485264200+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-24T21:28:07.490884400+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-06-24T21:28:07.490884400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-24T21:28:07.491393700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-24T21:28:07.495019100+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-06-24T21:28:07.495019100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-24T21:28:07.688156300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119081"
time="2025-06-24T21:28:07.691145800+08:00" level=info msg="Initial configuration complete, total time: 202ms"
time="2025-06-24T21:28:07.693139200+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-06-24T21:28:07.693139200+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-06-24T21:28:07.693139200+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-24T21:28:07.706880700+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-06-24T21:28:08.143870500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-24T21:28:08.147174700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-24T21:28:08.187123500+08:00" level=warning msg="[Provider] telegramcidr not updated for a long time, force refresh"
time="2025-06-24T21:28:08.188350400+08:00" level=warning msg="[Provider] claude not updated for a long time, force refresh"
time="2025-06-24T21:28:08.193413900+08:00" level=warning msg="[Provider] github not updated for a long time, force refresh"
time="2025-06-24T21:28:08.193413900+08:00" level=warning msg="[Provider] icloud not updated for a long time, force refresh"
time="2025-06-24T21:28:08.193923300+08:00" level=warning msg="[Provider] private not updated for a long time, force refresh"
time="2025-06-24T21:28:08.195541900+08:00" level=warning msg="[Provider] google not updated for a long time, force refresh"
time="2025-06-24T21:28:08.195541900+08:00" level=warning msg="[Provider] apple not updated for a long time, force refresh"
time="2025-06-24T21:28:08.198720300+08:00" level=warning msg="[Provider] openai not updated for a long time, force refresh"
time="2025-06-24T21:28:08.199222400+08:00" level=warning msg="[Provider] copilot not updated for a long time, force refresh"
time="2025-06-24T21:28:08.202765700+08:00" level=warning msg="[Provider] google-rules not updated for a long time, force refresh"
time="2025-06-24T21:28:08.203789200+08:00" level=warning msg="[Provider] microsoft not updated for a long time, force refresh"
time="2025-06-24T21:28:08.204823100+08:00" level=warning msg="[Provider] tld-not-cn not updated for a long time, force refresh"
time="2025-06-24T21:28:08.261478900+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-06-24T21:28:08.275935800+08:00" level=warning msg="[Provider] gfw not updated for a long time, force refresh"
time="2025-06-24T21:28:08.366516400+08:00" level=warning msg="[Provider] cncidr not updated for a long time, force refresh"
time="2025-06-24T21:28:08.478953000+08:00" level=warning msg="[Provider] advertising not updated for a long time, force refresh"
time="2025-06-24T21:28:08.501420400+08:00" level=warning msg="[Provider] proxy not updated for a long time, force refresh"
time="2025-06-24T21:28:09.087163400+08:00" level=warning msg="[Provider] direct not updated for a long time, force refresh"
time="2025-06-24T21:28:09.180160700+08:00" level=warning msg="[Provider] reject not updated for a long time, force refresh"
time="2025-06-24T21:28:13.147317100+08:00" level=error msg="PKM1 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:13.147317100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:15.375082200+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-24T21:28:15.384191400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-24T21:28:15.384191400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-24T21:28:16.601140200+08:00" level=error msg="CC2 | 上海移动转日本NTT[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:16.601140200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:20.173846900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/tld-not-cn) **********:62411(msedgewebview2.exe) --> api.ipapi.is:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-06-24T21:28:20.385728300+08:00" level=error msg="PKM1 | 套餐到期：长期有效 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:20.385728300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:20.385728300+08:00" level=error msg="PKM1 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:20.385728300+08:00" level=error msg="PKM1 | 剩余流量：194.35 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:20.385728300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:20.385728300+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:20.385728300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:20.385728300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.105483300+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3162376666396133 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:26.105483300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.322916100+08:00" level=error msg="CC2 | 广州移动转台湾KBT[M][Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323427800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.323427800+08:00" level=error msg="CC2 | 广州移动转日本TE[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323427800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.323427800+08:00" level=error msg="PKM1 | 🇺🇸【北美洲】美国06丨超速【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323427800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.323427800+08:00" level=error msg="CC1 | 广东移动转香港AWS[Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323427800+08:00" level=error msg="CC2 | 广东移动转美国Cera[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323936300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.323936300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.323936300+08:00" level=error msg="CC1 | 广州移动转美国AN[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.323936300+08:00" level=error msg="CC1 | 广东移动转香港NTT4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-06-24T21:28:26.324477400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:26.324477400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:29.452451700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6636346264353037 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:29.452451700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:39.909439100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6662386666323464 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:39.909439100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:44.003643300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:49182(msedge.exe) --> static.edge.microsoftapp.net:443 error: **************:5067 connect error: shct.1kgbbf2mga.ccddn4.icu:65301 connect error: context deadline exceeded"
time="2025-06-24T21:28:56.987264700+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3538346564343739 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:56.987264700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:57.163718200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6234663362633162 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:57.163718200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:58.160643900+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3530346166303035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:58.160643900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:28:59.498767600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:51176(SGDownload.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:28:59.523946000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:51181(SGTool.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:28:59.657961500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3235653865393761 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:28:59.657961500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:29:00.832073500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3437393937353032 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:29:00.832073500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:29:04.521073200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:29:04.521073200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:29:09.176444600+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_39623738303231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:29:09.176444600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:29:39.187022700+08:00" level=error msg="CC2 | 广东移动转美国Cera[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:29:39.187022700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
