2025-07-26 14:32:30 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-07-26 14:32:30 INFO - [Setup] 初始化资源...
2025-07-26 14:32:30 INFO - [Setup] 初始化完成，继续执行
2025-07-26 14:32:30 INFO - [System] 应用就绪或恢复
2025-07-26 14:32:30 INFO - [Setup] 检测到已有应用实例运行
2025-07-26 14:32:30 INFO - [System] 开始执行清理操作...
2025-07-26 14:32:30 INFO - [System] 开始执行异步清理操作...
2025-07-26 14:32:30 INFO - 核心服务已停止
2025-07-26 14:32:30 INFO - [Config] 生成运行时配置成功
2025-07-26 14:32:30 INFO - [Config] 开始验证配置
2025-07-26 14:32:30 INFO - [Config] 生成临时配置文件用于验证
2025-07-26 14:32:30 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-07-26 14:32:30 INFO - [Config] 使用内核: verge-mihomo
2025-07-26 14:32:30 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-07-26 14:32:30 INFO - 系统代理已重置
2025-07-26 14:32:31 INFO - TUN模式已禁用
2025-07-26 14:32:31 INFO - [System] 异步清理操作完成 - TUN: true, 代理: true, 核心: true, DNS: true, 总体: true
2025-07-26 14:32:31 INFO - [System] 清理操作完成，结果: true
