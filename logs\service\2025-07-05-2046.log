Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-05T20:46:11.538331500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-05T20:46:11.544771900+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-05T20:46:11.544771900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-05T20:46:11.545281800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-05T20:46:11.549902800+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7481"
time="2025-07-05T20:46:11.549902800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-05T20:46:11.787245500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119321"
time="2025-07-05T20:46:11.804093600+08:00" level=info msg="Initial configuration complete, total time: 262ms"
time="2025-07-05T20:46:11.809500800+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-05T20:46:11.809500800+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-05T20:46:11.809500800+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-05T20:46:11.843591300+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-05T20:46:12.382167700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-05T20:46:12.389413600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-05T20:46:12.395099800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-05T20:46:12.429640900+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-05T20:46:14.377464600+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-05T20:46:17.385196600+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-05T20:46:17.385196600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-05T20:46:17.430013200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-05T20:46:17.430013200+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-05T20:46:18.487806600+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-05T20:46:18.821270000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:18.821270000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:20.674653900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:21.670132200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:22.430490300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-05T20:46:22.430490300+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-05T20:46:23.898990200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:23.898990200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-05T20:46:26.402749900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
