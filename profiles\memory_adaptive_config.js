// 内存自适应配置模块
// 根据系统内存自动调整 Clash 配置参数

/**
 * 检测系统内存并返回优化配置
 * @returns {Object} 优化配置对象
 */
function getMemoryAdaptiveConfig() {
  // 模拟内存检测（实际使用时可以通过系统API获取）
  const totalMemoryGB = getTotalMemoryGB();
  
  if (totalMemoryGB >= 64) {
    return getHighMemoryConfig();
  } else if (totalMemoryGB >= 32) {
    return getMediumMemoryConfig();
  } else if (totalMemoryGB >= 16) {
    return getLowMemoryConfig();
  } else {
    return getMinimalMemoryConfig();
  }
}

/**
 * 获取系统总内存（GB）
 * 注意：这是一个示例函数，实际实现需要根据平台调用相应API
 */
function getTotalMemoryGB() {
  // 在实际环境中，可以通过以下方式获取：
  // Windows: wmic computersystem get TotalPhysicalMemory
  // Linux: cat /proc/meminfo | grep MemTotal
  // macOS: sysctl hw.memsize
  
  // 这里返回默认值，实际使用时需要实现真实的内存检测
  return 32; // 默认假设32GB
}

/**
 * 高内存配置 (64GB+)
 */
function getHighMemoryConfig() {
  return {
    dns: {
      "fake-ip-cache-size": 524288,     // 512K
      "concurrent": 8,
      "fallback-delay": 50,
      "cache-ttl": 7200
    },
    cache: {
      "profile-cache-size": 4096,       // 4GB
      "rule-cache-size": 2048,          // 2GB
      "geoip-cache-size": 1024,         // 1GB
      "dns-cache-size": 512,            // 512MB
      "memory-limit": 8192              // 8GB
    },
    connection: {
      "connection-pool-size": 64,
      "max-concurrent-streams": 128,
      "connection-idle-timeout": 300
    },
    proxy: {
      "interval": 120,                  // 2分钟
      "timeout": 3000,
      "max-failed-times": 5
    },
    tun: {
      "gso-max-size": 262144,           // 256KB
      "udp-timeout": 600,
      "stack-buffer-size": 1048576      // 1MB
    }
  };
}

/**
 * 中等内存配置 (32GB-64GB) - 当前使用的均衡配置
 */
function getMediumMemoryConfig() {
  return {
    dns: {
      "fake-ip-cache-size": 262144,     // 256K
      "concurrent": 6,
      "fallback-delay": 75,
      "cache-ttl": 5400
    },
    cache: {
      "profile-cache-size": 2048,       // 2GB
      "rule-cache-size": 1024,          // 1GB
      "geoip-cache-size": 512,          // 512MB
      "dns-cache-size": 256,            // 256MB
      "memory-limit": 4096              // 4GB
    },
    connection: {
      "connection-pool-size": 32,
      "max-concurrent-streams": 64,
      "connection-idle-timeout": 180
    },
    proxy: {
      "interval": 180,                  // 3分钟
      "timeout": 4000,
      "max-failed-times": 4
    },
    tun: {
      "gso-max-size": 196608,           // 192KB
      "udp-timeout": 450,
      "stack-buffer-size": 524288       // 512KB
    }
  };
}

/**
 * 低内存配置 (16GB-32GB)
 */
function getLowMemoryConfig() {
  return {
    dns: {
      "fake-ip-cache-size": 131072,     // 128K
      "concurrent": 4,
      "fallback-delay": 100,
      "cache-ttl": 3600
    },
    cache: {
      "profile-cache-size": 1024,       // 1GB
      "rule-cache-size": 512,           // 512MB
      "geoip-cache-size": 256,          // 256MB
      "dns-cache-size": 128,            // 128MB
      "memory-limit": 2048              // 2GB
    },
    connection: {
      "connection-pool-size": 16,
      "max-concurrent-streams": 32,
      "connection-idle-timeout": 120
    },
    proxy: {
      "interval": 300,                  // 5分钟
      "timeout": 5000,
      "max-failed-times": 3
    },
    tun: {
      "gso-max-size": 131072,           // 128KB
      "udp-timeout": 300,
      "stack-buffer-size": 262144       // 256KB
    }
  };
}

/**
 * 最小内存配置 (16GB以下)
 */
function getMinimalMemoryConfig() {
  return {
    dns: {
      "fake-ip-cache-size": 65536,      // 64K
      "concurrent": 3,
      "fallback-delay": 150,
      "cache-ttl": 1800
    },
    cache: {
      "profile-cache-size": 512,        // 512MB
      "rule-cache-size": 256,           // 256MB
      "geoip-cache-size": 128,          // 128MB
      "dns-cache-size": 64,             // 64MB
      "memory-limit": 1024              // 1GB
    },
    connection: {
      "connection-pool-size": 8,
      "max-concurrent-streams": 16,
      "connection-idle-timeout": 90
    },
    proxy: {
      "interval": 600,                  // 10分钟
      "timeout": 8000,
      "max-failed-times": 2
    },
    tun: {
      "gso-max-size": 65536,            // 64KB
      "udp-timeout": 180,
      "stack-buffer-size": 131072       // 128KB
    }
  };
}

/**
 * 应用内存自适应配置到主配置
 * @param {Object} config 主配置对象
 * @returns {Object} 优化后的配置对象
 */
function applyMemoryAdaptiveConfig(config) {
  const adaptiveConfig = getMemoryAdaptiveConfig();
  
  // 应用DNS配置
  if (config.dns) {
    Object.assign(config.dns, adaptiveConfig.dns);
  }
  
  // 应用缓存配置
  if (config.cache) {
    Object.assign(config.cache, adaptiveConfig.cache);
  }
  
  // 应用连接配置
  if (config.connection) {
    Object.assign(config.connection, adaptiveConfig.connection);
  }
  
  // 应用TUN配置
  if (config.tun) {
    Object.assign(config.tun, adaptiveConfig.tun);
  }
  
  return config;
}

// 导出配置函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getMemoryAdaptiveConfig,
    applyMemoryAdaptiveConfig,
    getHighMemoryConfig,
    getMediumMemoryConfig,
    getLowMemoryConfig,
    getMinimalMemoryConfig
  };
}

// 使用示例：
// const adaptiveConfig = getMemoryAdaptiveConfig();
// console.log('当前内存优化配置:', adaptiveConfig);
