# Windows 网络优化脚本
# 配合 Clash Script.js 使用，提升整体网络性能
# 需要管理员权限运行

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "此脚本需要管理员权限运行！" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell 并选择 '以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "=== Windows 网络性能优化脚本 ===" -ForegroundColor Green
Write-Host "配合 Clash Script.js 使用" -ForegroundColor Cyan
Write-Host ""

# 1. TCP 自动调优
Write-Host "1. 优化 TCP 自动调优..." -ForegroundColor Yellow
try {
    netsh int tcp set global autotuninglevel=normal
    Write-Host "   ✅ TCP 自动调优已设置为 normal" -ForegroundColor Green
} catch {
    Write-Host "   ❌ TCP 自动调优设置失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 启用 TCP Chimney Offload
Write-Host "2. 启用 TCP Chimney Offload..." -ForegroundColor Yellow
try {
    netsh int tcp set global chimney=enabled
    Write-Host "   ✅ TCP Chimney Offload 已启用" -ForegroundColor Green
} catch {
    Write-Host "   ❌ TCP Chimney Offload 启用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 启用 RSS (Receive Side Scaling)
Write-Host "3. 启用 RSS (Receive Side Scaling)..." -ForegroundColor Yellow
try {
    netsh int tcp set global rss=enabled
    Write-Host "   ✅ RSS 已启用" -ForegroundColor Green
} catch {
    Write-Host "   ❌ RSS 启用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 优化 TCP 窗口缩放
Write-Host "4. 优化 TCP 窗口缩放..." -ForegroundColor Yellow
try {
    netsh int tcp set global netdma=enabled
    Write-Host "   ✅ NetDMA 已启用" -ForegroundColor Green
} catch {
    Write-Host "   ❌ NetDMA 启用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 优化 DNS 缓存
Write-Host "5. 优化 DNS 缓存设置..." -ForegroundColor Yellow
try {
    # 增加 DNS 缓存大小
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" -Name "CacheHashTableBucketSize" -Value 1 -Type DWord -Force
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" -Name "CacheHashTableSize" -Value 384 -Type DWord -Force
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" -Name "MaxCacheEntryTtlLimit" -Value 86400 -Type DWord -Force
    Write-Host "   ✅ DNS 缓存已优化" -ForegroundColor Green
} catch {
    Write-Host "   ❌ DNS 缓存优化失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 优化网络适配器设置
Write-Host "6. 优化网络适配器设置..." -ForegroundColor Yellow
try {
    # 禁用 TCP/IP 卸载功能（可能与某些代理冲突）
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
    foreach ($adapter in $adapters) {
        try {
            # 启用大发送卸载
            Set-NetAdapterLso -Name $adapter.Name -V1IPv4Enabled $true -IPv4Enabled $true -IPv6Enabled $true -ErrorAction SilentlyContinue
            # 启用接收端缩放
            Set-NetAdapterRss -Name $adapter.Name -Enabled $true -ErrorAction SilentlyContinue
            Write-Host "   ✅ 已优化适配器: $($adapter.Name)" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️  适配器 $($adapter.Name) 优化部分失败" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "   ❌ 网络适配器优化失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 优化系统网络缓冲区
Write-Host "7. 优化系统网络缓冲区..." -ForegroundColor Yellow
try {
    # 增加系统网络缓冲区
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\AFD\Parameters" -Name "DefaultReceiveWindow" -Value 65536 -Type DWord -Force
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\AFD\Parameters" -Name "DefaultSendWindow" -Value 65536 -Type DWord -Force
    Write-Host "   ✅ 系统网络缓冲区已优化" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 系统网络缓冲区优化失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 清理 DNS 缓存
Write-Host "8. 清理 DNS 缓存..." -ForegroundColor Yellow
try {
    ipconfig /flushdns | Out-Null
    Write-Host "   ✅ DNS 缓存已清理" -ForegroundColor Green
} catch {
    Write-Host "   ❌ DNS 缓存清理失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 9. 重置网络栈（可选）
Write-Host "9. 是否重置网络栈？(建议在遇到网络问题时执行)" -ForegroundColor Yellow
$reset = Read-Host "输入 Y 重置网络栈，或按 Enter 跳过"
if ($reset -eq "Y" -or $reset -eq "y") {
    try {
        netsh winsock reset
        netsh int ip reset
        Write-Host "   ✅ 网络栈已重置（需要重启生效）" -ForegroundColor Green
        $needReboot = $true
    } catch {
        Write-Host "   ❌ 网络栈重置失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "   ⏭️  跳过网络栈重置" -ForegroundColor Cyan
}

# 10. 显示当前网络配置
Write-Host "10. 当前网络配置信息:" -ForegroundColor Yellow
try {
    Write-Host "   TCP 自动调优级别:" -NoNewline
    $autoTuning = netsh int tcp show global | Select-String "自动调优级别|Receive Window Auto-Tuning Level"
    Write-Host " $($autoTuning -replace '.*:\s*', '')" -ForegroundColor Cyan
    
    Write-Host "   TCP Chimney 状态:" -NoNewline
    $chimney = netsh int tcp show global | Select-String "Chimney 卸载状态|Chimney Offload State"
    Write-Host " $($chimney -replace '.*:\s*', '')" -ForegroundColor Cyan
    
    Write-Host "   RSS 状态:" -NoNewline
    $rss = netsh int tcp show global | Select-String "RSS 状态|Receive Side Scaling State"
    Write-Host " $($rss -replace '.*:\s*', '')" -ForegroundColor Cyan
} catch {
    Write-Host "   ⚠️  无法获取网络配置信息" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 优化完成 ===" -ForegroundColor Green
Write-Host "建议配合以下 Clash 配置使用:" -ForegroundColor Cyan
Write-Host "- MTU: 1400 (已在 Script.js 中设置)" -ForegroundColor White
Write-Host "- DNS 缓存: 131072 (已在 Script.js 中设置)" -ForegroundColor White
Write-Host "- TCP 优化: 已启用 (已在 Script.js 中设置)" -ForegroundColor White

if ($needReboot) {
    Write-Host ""
    Write-Host "⚠️  需要重启计算机以使所有更改生效" -ForegroundColor Yellow
    $reboot = Read-Host "是否现在重启？(Y/N)"
    if ($reboot -eq "Y" -or $reboot -eq "y") {
        Restart-Computer -Force
    }
}

Write-Host ""
Write-Host "优化脚本执行完成！" -ForegroundColor Green
pause
