Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-02T13:06:16.239712100+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-02T13:06:16.242762500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-08-02T13:06:16.242762500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-02T13:06:16.247844900+08:00" level=info msg="Initial configuration complete, total time: 5ms"
time="2025-08-02T13:06:16.249872600+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-08-02T13:06:16.249872600+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-08-02T13:06:16.249872600+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-08-02T13:06:16.413333500+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49608 --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:06:16.413333500+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49609 --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:06:22.567529300+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49628 --> mly1.543412546.xyz:443 error: connect failed: dial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\nconnect failed: dial tcp [2606:4700:3030::6815:3001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-08-02T13:06:27.151041500+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49648 --> bbb.flylink.cyou:443 error: connect failed: dial tcp 104.21.9.112:443: i/o timeout\ndial tcp 172.67.130.216:443: i/o timeout\nconnect failed: dial tcp [2606:4700:3030::6815:970]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-08-02T13:06:34.438755800+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49671 --> ssr.cnring.shop:80 error: connect failed: dial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\nconnect failed: dial tcp [2606:4700:3030::6815:7001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: A socket operation was attempted to an unreachable network."
time="2025-08-02T13:06:41.287075200+08:00" level=warning msg="[TCP] dial DIRECT 127.0.0.1:49687 --> sub.lbb886.nyc.mn:443 error: connect failed: dial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\nconnect failed: dial tcp [2606:4700:3030::6815:4001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: A socket operation was attempted to an unreachable network.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: A socket operation was attempted to an unreachable network."
time="2025-08-02T13:11:41.745067500+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-08-02T13:11:41.745067500+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-08-02T13:11:41.780153300+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-02T13:11:42.389885200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T13:11:42.391400100+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-02T13:11:42.393440300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T13:11:42.393440300+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-02T13:11:43.887136200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:11:43.887136200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:11:43.887705000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:11:43.887705000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:11:44.066802500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:11:44.695768500+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-02T13:11:45.516578900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:11:45.764899000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: **************:5067 connect error: dial tcp **************:5067: operation was canceled"
time="2025-08-02T13:11:45.764899000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-02T13:11:45.764899000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: context canceled"
time="2025-08-02T13:11:45.764899000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T13:11:45.764899000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T13:11:45.803136900+08:00" level=error msg="[Provider] free-5 pull error: Get \"https://alice.dns-dynamic.net/linuxdo-61adfb02-60cc-4b62-8390-cd95b91e70d1/config.yaml\": EOF"
time="2025-08-02T13:11:47.937343000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T13:11:47.938410000+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-02T13:11:47.943209900+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-02T13:11:47.943726900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T13:11:49.859147600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:11:49.968770700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:11:49.968770700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:11:49.968770700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:11:49.968770700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:11:50.155185000+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.878011100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.899628800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.899628800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:50.899628800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:50.899628800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=error msg="CC | 上海联通转日本NTT11[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=error msg="CC | 上海联通转日本BGP6[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T13:11:50.999515200+08:00" level=error msg="CT1 | 🇹🇷 土耳其-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T13:11:50.999515200+08:00" level=error msg="CC | 上海联通转日本BGP4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=error msg="PKM1 | 🇫🇷【欧洲】法国丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-08-02T13:11:50.999515200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T13:11:50.999515200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T13:11:50.999515200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T13:11:51.226192600+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T13:11:54.859549200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T13:11:54.859549200+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:11:56.456745400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:56.456745400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.063986800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59730(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.063986800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59726(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.063986800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59731(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.063986800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59727(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.063986800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59732(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.064994200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59725(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.064994200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59729(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.064994200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59728(clash-verge.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.081588900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59762(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.119245200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59793(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.184938600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.185477400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T13:11:57.185982600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T13:11:57.300687300+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:59826(clash-verge.exe) --> github.com:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-02T13:11:57.305913600+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:59828(msedgewebview2.exe) --> github.com:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-02T13:11:58.224043700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59943(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:12:00.348463700+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:60086(clash-verge.exe) --> github.com:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-02T13:12:02.085539800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60200(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:12:02.122509700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60206(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: dial tcp [2606:4700::6810:aee2]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700::6810:afe2]:443: connectex: The requested address is not valid in its context.\ndial tcp **********:443: i/o timeout"
time="2025-08-02T13:12:02.309022400+08:00" level=warning msg="[TCP] dial 📱 GitHub (match RuleSet/github) **********:60231(msedgewebview2.exe) --> github.com:443 error: dial tcp **************:443: i/o timeout"
time="2025-08-02T13:12:17.515335900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:17.515335900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:18.223846800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:18.223846800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:18.223846800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:18.223846800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:18.223846800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T13:12:29.969733900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:12:29.969733900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:12:31.999054600+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T13:12:37.134371600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:13:49.970613800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:13:49.970613800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:13:52.997091000+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T13:13:58.119810000+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:16:29.971122200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:16:29.971681200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:16:33.815930700+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T13:16:39.300603900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:21:48.106351800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T13:21:49.972190800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T13:21:49.972190800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T13:21:54.445723100+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T13:21:59.642551300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T13:22:15.925492400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T13:22:26.467460100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
