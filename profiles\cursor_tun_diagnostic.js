// Cursor TUN 模式诊断脚本
// 用于诊断和优化 Cursor 在 TUN 模式下的连接问题

const cursorDiagnostic = {
  // Cursor 相关域名列表（用于测试）
  domains: [
    "cursor.sh",
    "cursor.com", 
    "api.cursor.sh",
    "api.cursor.com",
    "auth.cursor.sh",
    "auth.cursor.com",
    "marketplace.cursorapi.com",
    "cursor-cdn.com",
    "elevenlabs.io",
    "download.todesktop.com"
  ],

  // TUN 模式优化建议
  tunOptimizations: {
    // DNS 优化
    dns: {
      "use-system-hosts": true,
      "fake-ip-cache-size": 65536,
      "prefer-h3": false,
      "respect-rules": true
    },

    // 代理组优化
    proxyGroup: {
      "timeout": 5000,
      "interval": 300,
      "tolerance": 100,
      "max-failed-times": 3,
      "disable-udp": false
    },

    // 规则优先级
    rulesPriority: [
      "DOMAIN-SUFFIX,cursor.sh,💻 Cursor",
      "DOMAIN-SUFFIX,cursor.com,💻 Cursor",
      "DOMAIN-SUFFIX,api.cursor.sh,💻 Cursor",
      "DOMAIN-SUFFIX,api.cursor.com,💻 Cursor",
      "DOMAIN-SUFFIX,auth.cursor.sh,💻 Cursor",
      "DOMAIN-SUFFIX,auth.cursor.com,💻 Cursor"
    ]
  },

  // 推荐的代理节点顺序（基于 AI 服务优化）
  recommendedProxyOrder: [
    "🇸🇬 新加坡",      // 首选：对AI服务友好，延迟适中
    "🇺🇸 美国",        // 次选：Cursor服务器可能在美国
    "🇭🇰 香港",        // 备选：延迟低，稳定性好
    "🎯 落地节点",      // 备选：专用节点
    "⚡ 深港专线",      // 备选：高速专线
    "🏠 家宽"          // 备选：家宽稳定
  ],

  // 故障排除步骤
  troubleshootingSteps: [
    "1. 检查 TUN 模式是否正常启动",
    "2. 验证 DNS 劫持是否生效",
    "3. 测试代理节点连通性",
    "4. 检查防火墙和安全软件",
    "5. 验证路由表配置",
    "6. 测试不同代理节点",
    "7. 检查 Clash 日志错误",
    "8. 重启网络适配器"
  ],

  // 常见错误和解决方案
  commonIssues: {
    "Connection failed": {
      causes: [
        "代理节点不稳定",
        "DNS 解析失败", 
        "网络路由冲突",
        "防火墙阻拦"
      ],
      solutions: [
        "切换到稳定的代理节点",
        "检查 DNS 配置",
        "重启 TUN 模式",
        "检查防火墙设置"
      ]
    },
    "Timeout": {
      causes: [
        "代理延迟过高",
        "网络拥塞",
        "节点负载过高"
      ],
      solutions: [
        "增加超时时间",
        "选择延迟更低的节点",
        "使用负载均衡"
      ]
    }
  }
};

// 导出配置
module.exports = cursorDiagnostic;
