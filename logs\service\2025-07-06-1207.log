Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-06T12:07:54.349743900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-06T12:07:54.355882300+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-06T12:07:54.355882300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-06T12:07:54.355882300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-06T12:07:54.359438000+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7481"
time="2025-07-06T12:07:54.359945200+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-06T12:07:54.558745400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119321"
time="2025-07-06T12:07:54.561718000+08:00" level=info msg="Initial configuration complete, total time: 208ms"
time="2025-07-06T12:07:54.563642700+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-06T12:07:54.563642700+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-06T12:07:54.563642700+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-06T12:07:54.575050200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-06T12:07:55.167672400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:07:55.168720600+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.170995300+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.174596600+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-06T12:07:55.174596600+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.175974900+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.178402500+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.178402500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:07:55.182940300+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-07-06T12:07:55.189609600+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-07-06T12:07:55.229222000+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-06T12:07:57.163396900+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:07:57.945014700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:00.175327900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: dial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-06T12:08:00.175327900+08:00" level=error msg="MLY | 官网:http://12306.131.996h.cn/ failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.175327900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-06T12:08:00.175327900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.175327900+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.175327900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.176380300+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.176380300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.176380300+08:00" level=error msg="PKM2 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.176380300+08:00" level=error msg="PKM2 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.176380300+08:00" level=error msg="PKM2 | 套餐到期：2025-08-25 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.176380300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.176380300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.176380300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 套餐到期：长期有效 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 剩余流量：188.73 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.179411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.184019600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431346261336137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:00.184019600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:00.229550900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-06T12:08:00.230065400+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:08:00.472043500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:00.646144100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:04.057078600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:04.572642900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-06T12:08:04.574691500+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:04.582489400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:08:04.629679900+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-06T12:08:04.664920200+08:00" level=error msg="CC | 安徽联通转日本BGP[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.664920200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:04.664920200+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context canceled"
time="2025-07-06T12:08:04.664920200+08:00" level=error msg="CC | 江苏联通转日本BGP5[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.664920200+08:00" level=error msg="CC | 江苏联通转日本NTT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.664920200+08:00" level=error msg="CC | 江苏联通转日本BGP4[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.665444700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:04.665444700+08:00" level=error msg="CC | 广州移动转日本TE[M][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.665444700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:04.665444700+08:00" level=error msg="CC | 江苏联通转日本BGP2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-06T12:08:04.665444700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:04.665444700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:04.665444700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:05.230522500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout"
time="2025-07-06T12:08:06.581138800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:09.579850000+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:09.579850000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:09.629909000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-06T12:08:09.629909000+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:08:12.791446500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:12.791446500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:12.791446500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:12.791446500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:12.791446500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:08:14.630351900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout"
time="2025-07-06T12:08:14.630351900+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:08:17.639089900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:17.762493300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:17.893150300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:18.027819200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:18.156321300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:18.405039500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:18.759081900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:18.837384300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:18.837384300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:18.837384300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:18.837384300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:18.907034900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:19.286303300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:19.286303300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:20.019701100+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:20.105505700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:20.105505700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:20.105505700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:20.105505700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:20.386659500+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60407(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:20.921830300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60571(msedge.exe) --> exo.nel.measure.office.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-06T12:08:21.315153300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:21.315153300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:23.179652300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:23.179652300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:23.757955800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60465(nvcontainer.exe) --> international-gfe.download.nvidia.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:24.729044100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:08:24.729044100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:08:25.629753800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:25.629753800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: operation was canceled"
time="2025-07-06T12:08:25.743228200+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60563(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:25.831298300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60574(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:25.920721400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:25.920721400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:25.920721400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:25.920721400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:08:25.929096000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60584(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:25.929096000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60583(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.042069300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60602(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.042069300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60601(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.131930900+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60612(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.586038600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60649(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.586038600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60645(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.586038600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60644(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.586038600+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60647(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.587178300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60648(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.609192800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60660(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.609192800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60656(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.609800000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60659(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.609800000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60661(msedgewebview2.exe) --> img-s.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:26.609800000+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:60657(msedgewebview2.exe) --> assets.msn.cn:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:30.205016300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60751(nvcontainer.exe) --> prod.otel.kaizen.nvidia.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: i/o timeout"
time="2025-07-06T12:08:33.840364600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:33.840364600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:33.840364600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:33.840364600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.141833400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.141833400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.141833400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.157140400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.678291700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:41.678291700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:44.222147200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:44.222589900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:46.086260500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:46.086260500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp *************:65520: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-06T12:08:59.630640300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-06T12:08:59.630640300+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:09:15.668364200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:09:20.668498700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:09:20.668498700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:09:20.669027200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638613563616231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:09:20.669027200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:09:29.290266100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:09:29.290266100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:09:30.781440900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:09:30.781440900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:09:33.513212300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:09:33.513212300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:10:10.095112900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:10:23.082244800+08:00" level=error msg="CC | 安徽联通转日本TE4[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:10:23.082244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:10:24.631444900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-06T12:10:24.631444900+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:10:26.670812900+08:00" level=error msg="CC | 江苏联通转日本NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:10:26.670812900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:15.499084700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:11:20.499593300+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:20.499593300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:28.253064400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:28.253064400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:29.363394200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:29.363394200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:29.918502900+08:00" level=error msg="CC | 上海联通转香港HKT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:29.918502900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:32.031290900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:32.031290900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:11:38.812824400+08:00" level=error msg="Free-6 | 🔴_🇨🇳:🇭🇰_💻_github.com/Ruk1ng001_6461636266616537 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:11:38.812824400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:12:07.978448100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:12:12.978987100+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:12:12.978987100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:02.077383400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:13:06.067936200+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:13:09.575107000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638613563616231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:09.575107000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:09.577138100+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:09.577138100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:09.577138100+08:00" level=error msg="MLY | 无网更新订阅 | 禁止回国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:09.577138100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:09.631771500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout"
time="2025-07-06T12:13:09.631771500+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:13:11.136740200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:13:12.884659000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:13:16.369335400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:16.369335400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:18.175292300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:18.175292300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:21.106278400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:21.106278400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:43.023432000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3437646139323434 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:13:43.023432000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:13:56.768582900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:14:01.769030600+08:00" level=error msg="MLY | 距离下次重置剩余：25 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:01.769030600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:04.588368200+08:00" level=error msg="MLY | RU-俄罗斯 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:04.588368200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:06.769116400+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:06.769116400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.026665800+08:00" level=error msg="MLY | AE-阿联酋 - 阿布扎比 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.026665800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.031720300+08:00" level=error msg="MLY | AE-阿联酋 - 阿布扎比 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.031720300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.035738800+08:00" level=error msg="MLY | HK-香港 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.035738800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.322294200+08:00" level=error msg="MLY | HK-香港 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.322294200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.769531400+08:00" level=error msg="MLY | HK-香港 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.769531400+08:00" level=error msg="MLY | TW-台湾 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:11.769531400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:11.769531400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:12.215185300+08:00" level=error msg="MLY | TW-台湾 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:12.215185300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:14:12.851906000+08:00" level=error msg="MLY | TW-台湾 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:14:12.851906000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:01.818824400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:15:06.818886500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:15:06.818886500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:13.544609900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:15:13.544609900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:15.446238500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:15:15.446238500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:17.975904200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:15:17.975904200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:28.690834900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3563643661393839 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:15:28.690834900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:15:54.123020300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:17:01.709810700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:17:06.710247900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:17:06.710247900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:17:13.993015300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:17:13.993015300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:17:15.775256700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:17:15.775256700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:17:18.483233700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:17:18.483233700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:17:55.221473100+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | 距离下次重置剩余：25 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | 官网:http://12306.131.996h.cn/ failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | 套餐到期：2025-08-02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | tg群：https://t.me/mly_cloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | 剩余流量：1998.93 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=error msg="MLY | 无网更新订阅 | 禁止回国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:00.221834200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:02.654834900+08:00" level=error msg="MLY | -----VIP国外免流区----- failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:02.654834900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:02.810830400+08:00" level=error msg="MLY | RU-俄罗斯 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:02.810830400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:03.764856000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:18:06.059788100+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:18:09.574430600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:09.574430600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:10.222663800+08:00" level=error msg="MLY | HK-香港 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:10.222663800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:14.632444100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-06T12:18:14.632444100+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-06T12:18:18.342804000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-06T12:18:19.031248500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:19.031248500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:21.344370300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-06T12:18:22.059033000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:22.059033000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:33.137855100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3563643661393839 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:33.137855100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:18:44.443048300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6534656565633833 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:18:44.443048300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:00.938764500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-06T12:19:05.939122000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:05.939122000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:12.596486400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:12.596486400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:14.366055200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:14.366055200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:16.955066400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:16.955066400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:18.997384400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3431336264653233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:18.997384400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:37.718701200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6632356238633566 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T12:19:37.718701200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T12:19:54.801505400+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
