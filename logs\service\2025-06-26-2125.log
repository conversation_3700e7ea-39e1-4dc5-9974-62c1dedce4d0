Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-26T21:25:30.885721500+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-26T21:25:30.891893100+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-06-26T21:25:30.891893100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-26T21:25:30.892404300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-26T21:25:30.895960500+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-06-26T21:25:30.896467900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-26T21:25:31.098253400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119081"
time="2025-06-26T21:25:31.101755700+08:00" level=info msg="Initial configuration complete, total time: 212ms"
time="2025-06-26T21:25:31.102779300+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-06-26T21:25:31.102779300+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-06-26T21:25:31.102779300+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-06-26T21:25:31.114941600+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-06-26T21:25:31.686760100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-26T21:25:31.686760100+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.691859000+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.699390700+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.699390700+08:00" level=warning msg="[Provider] cordcloud-2 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.704392700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-06-26T21:25:31.704392700+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.704392700+08:00" level=warning msg="[Provider] cordcloud-1 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.711892800+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.726902500+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-06-26T21:25:31.764363000+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-06-26T21:25:31.772734100+08:00" level=warning msg="[Provider] lancidr not updated for a long time, force refresh"
time="2025-06-26T21:25:31.781977600+08:00" level=warning msg="[Provider] games-cn not updated for a long time, force refresh"
time="2025-06-26T21:25:31.783000800+08:00" level=warning msg="[Provider] onedrive not updated for a long time, force refresh"
time="2025-06-26T21:25:31.791342800+08:00" level=warning msg="[Provider] gemini not updated for a long time, force refresh"
time="2025-06-26T21:25:31.801860600+08:00" level=warning msg="[Provider] netflix not updated for a long time, force refresh"
time="2025-06-26T21:25:33.673272800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-06-26T21:25:33.673272800+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-06-26T21:25:34.301044000+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
