Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-02T02:43:00.789721200+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-02T02:43:00.796938900+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-02T02:43:00.796938900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-02T02:43:00.796938900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-02T02:43:00.801557300+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-02T02:43:00.801557300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-02T02:43:01.044321900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-08-02T02:43:01.051451700+08:00" level=info msg="Initial configuration complete, total time: 258ms"
time="2025-08-02T02:43:01.104558200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-02T02:43:01.866116200+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-02T02:43:02.043313500+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-08-02T02:43:02.046688200+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-02T02:43:02.047191300+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-02T02:43:02.047859600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T02:43:02.056010300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T02:43:02.057454500+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-02T02:43:02.058121000+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-02T02:43:02.102350000+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-02T02:43:03.350681000+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:43:03.466862800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T02:43:04.525580000+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-02T02:43:05.105048100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:05.105048100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:05.106048100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:43:05.106048100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:43:05.106048100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:05.106048100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:43:05.106048100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:05.106048100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:43:05.559449900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.559449900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: context canceled"
time="2025-08-02T02:43:05.559449900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-02T02:43:05.560743900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:05.877520300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T02:43:06.004499500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:43:06.248607400+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T02:43:06.470126700+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-02T02:43:06.523774000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:06.729570600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:06.953344700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:07.054550500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-02T02:43:07.054550500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T02:43:07.186640900+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:07.408033300+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:07.673458700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:07.916739200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:08.436889500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:64417(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.744117200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.745141700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:43:10.745141700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.745141700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:10.745141700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:43:11.297090200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:64440(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:43:11.312343300+08:00" level=warning msg="[TCP] dial 📲 电报消息 (match RuleSet/telegramcidr) **********:64489(Telegram.exe) --> *************:443 error: dial tcp *************:443: i/o timeout"
time="2025-08-02T02:43:11.312844700+08:00" level=warning msg="[TCP] dial 📲 电报消息 (match RuleSet/telegramcidr) **********:64490(Telegram.exe) --> *************:80 error: dial tcp *************:80: i/o timeout"
time="2025-08-02T02:43:11.782615000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:43:11.782615000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:43:11.782615000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:43:11.782615000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:43:11.877760700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-08-02T02:43:11.877760700+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T02:43:12.054577500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-02T02:43:12.054577500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T02:43:13.066089000+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T02:43:15.962962800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:65042(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:43:16.318325800+08:00" level=warning msg="[TCP] dial 📲 电报消息 (match RuleSet/telegramcidr) **********:65088(Telegram.exe) --> *************:80 error: dial tcp *************:80: i/o timeout"
time="2025-08-02T02:43:25.082715500+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:25.214725100+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:25.356934800+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:25.529354800+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:25.724656400+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:26.011693900+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:26.396120900+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:26.784768300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:26.901475000+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:28.034098100+08:00" level=warning msg="[TCP] dial 💻 Cursor (match DomainSuffix/cursor.sh) **********:49183(Cursor.exe) --> api2.cursor.sh:443 error: cn-sh.sasg4e.ccddn4.icu:65301 connect error: EOF"
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:31.790845800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:43:45.106225700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:45.106225700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:45.106225700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:43:45.106225700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:43:46.116157700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T02:43:46.346051700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:43:46.996797800+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-02T02:43:52.632972900+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-02T02:43:52.639666800+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-02T02:43:52.639666800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-02T02:43:52.640180100+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-02T02:43:52.643749900+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-02T02:43:52.643749900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-02T02:43:52.849613400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-08-02T02:43:52.855617500+08:00" level=info msg="Initial configuration complete, total time: 219ms"
time="2025-08-02T02:43:52.911590200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-02T02:43:53.406831900+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-02T02:43:53.539724300+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-02T02:43:53.540726200+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-08-02T02:43:53.540726200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T02:43:53.541948900+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-02T02:43:53.543941000+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-02T02:43:53.546865600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T02:43:53.546865600+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-02T02:43:53.592719600+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-02T02:43:55.281342700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:43:55.365048800+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-02T02:43:56.079186800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:56.079186800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:56.079186800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:43:56.079186800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:43:56.080188600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:56.080188600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:43:56.080188600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:43:56.080188600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: context canceled"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: context canceled"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:56.324482100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: context canceled"
time="2025-08-02T02:43:57.279188700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:43:57.336546300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T02:43:57.737940500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:57.737940500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:57.814380800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T02:43:57.956415700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:57.956415700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.187172500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.188544400+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.418107100+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.429001000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.544367800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:43:58.544367800+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T02:43:58.546436500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-08-02T02:43:58.546436500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T02:43:58.678116700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.693268800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:58.997063200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:59.041846000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:59.221233600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:59.343258700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:43:59.826677200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-08-02T02:43:59.826677200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T02:43:59.913703500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:00.022040800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:00.179873400+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:00.672065600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50553(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:00.851240600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:01.863830400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:44:01.863830400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:44:02.068910400+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.077792900+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:50572(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.305295600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.516646700+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.602377200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.649999200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.703663000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.736476300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:50647(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:44:02.783787600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:02.826198000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.006549200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.171255100+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.234761100+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> 104.16.248.249:443 error: dial tcp 104.16.248.249:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.286497900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.287010200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.287010200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T02:44:03.291204000+08:00" level=warning msg="because PKM1 宝可梦-1 failed multiple times, active health check"
time="2025-08-02T02:44:03.291204000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T02:44:03.291204000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T02:44:03.370593900+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.544609000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:44:03.544609000+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T02:44:03.546678800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-08-02T02:44:03.546678800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T02:44:03.598075800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.914350800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.915855800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:03.983760400+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.026806200+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.141377500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.190228000+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.263174500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.375068800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.458160500+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.492790600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.822092600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51137(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:04.822893600+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:05.136765900+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51220(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:05.227193600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:44:05.227193600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T02:44:05.562554100+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:05.829720800+08:00" level=warning msg="[TCP] dial 🚀 Augment (match DomainKeyword/augment) **********:51343(Code.exe) --> d16.api.augmentcode.com:443 error: shct.7sj0r65w0g.ccddn4.icu:65300 connect error: EOF"
time="2025-08-02T02:44:07.784661300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:51248(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T02:44:22.892682400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:44:23.958828900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T02:44:36.080633100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:44:36.080633100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T02:44:36.080633100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T02:44:36.080633100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T02:44:37.577351700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T02:44:37.971324200+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
