2025-07-16 19:40:00 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-07-16 19:40:00 INFO - [Setup] 初始化资源...
2025-07-16 19:40:00 INFO - [Setup] 初始化完成，继续执行
2025-07-16 19:40:00 INFO - [System] 应用就绪或恢复
2025-07-16 19:40:00 INFO - [Config] 生成运行时配置成功
2025-07-16 19:40:00 INFO - [Config] 开始验证配置
2025-07-16 19:40:00 INFO - [Config] 生成临时配置文件用于验证
2025-07-16 19:40:00 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-07-16 19:40:00 INFO - [Config] 使用内核: verge-mihomo
2025-07-16 19:40:00 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-07-16 19:40:00 INFO - [Config] -------- 验证结果 --------
2025-07-16 19:40:00 INFO - [Config] 验证成功
2025-07-16 19:40:00 INFO - [Config] -------- 验证结束 --------
2025-07-16 19:40:00 INFO - [Config] 配置验证成功
2025-07-16 19:40:00 INFO - [Setup] 清理冗余的Profile文件...
2025-07-16 19:40:00 INFO - 已清理冗余文件: g7W9D2ZqQzIx.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: grClhp4ocRQR.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: mDTYOTF3aXQT.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: mXgedLbq6Vtc.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: p0S9aF1pz5Wz.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: pZlJxIUU6LBK.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: RhRXN3pk4oMn.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: rHwc3mK90Qod.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: rSmR9t7sJQCn.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: RVuFQe3SNSip.yaml
2025-07-16 19:40:00 INFO - 已清理冗余文件: sSRGQxdXZVh8.js
2025-07-16 19:40:00 INFO - 已清理冗余文件: sxR3mmdUi4zX.js
2025-07-16 19:40:00 INFO - Profile 文件清理完成: 总文件数=90, 删除文件数=12, 失败数=0
2025-07-16 19:40:00 INFO - 自动清理完成，删除了 12 个冗余文件
2025-07-16 19:40:00 INFO - [Setup] 启动时Profile文件清理完成
2025-07-16 19:40:00 INFO - [Service] 开始检查服务是否正在运行
2025-07-16 19:40:00 INFO - [Service] 开始检查服务状态 (IPC)
2025-07-16 19:40:00 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:00 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:00 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-07-16 19:40:00 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-07-16 19:40:00 INFO - [Service] 服务正在运行
2025-07-16 19:40:00 INFO - [Core] 服务当前可用或看似可用，尝试通过服务模式启动/重装
2025-07-16 19:40:00 INFO - [Service] 开始检查服务是否需要重装
2025-07-16 19:40:00 INFO - [Service] 开始检查服务版本 (IPC)
2025-07-16 19:40:00 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:00 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:00 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-07-16 19:40:00 INFO - [Service] 获取到服务版本: 1.1.0
2025-07-16 19:40:00 INFO - 服务版本检测：当前=1.1.0, 要求=1.1.0
2025-07-16 19:40:00 INFO - 服务版本匹配，无需重装
2025-07-16 19:40:00 INFO - 正在尝试通过服务启动核心
2025-07-16 19:40:00 INFO - [Service] 开始检查服务版本 (IPC)
2025-07-16 19:40:00 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:00 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:00 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-07-16 19:40:00 INFO - [Service] 获取到服务版本: 1.1.0
2025-07-16 19:40:00 INFO - 检测到服务版本: 1.1.0, 要求版本: 1.1.0
2025-07-16 19:40:00 INFO - 服务版本匹配
2025-07-16 19:40:00 INFO - [Service] 开始检查服务是否正在运行
2025-07-16 19:40:00 INFO - [Service] 开始检查服务状态 (IPC)
2025-07-16 19:40:00 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:00 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:00 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-07-16 19:40:00 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-07-16 19:40:00 INFO - [Service] 服务正在运行
2025-07-16 19:40:00 INFO - 服务已在运行且版本匹配，尝试使用
2025-07-16 19:40:00 INFO - 尝试使用现有服务启动核心 (IPC)
2025-07-16 19:40:00 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:00 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:01 INFO - [Service] IPC请求完成: 命令=StartClash, 成功=true
2025-07-16 19:40:01 INFO - [Service] 服务成功启动核心
2025-07-16 19:40:01 INFO - [Core] 服务模式成功启动核心
2025-07-16 19:40:01 INFO - [Tray] 创建系统托盘...
2025-07-16 19:40:01 INFO - 正在从AppHandle创建系统托盘
2025-07-16 19:40:01 INFO - 系统托盘创建成功
2025-07-16 19:40:01 INFO - [Tray] 系统托盘创建成功
2025-07-16 19:40:01 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-07-16 19:40:01 INFO - [Window] UI准备阶段更新: NotStarted, 耗时: 0ms
2025-07-16 19:40:01 INFO - [Timer] Initializing timer...
2025-07-16 19:40:01 INFO - [Timer] Refreshing 8 timer tasks
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RJRGh1X9IWCo, id=4, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=Rcvw1SCKmdqL, id=5, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=Rx9LL6kjRU3B, id=3, interval=360min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RqkaNnsWj6RG, id=8, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RFHm8frTGpzz, id=2, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RKzTK4d5rjIF, id=6, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RqUQUTUdf5hK, id=7, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] Adding task: uid=RO6hk7oF7wdt, id=1, interval=1440min
2025-07-16 19:40:01 INFO - [Timer] 已注册的定时任务数量: 8
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RKzTK4d5rjIF, interval=1440min, task_id=6
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RJRGh1X9IWCo, interval=1440min, task_id=4
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=Rcvw1SCKmdqL, interval=1440min, task_id=5
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RqUQUTUdf5hK, interval=1440min, task_id=7
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RO6hk7oF7wdt, interval=1440min, task_id=1
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=Rx9LL6kjRU3B, interval=360min, task_id=3
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RFHm8frTGpzz, interval=1440min, task_id=2
2025-07-16 19:40:01 INFO - [Timer] 注册了定时任务 - uid=RqkaNnsWj6RG, interval=1440min, task_id=8
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RKzTK4d5rjIF
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=Rcvw1SCKmdqL
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RqUQUTUdf5hK
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RqkaNnsWj6RG
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=Rx9LL6kjRU3B
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RFHm8frTGpzz
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RO6hk7oF7wdt
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置: uid=RJRGh1X9IWCo
2025-07-16 19:40:01 INFO - [Timer] 需要立即更新的配置数量: 8
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RKzTK4d5rjIF
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=Rcvw1SCKmdqL
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RqUQUTUdf5hK
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RqkaNnsWj6RG
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=Rx9LL6kjRU3B
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RFHm8frTGpzz
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RO6hk7oF7wdt
2025-07-16 19:40:01 INFO - [Timer] 立即执行任务: uid=RJRGh1X9IWCo
2025-07-16 19:40:01 INFO - [Timer] Timer initialization completed
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RJRGh1X9IWCo
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RqUQUTUdf5hK
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: Rcvw1SCKmdqL
2025-07-16 19:40:01 INFO - [Timer] 配置 Rcvw1SCKmdqL 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: Rx9LL6kjRU3B
2025-07-16 19:40:01 INFO - [Timer] 配置 Rx9LL6kjRU3B 是否为当前激活配置: true
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RO6hk7oF7wdt
2025-07-16 19:40:01 INFO - [Timer] 配置 RJRGh1X9IWCo 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RJRGh1X9IWCo
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 Rcvw1SCKmdqL
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RFHm8frTGpzz
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RqkaNnsWj6RG
2025-07-16 19:40:01 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [Timer] Running timer task for profile: RKzTK4d5rjIF
2025-07-16 19:40:01 INFO - [Timer] 配置 RKzTK4d5rjIF 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RKzTK4d5rjIF
2025-07-16 19:40:01 INFO - [订阅更新] RKzTK4d5rjIF 是远程订阅，URL: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [Timer] 配置 RO6hk7oF7wdt 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RO6hk7oF7wdt
2025-07-16 19:40:01 INFO - [订阅更新] RO6hk7oF7wdt 是远程订阅，URL: https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [Timer] 配置 RqUQUTUdf5hK 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-07-16 19:40:01 INFO - [订阅更新] RJRGh1X9IWCo 是远程订阅，URL: https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [订阅更新] Rcvw1SCKmdqL 是远程订阅，URL: https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [Timer] 配置 RFHm8frTGpzz 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RFHm8frTGpzz
2025-07-16 19:40:01 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [Timer] 配置 RqkaNnsWj6RG 是否为当前激活配置: false
2025-07-16 19:40:01 INFO - [订阅更新] RFHm8frTGpzz 是远程订阅，URL: http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae
2025-07-16 19:40:01 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-07-16 19:40:01 INFO - [订阅更新] 开始下载新的订阅内容
2025-07-16 19:40:01 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-07-16 19:40:01 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-07-16 19:40:01 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqkaNnsWj6RG
2025-07-16 19:40:01 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqUQUTUdf5hK
2025-07-16 19:40:02 INFO - [订阅更新] 更新订阅配置成功
2025-07-16 19:40:02 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-07-16 19:40:02 INFO - [Timer] Timer task completed successfully for uid: Rcvw1SCKmdqL (took 481ms)
2025-07-16 19:40:02 INFO - [Setup] 异步设置任务完成，耗时: 1.5304241s
2025-07-16 19:40:02 INFO - [Setup] 应用设置成功完成
2025-07-16 19:40:02 INFO - [Frontend] 发送2条启动时累积的错误消息
2025-07-16 19:40:02 INFO - [Window] 窗口已立即显示
2025-07-16 19:40:02 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-07-16 19:40:02 INFO - [Window] 窗口显示流程完成
2025-07-16 19:40:02 INFO - [订阅更新] 更新订阅配置成功
2025-07-16 19:40:02 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-07-16 19:40:02 INFO - [Timer] Timer task completed successfully for uid: RKzTK4d5rjIF (took 588ms)
2025-07-16 19:40:02 INFO - [Cmd] 快速获取配置列表成功
2025-07-16 19:40:02 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-07-16 19:40:02 INFO - [Network] 正在重置所有HTTP客户端
2025-07-16 19:40:02 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 ERROR - [[Timer]] Failed to update profile uid RqkaNnsWj6RG: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 ERROR - [[Timer]] Failed to update profile uid RqUQUTUdf5hK: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 INFO - UI加载阶段更新: Loading
2025-07-16 19:40:02 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-07-16 19:40:02 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 ERROR - [[Timer]] Failed to update profile uid RO6hk7oF7wdt: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:02 INFO - UI加载阶段更新: Loading
2025-07-16 19:40:02 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-07-16 19:40:02 INFO - UI加载阶段更新: DomReady
2025-07-16 19:40:02 INFO - [Window] UI准备阶段更新: DomReady, 耗时: 0ms
2025-07-16 19:40:02 INFO - UI加载阶段更新: ResourcesLoaded
2025-07-16 19:40:02 INFO - [Window] UI准备阶段更新: ResourcesLoaded, 耗时: 0ms
2025-07-16 19:40:02 INFO - [Service] 开始检查服务是否正在运行
2025-07-16 19:40:02 INFO - [Service] 开始检查服务状态 (IPC)
2025-07-16 19:40:02 INFO - [Service] 正在连接服务 (Windows)...
2025-07-16 19:40:02 INFO - [Service] 服务连接成功 (Windows)
2025-07-16 19:40:02 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-07-16 19:40:02 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-07-16 19:40:02 INFO - [Service] 服务正在运行
2025-07-16 19:40:02 INFO - 前端UI已准备就绪
2025-07-16 19:40:02 INFO - [Window] UI已标记为完全就绪
2025-07-16 19:40:02 INFO - [Window] UI已完全加载就绪
2025-07-16 19:40:02 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-07-16 19:40:02 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile with status 502 Bad Gateway
2025-07-16 19:40:02 ERROR - [[Timer]] Failed to update profile uid RFHm8frTGpzz: failed to fetch remote profile with status 502 Bad Gateway
2025-07-16 19:40:03 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-07-16 19:40:03 INFO - [Network] 正在重置所有HTTP客户端
2025-07-16 19:40:03 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:03 ERROR - [[Timer]] Failed to update profile uid Rx9LL6kjRU3B: failed to fetch remote profile: Failed to send HTTP request: error sending request
2025-07-16 19:40:04 INFO - [订阅更新] 更新订阅配置成功
2025-07-16 19:40:04 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-07-16 19:40:04 INFO - [Timer] Timer task completed successfully for uid: RJRGh1X9IWCo (took 2875ms)
2025-07-16 19:40:06 INFO - [Cmd] 快速获取配置列表成功
2025-07-16 19:40:06 ERROR - error sending request
2025-07-16 19:40:06 ERROR - error sending request
2025-07-16 19:43:54 ERROR - error sending request
2025-07-16 19:43:59 ERROR - error sending request
2025-07-16 19:43:59 ERROR - error sending request
2025-07-16 19:44:45 INFO - Tray点击事件: 显示主窗口
2025-07-16 19:44:45 INFO - [Window] 开始智能显示主窗口
2025-07-16 19:44:45 INFO - [Window] 开始激活窗口
2025-07-16 19:44:46 INFO - [Window] 窗口激活成功
2025-07-16 19:44:46 INFO - 窗口显示结果: Shown
2025-07-16 19:44:51 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-07-16 19:44:51 INFO - [Cmd] 快速获取配置列表成功
2025-07-16 19:45:37 ERROR - error sending request
2025-07-16 19:45:37 ERROR - error sending request
2025-07-16 19:45:47 ERROR - error sending request
2025-07-16 19:46:18 ERROR - error sending request
2025-07-16 19:46:28 ERROR - error sending request
2025-07-16 19:46:34 ERROR - error sending request
2025-07-16 19:46:34 ERROR - error sending request
2025-07-16 19:46:39 ERROR - error sending request
2025-07-16 19:46:39 ERROR - error sending request
2025-07-16 19:46:44 ERROR - error sending request
2025-07-16 19:47:31 ERROR - error sending request
2025-07-16 19:47:36 ERROR - error sending request
2025-07-16 19:47:51 ERROR - error sending request
2025-07-16 19:49:40 ERROR - error sending request
2025-07-16 19:49:51 ERROR - error sending request
2025-07-16 19:50:06 ERROR - error sending request
2025-07-16 19:50:12 ERROR - error sending request
2025-07-16 19:59:40 ERROR - error sending request
2025-07-16 20:04:59 ERROR - error sending request
2025-07-16 20:04:59 ERROR - error sending request
2025-07-16 20:05:04 ERROR - error sending request
2025-07-16 20:05:04 ERROR - error sending request
2025-07-16 20:05:09 ERROR - error sending request
2025-07-16 20:05:09 ERROR - error sending request
2025-07-16 20:05:14 ERROR - error sending request
2025-07-16 20:05:14 ERROR - error sending request
2025-07-16 20:05:19 ERROR - error sending request
2025-07-16 20:05:19 ERROR - error sending request
2025-07-16 20:05:25 ERROR - error sending request
2025-07-16 20:05:25 ERROR - error sending request
2025-07-16 20:05:30 ERROR - error sending request
2025-07-16 20:05:30 ERROR - error sending request
2025-07-16 20:05:35 ERROR - error sending request
2025-07-16 20:05:35 ERROR - error sending request
2025-07-16 20:05:40 ERROR - error sending request
2025-07-16 20:05:40 ERROR - error sending request
2025-07-16 20:05:46 ERROR - error sending request
2025-07-16 20:05:46 ERROR - error sending request
2025-07-16 20:05:51 ERROR - error sending request
2025-07-16 20:05:51 ERROR - error sending request
2025-07-16 20:05:56 ERROR - error sending request
2025-07-16 20:05:56 ERROR - error sending request
2025-07-16 20:06:01 ERROR - error sending request
2025-07-16 20:06:01 ERROR - error sending request
2025-07-16 20:06:07 ERROR - error sending request
2025-07-16 20:06:07 ERROR - error sending request
2025-07-16 20:06:12 ERROR - error sending request
2025-07-16 20:06:12 ERROR - error sending request
2025-07-16 20:06:17 ERROR - error sending request
2025-07-16 20:06:17 ERROR - error sending request
2025-07-16 20:06:22 ERROR - error sending request
2025-07-16 20:06:22 ERROR - error sending request
2025-07-16 20:06:28 ERROR - error sending request
2025-07-16 20:06:28 ERROR - error sending request
2025-07-16 20:06:33 ERROR - error sending request
2025-07-16 20:06:33 ERROR - error sending request
2025-07-16 20:06:38 ERROR - error sending request
2025-07-16 20:06:38 ERROR - error sending request
2025-07-16 20:06:43 ERROR - error sending request
2025-07-16 20:06:43 ERROR - error sending request
2025-07-16 20:06:49 ERROR - error sending request
2025-07-16 20:06:49 ERROR - error sending request
2025-07-16 20:06:54 ERROR - error sending request
2025-07-16 20:06:54 ERROR - error sending request
2025-07-16 20:06:59 ERROR - error sending request
2025-07-16 20:06:59 ERROR - error sending request
2025-07-16 20:07:04 ERROR - error sending request
2025-07-16 20:07:04 ERROR - error sending request
2025-07-16 20:07:09 ERROR - error sending request
2025-07-16 20:07:09 ERROR - error sending request
2025-07-16 20:07:15 ERROR - error sending request
2025-07-16 20:07:15 ERROR - error sending request
2025-07-16 20:07:20 ERROR - error sending request
2025-07-16 20:07:20 ERROR - error sending request
2025-07-16 20:07:25 ERROR - error sending request
2025-07-16 20:07:25 ERROR - error sending request
2025-07-16 20:07:30 ERROR - error sending request
2025-07-16 20:07:30 ERROR - error sending request
2025-07-16 20:07:36 ERROR - error sending request
2025-07-16 20:07:36 ERROR - error sending request
2025-07-16 20:07:41 ERROR - error sending request
2025-07-16 20:07:41 ERROR - error sending request
2025-07-16 20:07:46 ERROR - error sending request
2025-07-16 20:07:46 ERROR - error sending request
2025-07-16 20:07:51 ERROR - error sending request
2025-07-16 20:07:51 ERROR - error sending request
2025-07-16 20:07:57 ERROR - error sending request
2025-07-16 20:07:57 ERROR - error sending request
2025-07-16 20:08:02 ERROR - error sending request
2025-07-16 20:08:02 ERROR - error sending request
2025-07-16 20:08:07 ERROR - error sending request
2025-07-16 20:08:07 ERROR - error sending request
2025-07-16 20:09:37 ERROR - error sending request
2025-07-16 20:09:37 ERROR - error sending request
2025-07-16 20:18:15 ERROR - error sending request
2025-07-16 20:18:15 ERROR - error sending request
2025-07-16 20:18:15 ERROR - error sending request
2025-07-16 20:18:20 ERROR - error sending request
2025-07-16 20:18:20 ERROR - error sending request
2025-07-16 20:18:25 ERROR - error sending request
2025-07-16 20:18:25 ERROR - error sending request
2025-07-16 20:18:30 ERROR - error sending request
2025-07-16 20:18:30 ERROR - error sending request
2025-07-16 20:19:28 ERROR - error sending request
2025-07-16 20:19:44 ERROR - error sending request
2025-07-16 20:19:54 ERROR - error sending request
2025-07-16 20:24:06 ERROR - error sending request
2025-07-16 20:24:06 ERROR - error sending request
2025-07-16 20:24:17 ERROR - error sending request
2025-07-16 20:24:22 ERROR - error sending request
2025-07-16 20:24:33 ERROR - error sending request
2025-07-16 20:29:05 ERROR - error sending request
2025-07-16 20:34:55 ERROR - error sending request
2025-07-16 20:39:23 ERROR - error sending request
2025-07-16 20:39:33 ERROR - error sending request
2025-07-16 20:39:33 ERROR - error sending request
2025-07-16 20:39:59 ERROR - error sending request
2025-07-16 20:40:05 ERROR - error sending request
2025-07-16 20:41:23 ERROR - error sending request
2025-07-16 20:43:39 ERROR - error sending request
2025-07-16 20:53:52 ERROR - error sending request
2025-07-16 21:00:34 ERROR - error sending request
2025-07-16 21:04:57 ERROR - error sending request
2025-07-16 21:08:47 ERROR - error sending request
2025-07-16 21:08:47 ERROR - error sending request
2025-07-16 21:08:53 ERROR - error sending request
2025-07-16 21:08:53 ERROR - error sending request
2025-07-16 21:08:58 ERROR - error sending request
2025-07-16 21:08:58 ERROR - error sending request
2025-07-16 21:09:03 ERROR - error sending request
2025-07-16 21:09:03 ERROR - error sending request
2025-07-16 21:09:08 ERROR - error sending request
2025-07-16 21:09:08 ERROR - error sending request
2025-07-16 21:09:14 ERROR - error sending request
2025-07-16 21:09:14 ERROR - error sending request
2025-07-16 21:09:19 ERROR - error sending request
2025-07-16 21:09:24 ERROR - error sending request
2025-07-16 21:09:24 ERROR - error sending request
2025-07-16 21:09:29 ERROR - error sending request
2025-07-16 21:09:29 ERROR - error sending request
2025-07-16 21:09:35 ERROR - error sending request
2025-07-16 21:09:51 ERROR - error sending request
2025-07-16 21:09:51 ERROR - error sending request
2025-07-16 21:10:06 ERROR - error sending request
2025-07-16 21:10:22 ERROR - error sending request
2025-07-16 21:14:13 ERROR - error sending request
2025-07-16 21:14:44 ERROR - error sending request
2025-07-16 21:15:10 ERROR - error sending request
2025-07-16 21:15:21 ERROR - error sending request
2025-07-16 21:17:27 ERROR - error sending request
2025-07-16 21:31:43 ERROR - error sending request
2025-07-16 21:34:41 ERROR - error sending request
2025-07-16 21:34:56 ERROR - error sending request
2025-07-16 21:39:33 ERROR - error sending request
2025-07-16 21:39:53 ERROR - error sending request
2025-07-16 21:52:24 ERROR - error sending request
2025-07-16 22:09:21 ERROR - error sending request
2025-07-16 22:10:03 ERROR - error sending request
2025-07-16 22:10:13 ERROR - error sending request
2025-07-16 22:14:13 ERROR - error sending request
2025-07-16 22:14:18 ERROR - error sending request
2025-07-16 22:15:15 ERROR - error sending request
2025-07-16 22:20:03 ERROR - error sending request
2025-07-16 23:21:16 ERROR - error sending request
2025-07-16 23:23:21 ERROR - error sending request
2025-07-16 23:25:21 ERROR - error sending request
2025-07-16 23:29:47 ERROR - error sending request
2025-07-16 23:44:35 ERROR - error sending request
2025-07-16 23:44:35 ERROR - error sending request
2025-07-16 23:46:03 ERROR - error sending request
2025-07-16 23:48:24 ERROR - error sending request
2025-07-16 23:49:42 ERROR - error sending request
2025-07-16 23:49:42 ERROR - error sending request
2025-07-16 23:50:18 ERROR - error sending request
2025-07-16 23:50:18 ERROR - error sending request
2025-07-17 00:04:39 ERROR - error sending request
2025-07-17 00:09:10 ERROR - error sending request
2025-07-17 00:09:10 ERROR - error sending request
2025-07-17 00:09:25 ERROR - error sending request
2025-07-17 00:10:02 ERROR - error sending request
2025-07-17 00:10:07 ERROR - error sending request
2025-07-17 00:18:42 ERROR - error sending request
2025-07-17 00:24:43 ERROR - error sending request
2025-07-17 00:29:09 ERROR - error sending request
2025-07-17 00:29:45 ERROR - error sending request
2025-07-17 00:45:56 ERROR - error sending request
2025-07-17 00:52:43 ERROR - error sending request
2025-07-17 01:00:06 ERROR - error sending request
2025-07-17 01:01:09 ERROR - error sending request
2025-07-17 01:01:09 ERROR - error sending request
2025-07-17 01:01:19 ERROR - error sending request
2025-07-17 01:01:19 ERROR - error sending request
2025-07-17 01:01:24 ERROR - error sending request
2025-07-17 01:01:24 ERROR - error sending request
2025-07-17 01:01:30 ERROR - error sending request
2025-07-17 01:01:30 ERROR - error sending request
2025-07-17 01:01:35 ERROR - error sending request
2025-07-17 01:01:35 ERROR - error sending request
2025-07-17 01:01:45 ERROR - error sending request
2025-07-17 01:01:50 ERROR - error sending request
2025-07-17 01:01:50 ERROR - error sending request
2025-07-17 01:01:56 ERROR - error sending request
2025-07-17 01:01:56 ERROR - error sending request
2025-07-17 01:02:01 ERROR - error sending request
2025-07-17 01:04:27 ERROR - error sending request
2025-07-17 01:08:42 ERROR - error sending request
2025-07-17 01:19:05 ERROR - error sending request
2025-07-17 01:19:52 ERROR - error sending request
2025-07-17 01:24:54 ERROR - error sending request
2025-07-17 01:32:58 ERROR - error sending request
2025-07-17 01:32:58 ERROR - error sending request
2025-07-17 01:33:03 ERROR - error sending request
2025-07-17 01:33:03 ERROR - error sending request
2025-07-17 01:33:09 ERROR - error sending request
2025-07-17 01:33:09 ERROR - error sending request
2025-07-17 01:33:19 ERROR - error sending request
2025-07-17 01:33:24 ERROR - error sending request
2025-07-17 01:33:24 ERROR - error sending request
2025-07-17 01:33:29 ERROR - error sending request
2025-07-17 01:33:29 ERROR - error sending request
