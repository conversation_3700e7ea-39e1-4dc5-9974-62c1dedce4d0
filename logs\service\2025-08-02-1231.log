Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-02T12:32:01.415052800+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-02T12:32:01.421172300+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-02T12:32:01.421172300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-02T12:32:01.421680800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-02T12:32:01.425789800+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-02T12:32:01.426297800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-02T12:32:01.682243400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-08-02T12:32:01.687948200+08:00" level=info msg="Initial configuration complete, total time: 270ms"
time="2025-08-02T12:32:01.759328000+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-02T12:32:02.332869000+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-02T12:32:02.366798700+08:00" level=warning msg="[Provider] free-2 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.366798700+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.367317900+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.367317900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T12:32:02.367823900+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.367823900+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.370375400+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.370920300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T12:32:02.370920300+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.379213200+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-08-02T12:32:02.384986700+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-08-02T12:32:02.414016100+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-08-02T12:32:03.387277200+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:32:03.573642500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:32:04.006888600+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:32:04.138551300+08:00" level=error msg="initial proxy provider free-5 error: 404 Not Found"
time="2025-08-02T12:32:04.153516700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:04.153516700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:04.154026300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:32:04.154026300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:32:04.154536900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:04.154536900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:32:04.154536900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:04.154536900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:32:04.475045000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T12:32:04.604740100+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:32:05.013419900+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:32:05.063072800+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:32:05.116010600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> cloudflare-dns.com:443 error: context canceled"
time="2025-08-02T12:32:05.116010600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: context canceled"
time="2025-08-02T12:32:05.307383300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:32:05.333900800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-02T12:32:05.939736200+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-02T12:32:08.134663000+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.362567800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 Webshare-US专线-Direct failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🚄 Landing-Fast-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🔗 Landing-HK-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🚀 Landing-Auto-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🏠 Landing-Home-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🇺🇸 Landing-US-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:10.364244800+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:10.364756100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T12:32:11.203569000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T12:32:11.717781000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:12.152752500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:12.152752500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:12.152752500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-08-02T12:32:12.152752500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-08-02T12:32:12.152752500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-08-02T12:32:12.364100400+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:12.364100400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:12.364100400+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:12.364100400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:12.364100400+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:12.364100400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:12.364100400+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:12.364100400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:12.364100400+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:12.364100400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="because PKM1 宝可梦-1 failed multiple times, active health check"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:12.780079200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp **************:59010: operation was canceled"
time="2025-08-02T12:32:15.082708200+08:00" level=warning msg="because PKM1 宝可梦-1 failed multiple times, active health check"
time="2025-08-02T12:32:15.082708200+08:00" level=warning msg="because PKM1 宝可梦-1 failed multiple times, active health check"
time="2025-08-02T12:32:17.114522400+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3832646131376238 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:17.114522400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:29.268349500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431323961656438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:29.268349500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:32:31.386904600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:31.386904600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:31.386904600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:31.402972700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:31.402972700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:31.402972700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.245564400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.245564400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.245564400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.245564400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.245564400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.416606700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.416606700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.416606700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.416606700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.416606700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.432491600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:32.759661700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-08-02T12:32:44.154643600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:44.154643600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:32:44.154643600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:32:44.154643600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:32:45.368120200+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:32:45.389231200+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:32:45.662109000+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:32:46.097357300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:32:49.726943400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T12:32:55.341267700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6635363061343766 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:32:55.341267700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:33:07.448311100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3938356531643832 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:33:07.448311100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:33:15.427038700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6533343938633961 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:33:15.427038700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:34:04.155221100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:34:04.155221100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:34:04.155725500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:34:04.155725500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:34:05.713147700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:34:06.684651600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:34:06.855023700+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:34:10.663216600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-08-02T12:34:10.663216600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:34:11.423512500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T12:36:44.156433100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:36:44.156939700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:36:44.158533100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:36:44.158533100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:36:46.110479400+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:36:47.368599800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:36:47.493296300+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:36:52.596589100+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-08-02T12:36:55.664257900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-08-02T12:36:55.664763000+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:42:04.157062500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:42:04.157062500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:42:04.158619700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:42:04.158619700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:42:06.451059700+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:42:07.979003100+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:42:08.254370700+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:42:16.153689400+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-02T12:42:16.265189500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:47:02.519097600+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T12:47:02.645357900+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-08-02T12:47:05.808295700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T12:47:07.905635400+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T12:47:08.394483100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T12:47:30.719308900+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-08-02T12:47:30.719308900+08:00" level=warning msg="because CC CordCloud failed multiple times, active health check"
time="2025-08-02T12:48:07.046885700+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T12:48:35.141341300+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T12:49:17.232821600+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_32623732633836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:49:17.232821600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:49:43.443974200+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-08-02T12:49:59.689037600+08:00" level=error msg="CC | 上海电信转美国GS2[Trojan][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-08-02T12:49:59.689037600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-08-02T12:52:44.157915700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:52:44.157915700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-08-02T12:52:44.159120600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-02T12:52:44.159120600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-02T12:52:46.806096300+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-08-02T12:52:48.963912400+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-08-02T12:52:48.993838400+08:00" level=error msg="[Provider] free-5 pull error: 404 Not Found"
time="2025-08-02T12:52:56.987985100+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-08-02T12:53:02.597227600+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-08-02T13:02:10.929436600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T13:02:19.722140900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T13:02:19.722140900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-08-02T13:02:19.722140900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
