Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-07T20:03:12.195057800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-07T20:03:12.201686800+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-07T20:03:12.201686800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-07T20:03:12.202194900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-07T20:03:12.205898400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7481"
time="2025-07-07T20:03:12.206413200+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-07T20:03:12.457496000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119151"
time="2025-07-07T20:03:12.464774700+08:00" level=info msg="Initial configuration complete, total time: 266ms"
time="2025-07-07T20:03:12.467333500+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-07T20:03:12.467333500+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-07T20:03:12.467879200+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-07T20:03:12.495024000+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-07T20:03:13.164729000+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.167852300+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-07T20:03:13.175036500+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-07-07T20:03:13.183371700+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.186525400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:13.186525400+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.192305400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:13.192305400+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.205290100+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.244486200+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-07T20:03:13.280235700+08:00" level=warning msg="[Provider] lancidr not updated for a long time, force refresh"
time="2025-07-07T20:03:13.280235700+08:00" level=warning msg="[Provider] netflix not updated for a long time, force refresh"
time="2025-07-07T20:03:13.281029400+08:00" level=warning msg="[Provider] private not updated for a long time, force refresh"
time="2025-07-07T20:03:13.282603300+08:00" level=warning msg="[Provider] onedrive not updated for a long time, force refresh"
time="2025-07-07T20:03:13.284153400+08:00" level=warning msg="[Provider] gemini not updated for a long time, force refresh"
time="2025-07-07T20:03:13.284666400+08:00" level=warning msg="[Provider] games-cn not updated for a long time, force refresh"
time="2025-07-07T20:03:13.288267400+08:00" level=warning msg="[Provider] telegramcidr not updated for a long time, force refresh"
time="2025-07-07T20:03:13.288811700+08:00" level=warning msg="[Provider] claude not updated for a long time, force refresh"
time="2025-07-07T20:03:13.291397800+08:00" level=warning msg="[Provider] tld-not-cn not updated for a long time, force refresh"
time="2025-07-07T20:03:13.293479700+08:00" level=warning msg="[Provider] github not updated for a long time, force refresh"
time="2025-07-07T20:03:13.294498200+08:00" level=warning msg="[Provider] applications not updated for a long time, force refresh"
time="2025-07-07T20:03:13.295007000+08:00" level=warning msg="[Provider] icloud not updated for a long time, force refresh"
time="2025-07-07T20:03:13.295007000+08:00" level=warning msg="[Provider] apple not updated for a long time, force refresh"
time="2025-07-07T20:03:13.297054600+08:00" level=warning msg="[Provider] microsoft not updated for a long time, force refresh"
time="2025-07-07T20:03:13.300575100+08:00" level=warning msg="[Provider] openai not updated for a long time, force refresh"
time="2025-07-07T20:03:13.302169200+08:00" level=warning msg="[Provider] copilot not updated for a long time, force refresh"
time="2025-07-07T20:03:13.304482900+08:00" level=warning msg="[Provider] google not updated for a long time, force refresh"
time="2025-07-07T20:03:13.306406600+08:00" level=warning msg="[Provider] google-rules not updated for a long time, force refresh"
time="2025-07-07T20:03:13.331895900+08:00" level=warning msg="[Provider] gfw not updated for a long time, force refresh"
time="2025-07-07T20:03:13.463346100+08:00" level=warning msg="[Provider] cncidr not updated for a long time, force refresh"
time="2025-07-07T20:03:13.592528300+08:00" level=warning msg="[Provider] advertising not updated for a long time, force refresh"
time="2025-07-07T20:03:13.631340000+08:00" level=warning msg="[Provider] proxy not updated for a long time, force refresh"
time="2025-07-07T20:03:14.188562900+08:00" level=warning msg="[Provider] direct not updated for a long time, force refresh"
time="2025-07-07T20:03:14.254168800+08:00" level=warning msg="[Provider] reject not updated for a long time, force refresh"
time="2025-07-07T20:03:14.681950800+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-07T20:03:15.161486700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:15.161486700+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-07-07T20:03:15.161486700+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-07T20:03:15.161486700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-07T20:03:18.244529500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:03:18.244529500+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:03:18.676202300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:19.263674700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-07T20:03:19.263674700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-07T20:03:19.263674700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-07T20:03:19.263674700+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-07T20:03:20.196988100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:23.245015700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:03:23.245015700+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:03:24.837420400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6330383238666665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:24.837420400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:26.282014200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:28.889199100+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-07-07T20:03:30.179693400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:30.179693400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:30.179693400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:30.179693400+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:30.702389700+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-07T20:03:30.706160200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:30.715769900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:30.770436900+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-07T20:03:30.842129700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:32.933557700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:03:33.854841900+08:00" level=error msg="PKM1 | 备用官网 https://52pokemon.huxx.top failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:33.854841900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:33.909399300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:03:33.909399300+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:03:37.427599200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:37.427599200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:38.909612800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:03:38.909612800+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:03:41.990654100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638613563616231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:41.990654100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:44.979372800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:44.979372800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:45.321473100+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:45.321473100+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:45.321473100+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:45.321473100+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-07T20:03:45.649827400+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3737393037396534 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:45.649827400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:47.661719400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6161613964343438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:47.661719400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:47.853633800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6338636235343632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62071->*************:38482: use of closed network connection"
time="2025-07-07T20:03:47.853633800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:48.494286100+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6433613932316138 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:48.494286100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:48.518399400+08:00" level=error msg="CC | 安徽联通转美国DP[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:48.518399400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:50.092928500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:50.092928500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:03:54.580989500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:03:54.580989500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:04:03.020629300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:04:03.021166800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:04:05.788305900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6130376437323632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:04:05.788305900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:04:06.384208600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:04:06.384208600+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:04:08.193066500+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6338636235343632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:63080->*************:38482: use of closed network connection"
time="2025-07-07T20:04:08.193066500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:04:12.689765000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6366613734383561 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:04:12.689765000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:04:14.936555100+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-07T20:04:14.936555100+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-07T20:04:14.936555100+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-07T20:04:14.936555100+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-07T20:04:23.910180900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:04:23.910180900+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:05:48.910682100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:05:48.910682100+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:08:20.380013000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:08:22.751706000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/qq) **********:56916(SGTool.exe) --> qqcenter.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-07T20:08:22.768119800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/qq) **********:56927(SGTool.exe) --> qqcenter.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-07T20:08:22.780637200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match DomainKeyword/qq) **********:56928(SGTool.exe) --> qqcenter.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-07T20:08:26.164471300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:08:33.911315500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:08:33.911315500+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:08:35.886157000+08:00" level=error msg="CC | 深港专线6[Trojan][倍率:2.5] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:08:35.886157000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:08:50.448969800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:08:50.448969800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:08:57.205981200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:08:57.205981200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:08:59.130008400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6130376437323632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:08:59.130008400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:09:00.091230600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6338636235343632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59118->*************:38482: use of closed network connection"
time="2025-07-07T20:09:00.091230600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:09:09.810047200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:59416(SGDownload.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-07T20:09:09.840340900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:59418(SGTool.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-07T20:13:21.918354900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:13:28.044809700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-07T20:13:38.844982700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6330383238666665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:13:38.844982700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:13:38.911850000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:80 error: dial tcp [2606:4700:3030::6815:3001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:80: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:80: connectex: The requested address is not valid in its context.\ndial tcp ***********:80: i/o timeout\ndial tcp ************:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout\ndial tcp ***********:80: i/o timeout"
time="2025-07-07T20:13:38.911850000+08:00" level=error msg="[Provider] free-1 pull error: Get \"http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-07T20:13:40.079116100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3161366232613932 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:13:40.079116100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:13:48.982415000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6564643465363332 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:13:48.982415000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:13:59.508094300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6338636235343632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-07T20:13:59.508094300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T20:14:01.091429800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6164396332653066 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-07T20:14:01.091429800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
