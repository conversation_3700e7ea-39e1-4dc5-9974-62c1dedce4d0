# Clash Verge DNS Config

dns:
  enable: true
  listen: :53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  fake-ip-filter-mode: blacklist
  prefer-h3: false
  respect-rules: false
  use-hosts: false
  use-system-hosts: false
  fake-ip-filter:
  - '*.lan'
  - '*.local'
  - '*.arpa'
  - time.*.com
  - ntp.*.com
  - time.*.com
  - +.market.xiaomi.com
  - localhost.ptlogin2.qq.com
  - '*.msftncsi.com'
  - www.msftconnecttest.com
  default-nameserver:
  - system
  - *********
  - *******
  - 2400:3200::1
  - 2001:4860:4860::8888
  nameserver:
  - *******
  - https://doh.pub/dns-query
  - https://dns.alidns.com/dns-query
  fallback: []
  nameserver-policy: {}
  proxy-server-nameserver:
  - https://doh.pub/dns-query
  - https://dns.alidns.com/dns-query
  - tls://*********
  direct-nameserver: []
  direct-nameserver-follow-policy: false
  fallback-filter:
    geoip: true
    geoip-code: CN
    ipcidr:
    - 240.0.0.0/4
    - 0.0.0.0/32
    domain:
    - +.google.com
    - +.facebook.com
    - +.youtube.com
hosts: {}
