Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-22T21:24:55.236008500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-22T21:24:55.241088300+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-22T21:24:55.241088300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-22T21:24:55.241595600+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-22T21:24:55.245669800+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7484"
time="2025-07-22T21:24:55.245669800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-22T21:24:55.428326500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119019"
time="2025-07-22T21:24:55.434212600+08:00" level=info msg="Initial configuration complete, total time: 196ms"
time="2025-07-22T21:24:55.435219900+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-22T21:24:55.435219900+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-22T21:24:55.435219900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-22T21:24:55.469723700+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-22T21:24:56.010799300+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-22T21:24:56.010799300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:24:56.014022900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:24:56.014022900+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-22T21:24:56.014022900+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-22T21:24:56.020000600+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-22T21:24:59.859261700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-22T21:24:59.865402500+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-22T21:24:59.865402500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-22T21:24:59.865910200+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-22T21:24:59.869978500+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7484"
time="2025-07-22T21:24:59.869978500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-22T21:25:00.105971400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119019"
time="2025-07-22T21:25:00.111864200+08:00" level=info msg="Initial configuration complete, total time: 250ms"
time="2025-07-22T21:25:00.114371900+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-22T21:25:00.114371900+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-22T21:25:00.114371900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-22T21:25:00.152121400+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-22T21:25:00.789654900+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-22T21:25:00.790486600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:25:00.790486600+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-22T21:25:00.793416800+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-22T21:25:00.797611900+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-22T21:25:00.797611900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:25:00.827085600+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-22T21:25:02.429720900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-22T21:25:02.586605400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-22T21:25:04.968663100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-22T21:25:04.969168700+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-22T21:25:05.206582900+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:05.645310000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:05.645310000+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:05.787966000+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:05.789532100+08:00" level=error msg="FL | 距离下次重置剩余：19 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.789532100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.793900600+08:00" level=error msg="PKM2 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.793900600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.794427200+08:00" level=error msg="PKM2 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.794427200+08:00" level=error msg="PKM2 | 剩余流量：56.79 GB failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.794427200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.794427200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.794427200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-22T21:25:05.795029100+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-22T21:25:05.798099100+08:00" level=error msg="PKM1 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.798099100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.798099100+08:00" level=error msg="PKM1 | 官网 https://love.52pokemon.cc failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.798099100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.798099100+08:00" level=error msg="PKM1 | 🇸🇬【亚洲】新加坡01丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.798099100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.807011400+08:00" level=error msg="Free-6 | 🟡💻_github.com/Ruk1ng001_6531623064303132 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.807011400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.807011400+08:00" level=error msg="CC | 广东移动转台湾BGP2[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.807011400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:05.807011400+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💻_github.com/Ruk1ng001_3633306337373665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:05.807011400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:06.124988400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.124988400+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.190134200+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.241699800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.241699800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-22T21:25:06.241699800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.344792500+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.349238800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.365702300+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.370237100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.370237100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.373345000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.378369600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.462348800+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.475960700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.487619600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.487619600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.488492600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.498200100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.501254000+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.515841900+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-22T21:25:06.576721800+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.598443600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.599810600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.607729400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.626720000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.642077400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.647197500+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.706309200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.745671200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.765650800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.767155900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.780374100+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.788040800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.847964200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.855722400+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.893185800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.938422100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.939008800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:06.988716700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.053608800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.075738000+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.102784500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.103288400+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.111150800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.129396700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.208752300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.214443800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.377230200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.414342800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.507955600+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.807339900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.873385000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:07.941231200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.125916200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.219398800+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.219398800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.239188200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.382870700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.485667600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55549(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.525802100+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:08.977537800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:09.123901800+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60380(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:09.239871500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55548(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:09.302155700+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) **********:52641(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: context deadline exceeded"
time="2025-07-22T21:25:09.328943200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55361(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:09.484854200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55551(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.092915000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55568(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.210539800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.326613500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.604155800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.707256000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.715418800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.839659200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.842164800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.903519400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.903519400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:10.988444200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.012542300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.027822800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.029851100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.137445500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.154270600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.164272800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.200046000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.202102400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.283546700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.311389600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.347771200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.434495600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.474368100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.490676400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.521213400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.641700400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.666402700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.696122200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.943677600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.966034500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:11.968866000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.086080600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.087894500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.629226800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.629226800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.876187600+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:12.957419900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.059697000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.207890800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.278672600+08:00" level=error msg="MLY | SG - 新加坡 01 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:13.278672600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:13.280717000+08:00" level=error msg="MLY | SG - 新加坡 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:13.280717000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:13.326495500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55989(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.480691000+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55914(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.657928400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:13.686579100+08:00" level=error msg="MLY | SG - 新加坡 03 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:13.686579100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:13.904247300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:14.031268500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55945(msedgewebview2.exe) --> cdn.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:14.101494500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:14.444073800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:14.484673500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:55988(clash-verge.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:15.031300100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:55916(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-22T21:25:15.130171500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:56049(msedgewebview2.exe) --> fastly.jsdelivr.net:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:25:17.445102900+08:00" level=error msg="CC | 广东移动转日本NTT4[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:17.445102900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:18.607237200+08:00" level=error msg="Free-6 | 🟡_🇳🇱_💻_github.com/Ruk1ng001_3534666130343664 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:18.607237200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:20.790989500+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
