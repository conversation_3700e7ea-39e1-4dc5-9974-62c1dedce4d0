2025-06-28 20:27:07 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-06-28 20:27:07 INFO - [Setup] 初始化资源...
2025-06-28 20:27:07 INFO - [Setup] 初始化完成，继续执行
2025-06-28 20:27:07 INFO - [System] 应用就绪或恢复
2025-06-28 20:27:07 INFO - [Config] 生成运行时配置成功
2025-06-28 20:27:07 INFO - [Config] 开始验证配置
2025-06-28 20:27:07 INFO - [Config] 生成临时配置文件用于验证
2025-06-28 20:27:07 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-28 20:27:07 INFO - [Config] 使用内核: verge-mihomo
2025-06-28 20:27:07 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-28 20:27:08 INFO - [Config] -------- 验证结果 --------
2025-06-28 20:27:08 INFO - [Config] 验证成功
2025-06-28 20:27:08 INFO - [Config] -------- 验证结束 --------
2025-06-28 20:27:08 INFO - [Config] 配置验证成功
2025-06-28 20:27:08 INFO - [Setup] 清理冗余的Profile文件...
2025-06-28 20:27:08 INFO - Profile 文件清理完成: 总文件数=78, 删除文件数=0, 失败数=0
2025-06-28 20:27:08 INFO - [Setup] 启动时Profile文件清理完成
2025-06-28 20:27:08 INFO - [Service] 开始检查服务是否正在运行
2025-06-28 20:27:08 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-28 20:27:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:08 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-28 20:27:08 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-28 20:27:08 INFO - [Service] 服务正在运行
2025-06-28 20:27:08 INFO - [Core] 服务当前可用或看似可用，尝试通过服务模式启动/重装
2025-06-28 20:27:08 INFO - [Service] 开始检查服务是否需要重装
2025-06-28 20:27:08 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-28 20:27:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:08 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-28 20:27:08 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-28 20:27:08 INFO - 服务版本检测：当前=1.1.0, 要求=1.1.0
2025-06-28 20:27:08 INFO - 服务版本匹配，无需重装
2025-06-28 20:27:08 INFO - 正在尝试通过服务启动核心
2025-06-28 20:27:08 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-28 20:27:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:08 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-28 20:27:08 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-28 20:27:08 INFO - 检测到服务版本: 1.1.0, 要求版本: 1.1.0
2025-06-28 20:27:08 INFO - 服务版本匹配
2025-06-28 20:27:08 INFO - [Service] 开始检查服务是否正在运行
2025-06-28 20:27:08 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-28 20:27:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:08 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-28 20:27:08 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-28 20:27:08 INFO - [Service] 服务正在运行
2025-06-28 20:27:08 INFO - 服务已在运行且版本匹配，尝试使用
2025-06-28 20:27:08 INFO - 尝试使用现有服务启动核心 (IPC)
2025-06-28 20:27:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:10 INFO - [Service] IPC请求完成: 命令=StartClash, 成功=true
2025-06-28 20:27:10 INFO - [Service] 服务成功启动核心
2025-06-28 20:27:10 INFO - [Core] 服务模式成功启动核心
2025-06-28 20:27:10 INFO - [Tray] 创建系统托盘...
2025-06-28 20:27:10 INFO - 正在从AppHandle创建系统托盘
2025-06-28 20:27:10 INFO - 系统托盘创建成功
2025-06-28 20:27:10 INFO - [Tray] 系统托盘创建成功
2025-06-28 20:27:10 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-06-28 20:27:10 INFO - [Window] UI准备阶段更新: NotStarted, 耗时: 0ms
2025-06-28 20:27:10 INFO - [Timer] Initializing timer...
2025-06-28 20:27:10 INFO - [Timer] Refreshing 6 timer tasks
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=RqkaNnsWj6RG, id=3, interval=1440min
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=Rcvw1SCKmdqL, id=1, interval=1440min
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=RKzTK4d5rjIF, id=2, interval=1440min
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=Rx9LL6kjRU3B, id=4, interval=360min
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=RFHm8frTGpzz, id=5, interval=1440min
2025-06-28 20:27:10 INFO - [Timer] Adding task: uid=RqUQUTUdf5hK, id=6, interval=1440min
2025-06-28 20:27:10 INFO - [Timer] 已注册的定时任务数量: 6
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=RqkaNnsWj6RG, interval=1440min, task_id=3
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=Rcvw1SCKmdqL, interval=1440min, task_id=1
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=RKzTK4d5rjIF, interval=1440min, task_id=2
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=Rx9LL6kjRU3B, interval=360min, task_id=4
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=RFHm8frTGpzz, interval=1440min, task_id=5
2025-06-28 20:27:10 INFO - [Timer] 注册了定时任务 - uid=RqUQUTUdf5hK, interval=1440min, task_id=6
2025-06-28 20:27:10 INFO - [Timer] 需要立即更新的配置: uid=RKzTK4d5rjIF
2025-06-28 20:27:10 INFO - [Timer] 需要立即更新的配置: uid=Rx9LL6kjRU3B
2025-06-28 20:27:10 INFO - [Timer] 需要立即更新的配置: uid=RFHm8frTGpzz
2025-06-28 20:27:10 INFO - [Timer] 需要立即更新的配置数量: 3
2025-06-28 20:27:10 INFO - [Timer] 立即执行任务: uid=RKzTK4d5rjIF
2025-06-28 20:27:10 INFO - [Timer] 立即执行任务: uid=Rx9LL6kjRU3B
2025-06-28 20:27:10 INFO - [Timer] 立即执行任务: uid=RFHm8frTGpzz
2025-06-28 20:27:10 INFO - [Timer] Timer initialization completed
2025-06-28 20:27:10 INFO - [Timer] Running timer task for profile: RKzTK4d5rjIF
2025-06-28 20:27:10 INFO - [Timer] Running timer task for profile: RFHm8frTGpzz
2025-06-28 20:27:10 INFO - [Timer] Running timer task for profile: Rx9LL6kjRU3B
2025-06-28 20:27:10 INFO - [Timer] 配置 RKzTK4d5rjIF 是否为当前激活配置: false
2025-06-28 20:27:10 INFO - [Config] [订阅更新] 开始更新订阅 RKzTK4d5rjIF
2025-06-28 20:27:10 INFO - [Timer] 配置 RFHm8frTGpzz 是否为当前激活配置: false
2025-06-28 20:27:10 INFO - [Config] [订阅更新] 开始更新订阅 RFHm8frTGpzz
2025-06-28 20:27:10 INFO - [订阅更新] RKzTK4d5rjIF 是远程订阅，URL: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
2025-06-28 20:27:10 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-28 20:27:10 INFO - [Timer] 配置 Rx9LL6kjRU3B 是否为当前激活配置: true
2025-06-28 20:27:10 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-06-28 20:27:10 INFO - [订阅更新] RFHm8frTGpzz 是远程订阅，URL: http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae
2025-06-28 20:27:10 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-28 20:27:10 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-06-28 20:27:10 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-28 20:27:10 INFO - [Frontend] 启动过程中发现错误，加入消息队列: config_validate::success - 
2025-06-28 20:27:10 INFO - [Setup] 异步设置任务完成，耗时: 3.3779607s
2025-06-28 20:27:10 INFO - [Setup] 应用设置成功完成
2025-06-28 20:27:10 INFO - [Frontend] 发送1条启动时累积的错误消息
2025-06-28 20:27:10 INFO - [Window] 窗口已立即显示
2025-06-28 20:27:10 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-06-28 20:27:10 INFO - [Window] 窗口显示流程完成
2025-06-28 20:27:11 INFO - [订阅更新] 更新订阅配置成功
2025-06-28 20:27:11 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-28 20:27:11 INFO - [Timer] Timer task completed successfully for uid: RKzTK4d5rjIF (took 538ms)
2025-06-28 20:27:11 INFO - [Cmd] 快速获取配置列表成功
2025-06-28 20:27:11 INFO - UI加载阶段更新: Loading
2025-06-28 20:27:11 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-28 20:27:11 INFO - UI加载阶段更新: Loading
2025-06-28 20:27:11 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-28 20:27:11 ERROR - error sending request
2025-06-28 20:27:11 ERROR - error sending request
2025-06-28 20:27:11 INFO - UI加载阶段更新: DomReady
2025-06-28 20:27:11 INFO - [Window] UI准备阶段更新: DomReady, 耗时: 0ms
2025-06-28 20:27:11 INFO - UI加载阶段更新: ResourcesLoaded
2025-06-28 20:27:11 INFO - [Window] UI准备阶段更新: ResourcesLoaded, 耗时: 0ms
2025-06-28 20:27:11 INFO - [Service] 开始检查服务是否正在运行
2025-06-28 20:27:11 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-28 20:27:11 INFO - [Service] 正在连接服务 (Windows)...
2025-06-28 20:27:11 INFO - [Service] 服务连接成功 (Windows)
2025-06-28 20:27:11 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-28 20:27:11 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-28 20:27:11 INFO - [Service] 服务正在运行
2025-06-28 20:27:11 INFO - 前端UI已准备就绪
2025-06-28 20:27:11 INFO - [Window] UI已标记为完全就绪
2025-06-28 20:27:11 INFO - [Window] UI已完全加载就绪
2025-06-28 20:27:12 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request，尝试使用Clash代理更新
2025-06-28 20:27:12 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile with status 502 Bad Gateway
2025-06-28 20:27:12 ERROR - [[Timer]] Failed to update profile uid RFHm8frTGpzz: failed to fetch remote profile with status 502 Bad Gateway
2025-06-28 20:27:12 INFO - closing window...
2025-06-28 20:27:17 INFO - [订阅更新] 更新订阅配置成功
2025-06-28 20:27:17 INFO - [订阅更新] 是否为当前使用的订阅: true
2025-06-28 20:27:17 INFO - [Config] [订阅更新] 更新内核配置
2025-06-28 20:27:17 INFO - [Config] 开始更新配置
2025-06-28 20:27:17 INFO - [Config] 生成新的配置内容
2025-06-28 20:27:17 INFO - [Config] 生成临时配置文件用于验证
2025-06-28 20:27:17 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-28 20:27:17 INFO - [Config] 使用内核: verge-mihomo
2025-06-28 20:27:17 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-28 20:27:19 INFO - [Config] -------- 验证结果 --------
2025-06-28 20:27:19 INFO - [Config] 验证成功
2025-06-28 20:27:19 INFO - [Config] -------- 验证结束 --------
2025-06-28 20:27:19 INFO - [Config] 配置验证通过
2025-06-28 20:27:19 INFO - [Config] 生成运行时配置
2025-06-28 20:27:20 INFO - [Core] Configuration updated successfully
2025-06-28 20:27:20 INFO - [Config] [订阅更新] 更新成功
2025-06-28 20:27:20 INFO - [Timer] Timer task completed successfully for uid: Rx9LL6kjRU3B (took 9909ms)
2025-06-28 20:49:55 INFO - Tray点击事件: 显示主窗口
2025-06-28 20:49:55 INFO - [Window] 开始智能显示主窗口
2025-06-28 20:49:55 INFO - [Window] 开始激活窗口
2025-06-28 20:49:55 INFO - [Window] 窗口激活成功
2025-06-28 20:49:55 INFO - 窗口显示结果: Shown
2025-06-28 20:49:57 INFO - [Cmd] 快速获取配置列表成功
2025-06-28 20:51:19 INFO - [Cmd] 快速获取配置列表成功
2025-06-28 23:42:37 INFO - Tray点击事件: 显示主窗口
2025-06-28 23:42:37 INFO - [Window] 开始智能显示主窗口
2025-06-28 23:42:37 INFO - [Window] 开始激活窗口
2025-06-28 23:42:37 INFO - [Window] 窗口已最小化，正在取消最小化
2025-06-28 23:42:37 INFO - [Window] 窗口激活成功
2025-06-28 23:42:37 INFO - 窗口显示结果: Shown
2025-06-29 00:22:00 INFO - Tray点击事件: 显示主窗口
2025-06-29 00:22:00 INFO - [Window] 开始智能显示主窗口
2025-06-29 00:22:00 INFO - [Window] 开始激活窗口
2025-06-29 00:22:00 INFO - [Window] 窗口激活成功
2025-06-29 00:22:00 INFO - 窗口显示结果: Shown
2025-06-29 01:24:07 INFO - Tray点击事件: 显示主窗口
2025-06-29 01:24:07 INFO - [Window] 开始智能显示主窗口
2025-06-29 01:24:07 INFO - [Window] 开始激活窗口
2025-06-29 01:24:07 INFO - [Window] 窗口已最小化，正在取消最小化
2025-06-29 01:24:07 INFO - [Window] 窗口激活成功
2025-06-29 01:24:07 INFO - 窗口显示结果: Shown
2025-06-29 01:24:09 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 01:25:58 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 01:26:02 ERROR - the remote profile data is invalid yaml
2025-06-29 01:26:03 ERROR - the remote profile data is invalid yaml
2025-06-29 01:26:51 ERROR - failed to fetch remote profile with status 404 Not Found
2025-06-29 01:26:52 ERROR - failed to fetch remote profile with status 404 Not Found
2025-06-29 01:39:42 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 01:41:48 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 01:42:00 ERROR - failed to fetch remote profile: Failed to send HTTP request: builder error for url (vless://***********:37571?encryption=none&flow=xtls-rprx-vision&security=reality&sni=www.iij.ad.jp&fp=chrome&pbk=OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk&type=tcp&headerType=none#US-DigitalOcean,_LLC)
2025-06-29 01:42:00 ERROR - failed to fetch remote profile: Failed to send HTTP request: builder error for url (vless://***********:37571?encryption=none&flow=xtls-rprx-vision&security=reality&sni=www.iij.ad.jp&fp=chrome&pbk=OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk&type=tcp&headerType=none#US-DigitalOcean,_LLC)
2025-06-29 01:42:14 INFO - [Service] 开始检查服务是否正在运行
2025-06-29 01:42:14 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-29 01:42:14 INFO - [Service] 正在连接服务 (Windows)...
2025-06-29 01:42:14 INFO - [Service] 服务连接成功 (Windows)
2025-06-29 01:42:14 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-29 01:42:14 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-29 01:42:14 INFO - [Service] 服务正在运行
2025-06-29 01:46:21 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 01:46:26 ERROR - the remote profile data is invalid yaml
2025-06-29 01:46:27 ERROR - the remote profile data is invalid yaml
2025-06-29 01:46:58 ERROR - the remote profile data is invalid yaml
2025-06-29 01:46:59 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:04 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:05 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:09 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:11 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:14 ERROR - the remote profile data is invalid yaml
2025-06-29 01:47:15 ERROR - the remote profile data is invalid yaml
2025-06-29 01:59:11 INFO - [Config] [cmd配置save] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\profiles\pR6t6GHVbRMr.yaml, 是否为merge文件: false
2025-06-29 01:59:11 INFO - [Config] 使用Clash内核验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\profiles\pR6t6GHVbRMr.yaml
2025-06-29 01:59:11 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\profiles\pR6t6GHVbRMr.yaml
2025-06-29 01:59:11 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 01:59:11 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 01:59:11 INFO - [Config] -------- 验证结果 --------
2025-06-29 01:59:11 INFO - [Config] 验证成功
2025-06-29 01:59:11 INFO - [Config] -------- 验证结束 --------
2025-06-29 01:59:11 INFO - [Config] [cmd配置save] 验证成功
2025-06-29 01:59:11 INFO - [Config] 开始更新配置
2025-06-29 01:59:11 INFO - [Config] 生成新的配置内容
2025-06-29 01:59:11 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 01:59:11 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 01:59:11 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 01:59:11 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 01:59:13 INFO - [Config] -------- 验证结果 --------
2025-06-29 01:59:13 INFO - [Config] 验证成功
2025-06-29 01:59:13 INFO - [Config] -------- 验证结束 --------
2025-06-29 01:59:13 INFO - [Config] 配置验证通过
2025-06-29 01:59:13 INFO - [Config] 生成运行时配置
2025-06-29 01:59:14 INFO - [Core] Configuration updated successfully
2025-06-29 01:59:15 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:02:29 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:02:30 INFO - [Config] 开始更新配置
2025-06-29 02:02:30 INFO - [Config] 生成新的配置内容
2025-06-29 02:02:30 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 02:02:30 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 02:02:30 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 02:02:30 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 02:02:31 INFO - [Config] -------- 验证结果 --------
2025-06-29 02:02:31 INFO - [Config] 验证成功
2025-06-29 02:02:31 INFO - [Config] -------- 验证结束 --------
2025-06-29 02:02:31 INFO - [Config] 配置验证通过
2025-06-29 02:02:31 INFO - [Config] 生成运行时配置
2025-06-29 02:02:33 INFO - [Core] Configuration updated successfully
2025-06-29 02:02:33 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:10:09 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:10:10 INFO - [Config] 开始更新配置
2025-06-29 02:10:10 INFO - [Config] 生成新的配置内容
2025-06-29 02:10:10 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 02:10:10 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 02:10:10 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 02:10:10 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 02:10:11 INFO - [Config] -------- 验证结果 --------
2025-06-29 02:10:11 INFO - [Config] 验证成功
2025-06-29 02:10:11 INFO - [Config] -------- 验证结束 --------
2025-06-29 02:10:11 INFO - [Config] 配置验证通过
2025-06-29 02:10:11 INFO - [Config] 生成运行时配置
2025-06-29 02:10:16 INFO - [Core] Configuration updated successfully
2025-06-29 02:10:17 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:12:01 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 02:12:01 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:12:51 ERROR - error sending request
2025-06-29 02:12:51 ERROR - error sending request
2025-06-29 02:15:51 ERROR - error sending request
2025-06-29 02:15:54 ERROR - error sending request
2025-06-29 02:15:59 ERROR - error sending request
2025-06-29 02:16:05 ERROR - error sending request
2025-06-29 02:17:17 ERROR - error sending request
2025-06-29 02:17:17 ERROR - error sending request
2025-06-29 02:18:09 ERROR - error sending request
2025-06-29 02:18:09 ERROR - error sending request
2025-06-29 02:18:30 ERROR - error sending request
2025-06-29 02:19:06 ERROR - error sending request
2025-06-29 02:19:37 ERROR - error sending request
2025-06-29 02:20:03 ERROR - error sending request
2025-06-29 02:20:37 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:20:39 ERROR - error sending request
2025-06-29 02:20:39 ERROR - error sending request
2025-06-29 02:20:39 INFO - [Config] 开始更新配置
2025-06-29 02:20:39 INFO - [Config] 生成新的配置内容
2025-06-29 02:20:40 INFO - [Config] 生成临时配置文件用于验证
2025-06-29 02:20:40 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-29 02:20:40 INFO - [Config] 使用内核: verge-mihomo
2025-06-29 02:20:40 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-29 02:20:41 INFO - [Config] -------- 验证结果 --------
2025-06-29 02:20:41 INFO - [Config] 验证成功
2025-06-29 02:20:41 INFO - [Config] -------- 验证结束 --------
2025-06-29 02:20:41 INFO - [Config] 配置验证通过
2025-06-29 02:20:41 INFO - [Config] 生成运行时配置
2025-06-29 02:20:44 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:20:44 ERROR - error sending request
2025-06-29 02:20:44 ERROR - error sending request
2025-06-29 02:20:44 ERROR - error sending request
2025-06-29 02:20:46 INFO - [Core] Configuration updated successfully
2025-06-29 02:21:15 ERROR - error sending request
2025-06-29 02:21:20 ERROR - error sending request
2025-06-29 02:21:25 ERROR - error sending request
2025-06-29 02:21:30 ERROR - error sending request
2025-06-29 02:21:36 ERROR - error sending request
2025-06-29 02:21:37 ERROR - error sending request
2025-06-29 02:21:40 ERROR - error sending request
2025-06-29 02:21:45 ERROR - error sending request
2025-06-29 02:21:50 ERROR - error sending request
2025-06-29 02:21:51 ERROR - error sending request
2025-06-29 02:21:56 ERROR - error sending request
2025-06-29 02:22:06 ERROR - error sending request
2025-06-29 02:22:11 ERROR - error sending request
2025-06-29 02:22:15 ERROR - error sending request
2025-06-29 02:22:26 ERROR - error sending request
2025-06-29 02:22:31 ERROR - error sending request
2025-06-29 02:22:36 ERROR - error sending request
2025-06-29 02:22:37 ERROR - error sending request
2025-06-29 02:22:41 ERROR - error sending request
2025-06-29 02:22:47 ERROR - error sending request
2025-06-29 02:22:52 ERROR - error sending request
2025-06-29 02:22:57 ERROR - error sending request
2025-06-29 02:23:02 ERROR - error sending request
2025-06-29 02:23:48 ERROR - error sending request
2025-06-29 02:23:48 ERROR - error sending request
2025-06-29 02:23:50 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 02:23:50 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:23:50 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 02:23:50 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:23:50 ERROR - error sending request
2025-06-29 02:23:52 ERROR - error sending request
2025-06-29 02:23:57 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 02:23:57 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:24:18 ERROR - error sending request
2025-06-29 02:24:19 ERROR - error sending request
2025-06-29 02:24:29 ERROR - error sending request
2025-06-29 02:25:00 ERROR - error sending request
2025-06-29 02:25:05 ERROR - error sending request
2025-06-29 02:25:11 ERROR - error sending request
2025-06-29 02:25:36 ERROR - error sending request
2025-06-29 02:25:47 ERROR - error sending request
2025-06-29 02:25:52 ERROR - error sending request
2025-06-29 02:26:07 ERROR - error sending request
2025-06-29 02:26:18 ERROR - error sending request
2025-06-29 02:26:22 ERROR - error sending request
2025-06-29 02:26:22 ERROR - error sending request
2025-06-29 02:26:38 ERROR - error sending request
2025-06-29 02:26:45 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 02:27:00 ERROR - error sending request
2025-06-29 02:27:04 ERROR - error sending request
2025-06-29 02:27:05 ERROR - error sending request
2025-06-29 03:05:37 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:07:02 INFO - Tray点击事件: 显示主窗口
2025-06-29 03:07:02 INFO - [Window] 开始智能显示主窗口
2025-06-29 03:07:02 INFO - [Window] 开始激活窗口
2025-06-29 03:07:02 INFO - [Window] 窗口激活成功
2025-06-29 03:07:02 INFO - 窗口显示结果: Shown
2025-06-29 03:07:05 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:07:07 INFO - [Config] [订阅更新] 开始更新订阅 R1vf8GRLk2tM
2025-06-29 03:07:07 INFO - [订阅更新] R1vf8GRLk2tM 是远程订阅，URL: https://www.ccsub.org/link/0bq2vG3xxpKOWhUy?clash=1
2025-06-29 03:07:07 INFO - [订阅更新] 开始下载新的订阅内容
2025-06-29 03:07:10 INFO - [订阅更新] 更新订阅配置成功
2025-06-29 03:07:10 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-06-29 03:07:10 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:08:37 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:08:52 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:08:52 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:10:14 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:10:14 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:19:58 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:19:58 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:20:00 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:20:00 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:20:01 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:20:01 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:20:13 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:20:13 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:20:13 ERROR - error sending request
2025-06-29 03:20:34 ERROR - error sending request
2025-06-29 03:21:40 ERROR - error sending request
2025-06-29 03:24:06 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-06-29 03:24:06 INFO - [Cmd] 快速获取配置列表成功
2025-06-29 03:24:09 INFO - closing window...
