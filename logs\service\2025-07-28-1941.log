Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-28T19:41:10.821726300+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-28T19:41:10.827475600+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-28T19:41:10.827475600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-28T19:41:10.827988300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-28T19:41:10.831540200+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-07-28T19:41:10.832150700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-28T19:41:11.033096800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118476"
time="2025-07-28T19:41:11.037620300+08:00" level=info msg="Initial configuration complete, total time: 213ms"
time="2025-07-28T19:41:11.039636300+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-28T19:41:11.039636300+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-28T19:41:11.039636300+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:41:11.073869500+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-28T19:41:11.712141200+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:41:11.713320600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:41:11.717195000+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:41:11.719507200+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:41:11.722448800+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:41:11.722448800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:41:11.765481800+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:41:11.882709400+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-28T19:41:13.692180300+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:41:14.118850100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:14.118850100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:14.118850100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:41:14.118850100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:41:14.118850100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:14.118850100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:14.118850100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:41:14.118850100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:41:14.504569100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:14.504569100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.504569100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.504569100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:14.504569100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.894241700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.894241700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.894241700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:14.894241700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:15.228728800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.264074200+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:41:15.889196200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.889196200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:15.889196200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.889196200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:15.889196200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:16.712613400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:16.712613400+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:41:18.172251900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:18.172251900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:18.172251900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.172251900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.172764000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.173273300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.173782500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.173782500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:18.173782500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:18.174799800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:18.736760200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:19.512479300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:19.512479300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:19.512479300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:19.512479300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:19.512479300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:19.514513600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:19.514513600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:19.514513600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:19.515033600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:19.515033600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:20.890177600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:59124(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:41:21.713059000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:21.713059000+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:41:22.220356600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:22.220356600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:22.220356600+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:41:22.221367100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:22.221367100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:22.254772600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:23.523686500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": net/http: TLS handshake timeout"
time="2025-07-28T19:41:25.969105000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) 127.0.0.1:59468(clash-verge.exe) --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:41:29.309939200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:41:29.309939200+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:41:30.662833300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:41:30.662833300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.662833300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.662833300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.663339300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.664999100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:41:30.688074000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:41:30.710474300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.710474300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.711573100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:30.715253800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: i/o timeout"
time="2025-07-28T19:41:35.931182000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:35.931182000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:35.931182000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:35.931182000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:36.270278500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:41:39.588038200+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:41:39.588038200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:41:41.822292900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6439373666393161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:41:41.822292900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-28T19:41:47.218928400+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-28T19:41:47.224533000+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-28T19:41:47.224533000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-28T19:41:47.224533000+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-28T19:41:47.228081700+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7480"
time="2025-07-28T19:41:47.228591500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-28T19:41:47.432477700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118417"
time="2025-07-28T19:41:47.437726100+08:00" level=info msg="Initial configuration complete, total time: 216ms"
time="2025-07-28T19:41:47.439249200+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-28T19:41:47.439249200+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-28T19:41:47.439249200+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:41:47.474494700+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-28T19:41:48.100047500+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:41:48.103537800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:41:48.106797700+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:41:48.108945500+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:41:48.110638400+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:41:48.110638400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:41:48.150693200+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:41:49.622413700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:41:50.240375400+08:00" level=warning msg="because 🇯🇵 日本 failed multiple times, active health check"
time="2025-07-28T19:41:50.269386600+08:00" level=warning msg="because 🇰🇷 韩国 failed multiple times, active health check"
time="2025-07-28T19:41:50.494329400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:50.494329400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:50.494329400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:50.494329400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:50.494329400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:50.693645500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:41:51.102579600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:53.110541700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:41:53.110541700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:41:53.111050500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:53.111050500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:41:53.112583700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:53.112583700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:41:54.093194800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:54.093194800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:54.093194800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:54.309330200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: can't resolve ip: couldn't find ip"
time="2025-07-28T19:41:54.538336300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:41:54.539864800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.539864800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:54.539864800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.540384700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.540940700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:54.542526000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.542526000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:54.542526000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.543275700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:54.543806700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:54.607319500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:41:54.607319500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:41:54.986102300+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:55.055060700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:55.500483700+08:00" level=warning msg="[UDP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: can't resolve ip: couldn't find ip"
time="2025-07-28T19:41:55.502054300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.502054300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:55.502562600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.502562600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:55.502562600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:41:55.503162200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.505411400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.505411400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.505947200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:41:55.505947200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:41:55.505947200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:41:55.505947200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:41:55.567307800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:41:56.444511300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-07-28T19:41:56.445545000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: operation was canceled"
time="2025-07-28T19:41:56.446558700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: operation was canceled"
time="2025-07-28T19:41:57.000038200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:57.000038200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:41:57.000038200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:41:57.000038200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:41:58.110623000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:41:58.110623000+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:41:58.754708500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:58.754708500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:41:58.754708500+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:41:58.754708500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk01.521pokemon.com:52011 connect error: dial tcp ***********:52011: operation was canceled"
time="2025-07-28T19:42:00.018056500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:00.018056500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:04.539035100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:04.571716200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:04.588782700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:42:06.386725500+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:42:06.388330300+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:42:06.388892700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:42:06.391080200+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:42:06.398794600+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:42:06.399857100+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:42:06.399857100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:42:06.445531900+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:42:08.065766600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:08.065766600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:08.065766600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:42:08.065766600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:42:08.065766600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:08.065766600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:42:08.065766600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:08.065766600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:11.283803500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:11.398168300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:11.398168300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:42:11.398675900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:11.398675900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:42:11.536695900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:11.536695900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:11.536695900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:11.536695900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:11.536695900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:12.895776300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:12.895776300+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:16.398342500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:16.398342500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:42:16.398854500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:16.398854500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:42:17.501653700+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:18.226564400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3431336264653233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:18.226564400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:18.671836600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:18.671836600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:19.628853900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:22.751016100+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-28T19:42:22.752569600+08:00" level=warning msg="[Provider] 飞链 not updated for a long time, force refresh"
time="2025-07-28T19:42:22.753631000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:42:22.754138400+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-28T19:42:22.760523700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-28T19:42:22.760523700+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-28T19:42:22.760523700+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-28T19:42:22.809864100+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-28T19:42:24.241526500+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:24.241526500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:24.244101500+08:00" level=error msg="PKM1 | 🇭🇰【亚洲】香港02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:24.244101500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:24.244101500+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:24.244101500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:24.245135400+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:24.245135400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:24.247181400+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡03丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:24.247181400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:24.319782900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:24.319782900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:24.320286000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:42:24.320286000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:42:24.320286000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:24.320286000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:42:24.320286000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:24.320286000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:42:27.131806700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:42:27.131806700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:42:27.133619100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.133619100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.134131800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.134131800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.134131800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:42:27.134646200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:27.134646200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.136205900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:42:27.152603000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:27.166540300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.166540300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.166540300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.166540300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.166540300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.168067200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.168067200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.168067200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.168576300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.168576300+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:42:27.169135700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.169647800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.169647800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.169647800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.169647800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.170191700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:42:27.170703300+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> 104.16.248.249:443 error: dial tcp 104.16.248.249:443: i/o timeout"
time="2025-07-28T19:42:27.171897900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.172917600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:27.759613600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:27.759613600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:42:27.760206400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:27.760206400+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:42:28.138819400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: i/o timeout"
time="2025-07-28T19:42:28.138819400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:28.138819400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:28.138819400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: i/o timeout"
time="2025-07-28T19:42:28.138819400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:28.142539600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-07-28T19:42:28.253378600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:28.253378600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:28.254067500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: operation was canceled"
time="2025-07-28T19:42:28.254067500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: operation was canceled"
time="2025-07-28T19:42:28.254067500+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: operation was canceled"
time="2025-07-28T19:42:29.429058900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:29.715143900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:29.715143900+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:30.769565900+08:00" level=error msg="CC | 广东移动转台湾BGP2[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:30.769565900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:32.759653300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:42:32.759653300+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:42:32.760664700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:32.760664700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:42:33.024960800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.024960800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.024960800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:42:33.024960800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.024960800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.026037200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.029174100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.029174100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.029695400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.029695400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.030262400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:33.353633400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) **********:64297(Cherry Studio.exe) --> hpoynbngxpjn.ap-southeast-1.clawcloudrun.com:443 error: dial tcp 47.79.1.82:443: i/o timeout\ndial tcp 47.79.98.82:443: i/o timeout\ndial tcp 47.79.227.112:443: i/o timeout\ndial tcp 47.79.243.183:443: i/o timeout\ndial tcp 47.79.123.18:443: i/o timeout\ndial tcp 47.79.237.237:443: i/o timeout\ndial tcp 47.79.98.1:443: i/o timeout"
time="2025-07-28T19:42:33.927992700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: operation was canceled"
time="2025-07-28T19:42:33.927992700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: operation was canceled"
time="2025-07-28T19:42:35.535896800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:35.535896800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:35.694394700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:35.694394700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:36.035079500+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:42:37.000214700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:37.000214700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:42:37.000717900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:42:37.000717900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:42:37.797483900+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:37.797483900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:38.130326300+08:00" level=error msg="CC | 上海联通转香港HKT2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-28T19:42:38.130837300+08:00" level=error msg="CC | 上海联通转香港HKC3[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-28T19:42:38.130837300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:38.130837300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:38.130837300+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💼_github.com/Ruk1ng001_3662346364343063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-28T19:42:38.130837300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:38.815230400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:42:38.815230400+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:38.815740000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:38.827031000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:38.837522700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:41.365117400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6561353930393261 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:41.365117400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:41.423611800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.423611800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.423611800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.424131900+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) mihomo --> cloudflare-dns.com:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.424131900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.426698700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:41.426698700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:42:43.111146700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:43.111146700+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:42:43.944035100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: operation was canceled"
time="2025-07-28T19:42:43.945691000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: operation was canceled"
time="2025-07-28T19:42:43.945691000+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: operation was canceled"
time="2025-07-28T19:42:45.815763400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:42:45.815763400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:42:46.614583200+08:00" level=error msg="PKM1 | 🇨🇭【欧洲】瑞士【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:46.614583200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:42:46.756362600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:46.758936300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:42:46.948484200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: operation was canceled"
time="2025-07-28T19:42:48.175370800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:49.167059900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> **************:443 error: dial tcp **************:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:49.167059900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *********:443 error: dial tcp *********:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:49.182296800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:49.182296800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:49.182296800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: dial tcp *******:443: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond."
time="2025-07-28T19:42:56.270902000+08:00" level=error msg="CC | 广州移动转香港HKC2[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:42:56.270902000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:43:02.667096200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:02.667096200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:02.682899100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:02.682899100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:02.684418200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:02.685525600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:02.718222600+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:02.719796500+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:04.320777800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:43:04.320777800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:43:04.320777800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:43:04.320777800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:43:13.142291800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:43:17.759965600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:17.759965600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:43:20.263431600+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:43:21.278282100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:43:21.278282100+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:43:24.119462900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:24.123259400+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:32.861313800+08:00" level=error msg="PKM1 | 🇩🇪【欧洲】德国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:43:32.861313800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:43:33.474029700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:33.483740200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:33.828928000+08:00" level=error msg="PKM1 | 🇺🇦【欧洲】乌克兰【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:43:33.828928000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:43:49.653381800+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:43:49.714594700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:50.058282000+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:43:50.137734200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:50.187029900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:50.233189800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:43:57.000621200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:43:57.000621200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:43:57.001167800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:43:57.001167800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:43:59.976985200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:43:59.976985200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:44:00.026759600+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:44:00.070479900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:44:00.090345700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:44:00.516622300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:44:00.516622300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:44:00.695185800+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp **************:443: i/o timeout\ndial tcp ************:443: i/o timeout"
time="2025-07-28T19:44:00.695185800+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:44:05.148623400+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:44:05.148623400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:44:08.111711600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:44:08.111711600+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:44:11.621951400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:44:11.621951400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:44:13.837027300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:44:13.839623000+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:44:13.889653700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:44:24.321219400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:44:24.321219400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:44:24.321219400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:44:24.321219400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:44:38.143627500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> mly1.543412546.xyz:443 error: dial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:44:38.143627500+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:44:42.760564200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:44:42.760564200+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:44:43.316764000+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:44:47.103867400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:44:47.103867400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:45:05.405956200+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:45:20.920570600+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:45:20.920570600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:45:29.670011700+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:45:29.670011700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:45:33.286484600+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💼_github.com/Ruk1ng001_3662346364343063 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:45:33.286989300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:45:33.807606300+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-28T19:45:33.813047100+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> **********:443 error: dial tcp **********:443: i/o timeout"
time="2025-07-28T19:45:57.748900700+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:46:01.449859700+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) mihomo --> ************:443 error: dial tcp ************:443: operation was canceled"
time="2025-07-28T19:46:11.179837900+08:00" level=error msg="PKM1 | 🇧🇬【欧洲】保加利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:46:11.179837900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:46:21.297879500+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:46:37.001484700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:46:37.001484700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:46:37.001484700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:46:37.002000800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:46:40.770191400+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:46:40.770191400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:46:41.207052600+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:46:42.519918200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:46:42.566356700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: sshk02.521pokemon.com:52012 connect error: dial tcp ***********:52012: i/o timeout"
time="2025-07-28T19:46:48.705445400+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:46:57.237456500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> ssr.cnring.shop:443 error: dial tcp [2606:4700:3030::6815:5001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:6001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:7001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:4001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:1001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:2001]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:3001]:443: connectex: The requested address is not valid in its context.\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ************:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout\ndial tcp ***********:443: i/o timeout"
time="2025-07-28T19:46:57.237456500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:47:04.321718100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:47:04.321718100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:47:04.321718100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:47:04.321718100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:47:08.516528300+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:47:18.778824900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:47:23.304746700+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:47:23.950929600+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:29.245139700+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:47:29.246669100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:47:29.293567200+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:47:29.855655300+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.113839100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:30.114347900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:47:33.944249300+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:47:41.944410300+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:47:41.944410300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:47:44.179643800+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:47:44.183841900+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: i/o timeout"
time="2025-07-28T19:47:50.712734300+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:47:50.712734300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:47:53.268424100+08:00" level=warning msg="[TCP] dial 🐟 漏网之鱼 (match Match/) mihomo --> *******:443 error: ssaomen.136470.xyz:59010 connect error: dial tcp ***********:59010: operation was canceled"
time="2025-07-28T19:48:33.996066000+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:48:47.838911000+08:00" level=error msg="PKM1 | 🇷🇺【欧洲】俄罗斯【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:48:47.838911000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:48:47.858571200+08:00" level=error msg="PKM1 | 🇳🇱【欧洲】荷兰【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:48:47.858571200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:48:47.906472000+08:00" level=error msg="PKM1 | 🇺🇦【欧洲】乌克兰【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:48:47.906472000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:48:59.531742700+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:49:18.079949800+08:00" level=error msg="PKM1 | 🇳🇬【非洲】尼日利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:49:18.079949800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:49:54.729453900+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:50:10.808671800+08:00" level=error msg="CC | 深港专线5[倍率:2.5] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:10.808671800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:50:30.099433200+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:50:38.099485900+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:38.099485900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:50:41.451112100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734366538303564 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:41.451112100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:50:44.172500300+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_3439383065326365 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:44.173133100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:50:46.100669100+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:46.100669100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:50:55.888094200+08:00" level=error msg="Free-6 | 🔴_🇺🇸_💻_github.com/Ruk1ng001_3933633032326161 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:50:55.888094200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:51:29.852523200+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:51:42.360278800+08:00" level=error msg="PKM1 | 🇩🇪【欧洲】德国01丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:51:42.360278800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:51:46.119799700+08:00" level=error msg="PKM1 | 🇧🇬【欧洲】保加利亚【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:51:46.119799700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:51:57.001635100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:51:57.001635100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:51:57.002698200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:51:57.002698200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:52:01.684637500+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:52:06.372948800+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:52:09.208177900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:52:14.373365500+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:52:14.373870200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:52:14.874727500+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡03丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:52:14.874727500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:52:19.124220800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": EOF"
time="2025-07-28T19:52:22.806590300+08:00" level=error msg="PKM1 | 🇮🇸【欧洲】冰岛【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:52:22.806590300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:52:23.424701100+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:24.322037100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:52:24.322037100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-07-28T19:52:24.322037100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-28T19:52:24.322037100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-28T19:52:28.113244900+08:00" level=error msg="CT CreaTivity failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:52:28.113244900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:28.751227800+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:52:32.926031300+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:52:32.926031300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:52:39.314501900+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-28T19:52:48.305459400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> bbb.flylink.cyou:443 error: dial tcp [2606:4700:3037::ac43:82d8]:443: connectex: The requested address is not valid in its context.\ndial tcp [2606:4700:3030::6815:970]:443: connectex: The requested address is not valid in its context.\ndial tcp ************:443: i/o timeout\ndial tcp **************:443: i/o timeout"
time="2025-07-28T19:52:48.305459400+08:00" level=error msg="[Provider] 飞链 pull error: Get \"https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7\": EOF"
time="2025-07-28T19:52:53.840549700+08:00" level=error msg="[Provider] free-1 pull error: 410 Gone"
time="2025-07-28T19:53:02.627044300+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:53:32.818631300+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:53:48.411331400+08:00" level=error msg="Free-6 | ❓_🇮🇷_📶_github.com/Ruk1ng001_3639306465656466 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:53:48.411331400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:53:54.248363000+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6637383539383662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:53:54.248363000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:53:58.427686800+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:53:58.427686800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:54:16.630787400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3234623938643265 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:54:16.630787400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:54:27.081852400+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:54:57.518028400+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:55:05.518689300+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:55:05.518689300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:55:49.046699000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6436613836333164 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:55:49.046699000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:55:55.589436900+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:56:04.077990400+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:04.077990400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:08.986843300+08:00" level=error msg="PKM1 | 🇩🇪【欧洲】德国02丨专线【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:08.986843300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:10.192683400+08:00" level=error msg="PKM1 | 🇳🇱【欧洲】荷兰【3x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:10.192683400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:16.204279200+08:00" level=error msg="CC | 上海电信转香港HKC4[M][倍率:1] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:16.204279200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:30.958701900+08:00" level=warning msg="because 🚄 3.0倍率 failed multiple times, active health check"
time="2025-07-28T19:56:38.959189100+08:00" level=error msg="Free-6 | 🔴_🇨🇦_💻_github.com/Ruk1ng001_3833313437643931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:38.959189100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:51.397411700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6637383539383662 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:51.397411700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:56:55.544948100+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3666383561633633 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-28T19:56:55.544948100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-28T19:57:23.671983200+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T19:57:29.096857900+08:00" level=warning msg="because FL 飞链 failed multiple times, active health check"
time="2025-07-28T20:00:22.981068600+08:00" level=warning msg="Mihomo shutting down"
