Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-22T21:25:35.412700500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-22T21:25:35.417804600+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-22T21:25:35.417804600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-22T21:25:35.418310400+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-22T21:25:35.566333400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 19263"
time="2025-07-22T21:25:35.566333400+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-22T21:25:35.766518800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119019"
time="2025-07-22T21:25:35.771771100+08:00" level=info msg="Initial configuration complete, total time: 356ms"
time="2025-07-22T21:25:35.813650000+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-22T21:25:36.458926300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:25:36.458926300+08:00" level=warning msg="[Provider] creativity-2 not updated for a long time, force refresh"
time="2025-07-22T21:25:36.459922700+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-22T21:25:36.460919500+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-22T21:25:36.464646800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-22T21:25:36.464646800+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-22T21:25:36.617245100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-22T21:25:37.734057000+08:00" level=error msg="CT2 CreaTivity-2 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:57040->*************:80: use of closed network connection"
time="2025-07-22T21:25:37.734057000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:37.807691300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-22T21:25:37.808709600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-22T21:25:40.126635600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-22T21:25:40.126635600+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-22T21:25:41.456917700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.456917700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.456917700+08:00" level=error msg="🔗 Landing-HK-Relay failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.456917700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.456917700+08:00" level=error msg="🎯 Webshare-US专线-Direct failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.456917700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.456917700+08:00" level=error msg="🎯 落地节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.456917700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.456917700+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.456917700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.457926500+08:00" level=error msg="Free-2 | 🇯🇵JP_3 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.457926500+08:00" level=error msg="⚙️ 自动选择 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.457926500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.457926500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.461172000+08:00" level=error msg="PKM2 | 套餐到期：2025-08-25 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.461172000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.461172000+08:00" level=error msg="MLY | 套餐到期：2025-08-02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.461172000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.461172000+08:00" level=error msg="MLY | US-美国02 | 1Gbps | 下载0.5× failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.461172000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.466361600+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-22T21:25:41.466361600+08:00" level=error msg="[Provider] creativity-2 pull error: Get \"https://dy-efxrjgadvc.cn-hongkong.fcapp.run/api/v1/client/subscribe?token=e8074efa230eba69a9669fa308da2ca9\": EOF"
time="2025-07-22T21:25:41.476057000+08:00" level=error msg="CC | 广东移动转台湾BGP2[M][Trojan][倍率:0.9] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.476057000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.482627600+08:00" level=error msg="Free-6 | 🟡_🇨🇳:🇭🇰_💻_github.com/Ruk1ng001_3633306337373665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:41.482627600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:41.956986800+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-22T21:25:43.361882400+08:00" level=error msg="Free-6 FreeSub failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:43.361882400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:44.669644800+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:57277(backgroundTaskHost.exe) --> arc.msn.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:25:50.613173900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) 127.0.0.1:57872(clash-verge.exe) --> dy-efxrjgadvc.cn-hongkong.fcapp.run:443 error: dial tcp ************:443: i/o timeout"
time="2025-07-22T21:25:52.564041000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6638373237636334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:52.564041000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:54.451240500+08:00" level=error msg="Free-6 | 🟡_🇳🇱_💻_github.com/Ruk1ng001_3534666130343664 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:25:54.451240500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:25:56.460280300+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-22T21:26:08.401769700+08:00" level=error msg="Free-6 | 🟠_🇻🇳_💻_github.com/Ruk1ng001_6665393064623131 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:26:08.401769700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:12.382742300+08:00" level=warning msg="[TCP] dial Ⓜ️ Microsoft (match RuleSet/microsoft) **********:58593(backgroundTaskHost.exe) --> arc.msn.com:443 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:16.290102400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6665373339653631 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-22T21:26:16.290102400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:16.460640700+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-22T21:26:20.478155500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-22T21:26:20.478155500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-22T21:26:22.339059500+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-22T21:26:23.067024100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431326335616233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:26:23.067024100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:25.612221700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-22T21:26:25.612221700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:31.719871600+08:00" level=error msg="CC | 上海联通转德国DF[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:58957->************:65101: use of closed network connection"
time="2025-07-22T21:26:31.719871600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:34.929465200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6538653635373035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59091->************:25618: use of closed network connection"
time="2025-07-22T21:26:34.929465200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:37.457865200+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:59545(SGDownload.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-22T21:26:37.477831900+08:00" level=warning msg="[TCP] dial 🔗 直连 (match RuleSet/direct) **********:59557(SGTool.exe) --> st.pinyin.sogou.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-22T21:26:42.114551100+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59526(SogouExe.exe) --> ocsp.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:42.817334700+08:00" level=error msg="CC | 广东移动转日本NTT4[倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:26:42.817334700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:46.126531200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59582(svchost.exe) --> crl3.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:47.126071400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59590(SogouExe.exe) --> crl3.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:51.143283200+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59683(WeChat.exe) --> ocsp.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:52.323053700+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59700(SogouExe.exe) --> crl4.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:52.882080900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:52.882080900+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-22T21:26:53.065795400+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:53.225522800+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:53.361618500+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:53.540745900+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:53.762969100+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:54.189381200+08:00" level=warning msg="[TCP] dial ⚡ 深港专线 mihomo --> mly1.543412546.xyz:443 error: cnsg.1ga4cs.ccddn4.icu:65023 connect error: EOF"
time="2025-07-22T21:26:56.152360400+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:59781(WeChat.exe) --> crl3.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:26:57.882365500+08:00" level=error msg="Free-2 | 🇭🇰HK_4 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:26:57.882365500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:26:57.883539100+08:00" level=error msg="FL | 德国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:26:57.883539100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:01.174766800+08:00" level=warning msg="[TCP] dial 🔰 模式选择 (match RuleSet/proxy) **********:60027(WeChat.exe) --> crl4.digicert.com:80 error: cnsg.12a4cv.ccddn4.icu:65520 connect error: dial tcp 21***********:65520: i/o timeout"
time="2025-07-22T21:27:09.194531800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6262643063336639 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:27:09.194531800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:16.055513300+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3631613035633538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:27:16.055513300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:16.461368400+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-22T21:27:34.391650000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:60741->************:3596: use of closed network connection"
time="2025-07-22T21:27:34.391650000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:41.427887900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-22T21:27:41.431116600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-22T21:27:41.786020600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3735626564323361 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:27:41.786020600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:42.736510200+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-22T21:27:44.162237200+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6538653635373035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:52506->************:25618: use of closed network connection"
time="2025-07-22T21:27:44.162237200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:51.307446000+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-22T21:27:56.806301600+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本樱花05丨直连【2x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:27:56.806301600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:27:58.472184300+08:00" level=error msg="Free-2 | 🇨🇦CA_1 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:27:58.472184300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:03.097207400+08:00" level=error msg="CC | 广东移动转日本NTT7[Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:03.097207400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:03.526154800+08:00" level=error msg="MLY | HK-香港 02 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:03.526154800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:17.868493500+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-22T21:28:22.868527000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6433353636663034 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:22.868527000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:22.868527000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3830646534363836 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:22.868527000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:22.868527000+08:00" level=error msg="FL | 英国 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:22.868527000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6636393638656162 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:22.868527000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:22.868527000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:33.352341700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6262643063336639 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:33.352341700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:34.929826400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_61666364616232 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:34.929826400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:38.406820900+08:00" level=error msg="Free-6 | ❓_🇬🇧_💻_github.com/Ruk1ng001_3630356538396166 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:38.406820900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:43.140518800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3661613533323437 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:43.140518800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:49.775742200+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-07-22T21:28:56.124909800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6333323033383337 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:56.124909800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:56.462012000+08:00" level=error msg="[Provider] creativity-1 pull error: Get \"https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797\": context deadline exceeded"
time="2025-07-22T21:28:57.147230400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6431326335616233 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:28:57.147230400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:28:57.626747500+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6662336261393538 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:54346->************:3596: use of closed network connection"
time="2025-07-22T21:28:57.626747500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:29:07.375407600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6538653635373035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:54447->************:25618: use of closed network connection"
time="2025-07-22T21:29:07.375407600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:29:10.835629800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3334353963633135 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:29:10.835629800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:29:13.374248000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6338373137376363 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-22T21:29:13.374248000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-22T21:29:29.776095700+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-07-22T21:29:41.666906200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-22T21:29:41.668466200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-22T21:29:43.152529400+08:00" level=error msg="[Provider] creativity-2 pull error: file must have a `proxies` field"
time="2025-07-22T21:30:29.776439700+08:00" level=error msg="[Provider] cordcloud pull error: context deadline exceeded"
time="2025-07-22T21:30:35.329492400+08:00" level=warning msg="Mihomo shutting down"
