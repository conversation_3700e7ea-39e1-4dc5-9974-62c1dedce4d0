2025-06-22 16:34:06 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-06-22 16:34:06 INFO - [Setup] 初始化资源...
2025-06-22 16:34:06 INFO - [Setup] 初始化完成，继续执行
2025-06-22 16:34:06 INFO - [System] 应用就绪或恢复
2025-06-22 16:34:06 INFO - [Config] 生成运行时配置成功
2025-06-22 16:34:06 INFO - [Config] 开始验证配置
2025-06-22 16:34:06 INFO - [Config] 生成临时配置文件用于验证
2025-06-22 16:34:06 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-06-22 16:34:06 INFO - [Config] 使用内核: verge-mihomo
2025-06-22 16:34:06 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-06-22 16:34:06 INFO - [Config] -------- 验证结果 --------
2025-06-22 16:34:06 INFO - [Config] 验证成功
2025-06-22 16:34:06 INFO - [Config] -------- 验证结束 --------
2025-06-22 16:34:06 INFO - [Config] 配置验证成功
2025-06-22 16:34:06 INFO - [Setup] 清理冗余的Profile文件...
2025-06-22 16:34:06 INFO - Profile 文件清理完成: 总文件数=2, 删除文件数=0, 失败数=0
2025-06-22 16:34:06 INFO - [Setup] 启动时Profile文件清理完成
2025-06-22 16:34:06 INFO - [Service] 开始检查服务是否正在运行
2025-06-22 16:34:06 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-22 16:34:06 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:06 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:06 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-22 16:34:06 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-22 16:34:06 INFO - [Service] 服务正在运行
2025-06-22 16:34:06 INFO - [Core] 服务当前可用或看似可用，尝试通过服务模式启动/重装
2025-06-22 16:34:06 INFO - [Service] 开始检查服务是否需要重装
2025-06-22 16:34:06 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-22 16:34:06 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:06 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:06 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-22 16:34:06 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-22 16:34:06 INFO - 服务版本检测：当前=1.1.0, 要求=1.1.0
2025-06-22 16:34:06 INFO - 服务版本匹配，无需重装
2025-06-22 16:34:06 INFO - 正在尝试通过服务启动核心
2025-06-22 16:34:06 INFO - [Service] 开始检查服务版本 (IPC)
2025-06-22 16:34:06 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:06 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:06 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-06-22 16:34:06 INFO - [Service] 获取到服务版本: 1.1.0
2025-06-22 16:34:06 INFO - 检测到服务版本: 1.1.0, 要求版本: 1.1.0
2025-06-22 16:34:06 INFO - 服务版本匹配
2025-06-22 16:34:06 INFO - [Service] 开始检查服务是否正在运行
2025-06-22 16:34:06 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-22 16:34:06 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:06 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:06 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-22 16:34:06 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-22 16:34:06 INFO - [Service] 服务正在运行
2025-06-22 16:34:06 INFO - 服务已在运行且版本匹配，尝试使用
2025-06-22 16:34:06 INFO - 尝试使用现有服务启动核心 (IPC)
2025-06-22 16:34:06 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:06 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:07 INFO - [Service] IPC请求完成: 命令=StartClash, 成功=true
2025-06-22 16:34:07 INFO - [Service] 服务成功启动核心
2025-06-22 16:34:07 INFO - [Core] 服务模式成功启动核心
2025-06-22 16:34:07 INFO - [Tray] 创建系统托盘...
2025-06-22 16:34:07 INFO - 正在从AppHandle创建系统托盘
2025-06-22 16:34:07 INFO - 系统托盘创建成功
2025-06-22 16:34:07 INFO - [Tray] 系统托盘创建成功
2025-06-22 16:34:07 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-06-22 16:34:07 INFO - [Window] UI准备阶段更新: NotStarted, 耗时: 0ms
2025-06-22 16:34:07 INFO - [Timer] Initializing timer...
2025-06-22 16:34:07 INFO - [Timer] 已注册的定时任务数量: 0
2025-06-22 16:34:07 INFO - [Timer] Timer initialization completed
2025-06-22 16:34:07 INFO - [Setup] 异步设置任务完成，耗时: 1.2456623s
2025-06-22 16:34:07 INFO - [Setup] 应用设置成功完成
2025-06-22 16:34:07 INFO - [Window] 窗口已立即显示
2025-06-22 16:34:07 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-06-22 16:34:07 INFO - [Window] 窗口显示流程完成
2025-06-22 16:34:08 INFO - [Cmd] 快速获取配置列表成功
2025-06-22 16:34:08 INFO - UI加载阶段更新: Loading
2025-06-22 16:34:08 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-22 16:34:08 INFO - UI加载阶段更新: Loading
2025-06-22 16:34:08 INFO - [Window] UI准备阶段更新: Loading, 耗时: 0ms
2025-06-22 16:34:08 INFO - UI加载阶段更新: DomReady
2025-06-22 16:34:08 INFO - [Window] UI准备阶段更新: DomReady, 耗时: 0ms
2025-06-22 16:34:08 INFO - [Service] 开始检查服务是否正在运行
2025-06-22 16:34:08 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-22 16:34:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:08 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-22 16:34:08 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-22 16:34:08 INFO - [Service] 服务正在运行
2025-06-22 16:34:08 INFO - UI加载阶段更新: ResourcesLoaded
2025-06-22 16:34:08 INFO - [Window] UI准备阶段更新: ResourcesLoaded, 耗时: 0ms
2025-06-22 16:34:08 INFO - 前端UI已准备就绪
2025-06-22 16:34:08 INFO - [Window] UI已标记为完全就绪
2025-06-22 16:34:08 INFO - [Window] UI已完全加载就绪
2025-06-22 16:34:12 INFO - [Service] 开始检查服务是否正在运行
2025-06-22 16:34:12 INFO - [Service] 开始检查服务状态 (IPC)
2025-06-22 16:34:12 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:34:12 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:34:12 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-06-22 16:34:12 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-06-22 16:34:12 INFO - [Service] 服务正在运行
2025-06-22 16:38:14 INFO - Tray点击事件: 显示主窗口
2025-06-22 16:38:14 INFO - [Window] 开始智能显示主窗口
2025-06-22 16:38:14 INFO - [Window] 开始激活窗口
2025-06-22 16:38:14 INFO - [Window] 窗口激活成功
2025-06-22 16:38:14 INFO - 窗口显示结果: Shown
2025-06-22 16:39:08 INFO - [Service] 通过服务停止核心 (IPC)
2025-06-22 16:39:08 INFO - [Service] 正在连接服务 (Windows)...
2025-06-22 16:39:08 INFO - [Service] 服务连接成功 (Windows)
2025-06-22 16:39:08 INFO - [Service] IPC请求完成: 命令=StopClash, 成功=true
