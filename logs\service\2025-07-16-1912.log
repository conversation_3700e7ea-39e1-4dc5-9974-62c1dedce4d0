Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-16T19:12:53.556342900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-16T19:12:53.563022900+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-07-16T19:12:53.563022900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-16T19:12:53.563022900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-16T19:12:53.567612300+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7484"
time="2025-07-16T19:12:53.567612300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-16T19:12:53.796806800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 119280"
time="2025-07-16T19:12:53.801441200+08:00" level=info msg="Initial configuration complete, total time: 241ms"
time="2025-07-16T19:12:53.802443600+08:00" level=warning msg="Failed to start Redir UDP Listener: not supported on current platform"
time="2025-07-16T19:12:53.803446000+08:00" level=error msg="Start Redir server error: not supported on current platform"
time="2025-07-16T19:12:53.803446000+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-16T19:12:53.819727800+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-07-16T19:12:54.384910800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-16T19:12:54.539073200+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"
time="2025-07-16T19:12:54.539726600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:12:54.542278200+08:00" level=warning msg="[Provider] 免流云 not updated for a long time, force refresh"
time="2025-07-16T19:12:54.543806200+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-16T19:12:54.546287900+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:12:54.546287900+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-16T19:12:54.554972700+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"
time="2025-07-16T19:12:54.563912400+08:00" level=warning msg="[Provider] free-6 not updated for a long time, force refresh"
time="2025-07-16T19:12:54.593025200+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-16T19:12:56.024465700+08:00" level=error msg="[Sniffer] [*********] [tls] may not have any sent data, Consider adding skip"
time="2025-07-16T19:12:56.528083300+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:12:56.528083300+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-07-16T19:12:56.528083300+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:12:56.528083300+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-16T19:12:56.752175100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-16T19:12:57.085492800+08:00" level=error msg="[Provider] 免流云 pull error: Get \"https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372\": EOF"
time="2025-07-16T19:12:57.882051700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:12:57.961227900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:12:59.543931600+08:00" level=error msg="PKM2 | 🇸🇬【亚洲】新加坡03丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:12:59.543931600+08:00" level=error msg="PKM2 | 建议：感到卡顿请切换到专线节点 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:12:59.543931600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:12:59.543931600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:12:59.593408800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:12:59.593408800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:12:59.593408800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:12:59.593408800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:12:59.838529300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:13:00.665512900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:13:01.120148800+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:13:03.843097800+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本樱花04丨直连【2x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:03.843097800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:03.844606100+08:00" level=error msg="PKM2 | 🇯🇵【亚洲】日本樱花05丨直连【2x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:03.844606100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:04.032671100+08:00" level=warning msg="because CT2 CreaTivity-2 failed multiple times, active health check"
time="2025-07-16T19:13:06.248374700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6234326630376362 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:06.248374700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:09.142635900+08:00" level=error msg="Start TProxy server error: not supported on current platform"
time="2025-07-16T19:13:09.148047800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:13:09.153718100+08:00" level=warning msg="[Provider] 宝可梦-2 not updated for a long time, force refresh"
time="2025-07-16T19:13:09.154739000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:13:09.154739000+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-07-16T19:13:09.200171100+08:00" level=warning msg="[Provider] free-1 not updated for a long time, force refresh"
time="2025-07-16T19:13:10.157811900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": tls: failed to verify certificate: x509: certificate is valid for *.cdn.myqcloud.com, *.bldimg.com, *.geetest.com, *.jsbchina.cn, *.lof3.xyz, *.suyinwealth.com, bldimg.com, geetest.com, jsbchina.cn, lof3.xyz, suyinwealth.com, cdn.myqcloud.com, not 52pokemon.xz61.cn"
time="2025-07-16T19:13:11.143713800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:11.144247600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:13:12.082555000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6366363839343336 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:12.082555000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:12.591014900+08:00" level=error msg="PKM1 | 🇸🇦【中东】沙特阿拉伯【4x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:12.591014900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:13.910249300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:13:14.010618400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:13:14.150834500+08:00" level=error msg="MLY | tg群：https://t.me/mly_cloud failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:14.150834500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:14.163815400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6436613836333164 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:14.164327900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:14.200896600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:13:14.200896600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:13:14.200896600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:13:14.200896600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:13:14.351818500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:13:14.593254600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:13:14.944639500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6562383037633835 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:14.944639500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:15.236878700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:15.236878700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:15.236878700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:15.236878700+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:17.796577100+08:00" level=error msg="PKM1 | 🇯🇵【亚洲】日本樱花04丨直连【2x】 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:17.796577100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:18.501984600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:18.501984600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:19.431247500+08:00" level=error msg="Free-6 | 🟡_🇺🇸_💻_github.com/Ruk1ng001_3739653133393232 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:19.431247500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:19.735223400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context canceled"
time="2025-07-16T19:13:19.736555800+08:00" level=error msg="CC | 深港专线转香港BGP2[M][倍率:2.5] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-16T19:13:19.736555800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:19.736555800+08:00" level=error msg="CC | 江苏联通转香港NTT[M][Trojan][倍率:0.8] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context canceled"
time="2025-07-16T19:13:19.736555800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:26.441824800+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-16T19:13:26.441824800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:27.762692700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6265333162323339 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:27.762692700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:29.200202400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:13:29.685051800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:29.685051800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:29.685051800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:29.685051800+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:13:30.901959900+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6265626639366665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:30.901959900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:36.358565100+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-16T19:13:36.358565100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:41.281581500+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_66316664366562 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:41.281581500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:42.958879600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6366356363353736 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:42.958879600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:49.200445600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:13:49.521573100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6662646565623632 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:49.521573100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:54.062813400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:13:54.741141100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:13:58.701072900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:58.701072900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:13:58.751054700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6633643462393334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:13:58.751054700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:14:01.670290400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_32356635356366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:14:01.670290400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:14:02.747352000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734663364663236 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:14:02.747352000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:14:09.583835500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6433613932316138 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:14:09.583835500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:14:16.992532400+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-16T19:14:16.992532400+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-16T19:14:16.992532400+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-16T19:14:16.992532400+08:00" level=warning msg="because Free-6 FreeSub failed multiple times, active health check"
time="2025-07-16T19:14:49.201187000+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:15:14.230921000+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:15:14.922143700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:16:29.201750500+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:17:14.607051100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:17:55.071937200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:18:01.327126200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:18:08.859228500+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-07-16T19:18:09.693346000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:18:10.685382000+08:00" level=warning msg="because MLY 免流云 failed multiple times, active health check"
time="2025-07-16T19:18:14.146363500+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_3161396363366536 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:14.146363500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:16.066659800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:16.066659800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:19.870558800+08:00" level=error msg="Free-6 | 🟡_🇨🇦_💼_github.com/Ruk1ng001_613035363035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:19.870558800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:23.488355700+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:50010->*************:44123: use of closed network connection"
time="2025-07-16T19:18:23.488355700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:23.523391100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3464316439643035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:23.523391100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:27.985976200+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6265626639366665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:27.985976200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:31.417221600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-16T19:18:31.417221600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:38.860436900+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-07-16T19:18:50.684982100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:50.685485200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:50.874540000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6633643462393334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:50.874540000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:18:54.206170800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734663364663236 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:18:54.206170800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:19:00.176551200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6435623463663838 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:19:00.176551200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:19:00.513087800+08:00" level=error msg="Free-6 | 🟠_🇷🇺_💻_github.com/Ruk1ng001_6433613932316138 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:19:00.513087800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:19:00.955308800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6463303766343065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:19:00.955308800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:19:14.987743900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:19:28.861070800+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-07-16T19:19:29.202595800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:20:58.861744600+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-07-16T19:21:15.680914200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:22:55.251828200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:23:03.731551700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:23:09.344320800+08:00" level=warning msg="because ⚡ 深港专线 failed multiple times, active health check"
time="2025-07-16T19:23:09.629147200+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:23:09.791661000+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:23:14.145350600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6436613836333164 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:14.145350600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:15.840509300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:23:16.107845800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:16.108351900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:22.336215400+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62533->*************:44123: use of closed network connection"
time="2025-07-16T19:23:22.336215400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:27.336462200+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6265626639366665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:27.336462200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:31.125677600+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:62893->*************:4447: use of closed network connection"
time="2025-07-16T19:23:31.125677600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:48.862363100+08:00" level=error msg="[Provider] free-6 pull error: Get \"https://gh-proxy.com/raw.githubusercontent.com/Ruk1ng001/freeSub/main/clash.yaml\": net/http: TLS handshake timeout"
time="2025-07-16T19:23:51.611305000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:51.611305000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:51.710511700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6633643462393334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:51.710511700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:23:55.249419100+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3734663364663236 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:23:55.249419100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:24:49.203078600+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:25:16.187394500+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:27:16.304273100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:27:55.588345000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:28:05.364844100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:28:09.738716600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:28:10.147342200+08:00" level=warning msg="because CT1 CreaTivity-1 failed multiple times, active health check"
time="2025-07-16T19:28:14.143338700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3861333663393638 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:14.143338700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3731316464376235 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:14.143338700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:14.143338700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:15.744107800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:15.744107800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:16.052355500+08:00" level=error msg="[Provider] cordcloud pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": net/http: TLS handshake timeout"
time="2025-07-16T19:28:23.780730300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:52503->*************:44123: use of closed network connection"
time="2025-07-16T19:28:23.780730300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:23.961294700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3464316439643035 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:23.961294700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:24.817589400+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6437313635336463 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:24.817589400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:28.255172000+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6265626639366665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:28.255172000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:32.669848000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:52884->*************:4447: use of closed network connection"
time="2025-07-16T19:28:32.669848000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:38.415124700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3563643661393839 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:38.415124700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:46.053092700+08:00" level=error msg="[Provider] cordcloud pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": net/http: TLS handshake timeout"
time="2025-07-16T19:28:54.980101600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:54.980101600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:28:55.087115000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6633643462393334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:28:55.087115000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:29:02.723193200+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6464653266653165 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:29:02.723193200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:29:03.252067500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6463303766343065 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:29:03.252067500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:29:05.015643900+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3739623035636266 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:29:05.015643900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:29:07.170940800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3430333037396463 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:29:07.170940800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:29:16.638143900+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:29:36.053465700+08:00" level=error msg="[Provider] cordcloud pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": net/http: TLS handshake timeout"
time="2025-07-16T19:30:09.204261800+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:31:06.053849000+08:00" level=error msg="[Provider] cordcloud pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": net/http: TLS handshake timeout"
time="2025-07-16T19:31:18.030392200+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": EOF"
time="2025-07-16T19:32:55.720390500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-07-16T19:33:08.038432300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:33:09.886189400+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:33:09.985716600+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:33:10.146018200+08:00" level=warning msg="because 🇺🇸 美国 failed multiple times, active health check"
time="2025-07-16T19:33:16.209242000+08:00" level=error msg="PKM1 | 🇹🇼【亚洲】台湾家宽02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:33:16.209242000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:16.600967700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:33:16.600967700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:25.416426700+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3733383464316262 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:33:25.416426700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:25.662087800+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6437313635336463 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:33:25.662087800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:25.996906900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": use of closed network connection"
time="2025-07-16T19:33:25.996906900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:28.030935300+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": net/http: TLS handshake timeout"
time="2025-07-16T19:33:29.735514700+08:00" level=error msg="Free-6 | 🟠_🇺🇸_💻_github.com/Ruk1ng001_6265626639366665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:33:29.735514700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:35.575853000+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-16T19:33:35.575853000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:40.661204500+08:00" level=error msg="Free-6 | ❓_🇺🇸_📶_github.com/Ruk1ng001_3838303539393031 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:59260->**************:25888: wsarecv: An existing connection was forcibly closed by the remote host."
time="2025-07-16T19:33:40.661719200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:33:56.054816900+08:00" level=error msg="[Provider] cordcloud pull error: Get \"https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1\": net/http: TLS handshake timeout"
time="2025-07-16T19:34:00.347030600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:34:00.347030600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:34:00.920941600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6633643462393334 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:34:00.920941600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:34:16.708593400+08:00" level=error msg="Free-6 | ❓_🇩🇪_💻_github.com/Ruk1ng001_3332613161656137 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:34:16.708593400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:35:29.205212400+08:00" level=error msg="[Provider] free-1 pull error: Get \"https://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae\": context deadline exceeded"
time="2025-07-16T19:35:38.031472400+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": net/http: TLS handshake timeout"
time="2025-07-16T19:37:48.032137100+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": net/http: TLS handshake timeout"
time="2025-07-16T19:38:05.721144200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": net/http: TLS handshake timeout"
time="2025-07-16T19:38:09.857402800+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:38:10.143383400+08:00" level=warning msg="because Free-1 failed multiple times, active health check"
time="2025-07-16T19:38:10.143890200+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-07-16T19:38:11.707459100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-07-16T19:38:15.949026800+08:00" level=error msg="PKM1 | 🇹🇼【亚洲】台湾家宽02丨直连 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:15.949026800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:16.047697800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6266616564326231 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:16.047697800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:21.554318500+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3932363262633931 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:21.554318500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:23.370594300+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_6264356437346530 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": readLoopPeekFailLocked: read tcp *************:64388->*************:44123: use of closed network connection"
time="2025-07-16T19:38:23.370594300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:23.587338800+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6262643063336639 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:23.587338800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:28.081217500+08:00" level=error msg="CC | 广港专线转香港BGP[M][Trojan][倍率:1.7] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": EOF"
time="2025-07-16T19:38:28.081217500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:30.625679900+08:00" level=error msg="Free-6 | ❓_🇨🇳_📶_github.com/Ruk1ng001_3634396261333964 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp *************:64816->*************:4447: use of closed network connection"
time="2025-07-16T19:38:30.625679900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:49.965044600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_6434393261633962 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:49.965044600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:52.434480000+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_32356635356366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:52.434480000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:52.664440400+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_3838643536306438 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:52.664440400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:38:52.935487600+08:00" level=error msg="Free-6 | github.com/Ruk1ng001_36303839353366 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:38:52.935487600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:39:03.046550700+08:00" level=error msg="Free-6 | 🟡_🇯🇵_💻_github.com/Ruk1ng001_3732356432656665 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-16T19:39:03.046550700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-16T19:39:28.400019000+08:00" level=warning msg="because 🏠 家宽 failed multiple times, active health check"
time="2025-07-16T19:39:36.504543400+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-16T19:39:36.507339300+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-07-16T19:39:58.032476800+08:00" level=error msg="[Provider] 宝可梦-2 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640\": net/http: TLS handshake timeout"
